{"name": "akina", "version": "0.0.1", "private": true, "scripts": {"android:staging": "react-native run-android --variant=stagingdebug", "android:staging-release": "react-native run-android --variant=stagingrelease", "android:dev": "react-native run-android --variant=developmentdebug", "android:dev-release": "react-native run-android --variant=developmentrelease", "android:prod": "react-native run-android --variant=productiondebug", "android:prod-release": "react-native run-android --variant=productionrelease", "android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "type-check": "tsc", "test:report": "jest --collectCoverage --coverageDirectory=\"./coverage\" --ci --reporters=default --reporters=jest-junit --coverage", "pod-install": "cd ios && RCT_NEW_ARCH_ENABLED=1 bundle exec pod install && cd ..", "postinstall": "patch-package"}, "dependencies": {"@backpackapp-io/react-native-toast": "^0.14.0", "@expo/react-native-action-sheet": "^4.0.1", "@native-html/table-plugin": "^5.3.1", "@notifee/react-native": "^9.1.8", "@pusher/pusher-websocket-react-native": "^1.3.1", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/blur": "^4.4.1", "@react-native-community/slider": "^4.5.6", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/analytics": "^21.6.1", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/messaging": "^21.6.1", "@react-native-masked-view/masked-view": "^0.3.0", "@react-native-media-console/reanimated": "^2.2.4", "@react-native-segmented-control/segmented-control": "^2.5.7", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.10", "@react-navigation/native-stack": "^6.9.26", "@react-navigation/stack": "^6.3.21", "@sentry/react-native": "^6.19.0", "@tanstack/react-query": "^5.20.5", "@types/lodash": "^4.17.0", "@types/react-native-vector-icons": "^6.4.18", "fast-text-encoding": "^1.0.6", "i18next": "^23.8.2", "ky": "^1.2.0", "lodash": "^4.17.21", "marked": "^15.0.6", "mathjs": "^13.0.0", "moment": "^2.30.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-i18next": "^14.0.5", "react-native": "0.73.4", "react-native-animated-stopwatch-timer": "^1.3.0", "react-native-autoheight-webview": "^1.6.5", "react-native-background-upload": "^6.6.0", "react-native-biometrics": "^3.0.1", "react-native-compressor": "^1.9.0", "react-native-config": "^1.5.1", "react-native-confirmation-code-field": "^7.4.0", "react-native-create-thumbnail": "^2.1.1", "react-native-date-picker": "^5.0.2", "react-native-dots-pagination": "^0.3.1", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.18.0", "react-native-image-crop-picker": "^0.50.1", "react-native-image-picker": "^7.1.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-markdown-display": "^7.0.2", "react-native-media-console": "^2.2.4", "react-native-mmkv": "^2.12.1", "react-native-modal": "^13.0.1", "react-native-pager-view": "^6.8.1", "react-native-permissions": "^5.4.0", "react-native-popover-view": "^6.1.0", "react-native-reanimated": "3.16.7", "react-native-reanimated-carousel": "^3.5.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "~4.0.0", "react-native-share": "^12.1.0", "react-native-tab-view": "^4.1.1", "react-native-track-player": "^4.1.1", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.0.3", "react-native-video": "^6.16.1", "react-native-video-cache": "^2.7.4", "react-native-webview": "^13.10.2", "react-native-youtube-iframe": "^2.3.0", "rn-material-ui-textfield": "^1.0.9", "rn-tooltip": "^3.0.3", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "^12.1.2", "@types/jest": "^29.4.0", "@types/node": "^18.14.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/parser": "^6.13.1", "babel-jest": "^29.6.3", "babel-plugin-inline-dotenv": "^1.7.0", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-root-import": "^6.6.0", "dotenv": "^16.3.1", "eslint": "^8.19.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "jest": "^29.6.3", "jest-junit": "^16.0.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.3.3"}, "engines": {"node": ">=18"}}