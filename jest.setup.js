// import 'whatwg-fetch';
import 'react-native-gesture-handler/jestSetup';
import '@testing-library/jest-native/extend-expect';

jest.mock('react-native-reanimated', () =>
	// eslint-disable-next-line @typescript-eslint/no-unsafe-return
	require('react-native-reanimated/mock'),
);

// Silence the warning: Animated: `useNativeDriver` is not supported because the native animated module is missing
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

jest.mock('@/services/instance');

jest.mock('@/App', () => ({
  storage: {
	set: jest.fn(),
	getString: jest.fn(() => 'mock-token'), // simulate token returned from storage
  },
}));

// jest.mock('@/utils/utility', () => ({
//   parseServerError: jest.fn(() => 'Parsed server error'),
// }));

jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn().mockImplementation(response => {
	if (response.errors != null) {
	  return response.errors[0];
	}
	return 'Parsed server error';
  }),
}));

jest.mock('@react-native-firebase/app', () => ({
  RNFBNativeEventEmitter: class {},
  // add any exports you need
}));

jest.mock('@react-native-firebase/messaging', () => ({
  onMessage: jest.fn(() => jest.fn()),
  getToken: jest.fn(() => Promise.resolve('mock-token')),
}));

jest.mock('@notifee/react-native', () => {
  return {
	__esModule: true,
	default: {
	  onForegroundEvent: jest.fn(() => jest.fn()),
	  onBackgroundEvent: jest.fn(() => jest.fn()),
	  requestPermission: jest.fn(() => Promise.resolve(true)),
	},
	EventType: {
	  ACTION_PRESS: 'action_press',
	  // add others as needed
	},
  };
});