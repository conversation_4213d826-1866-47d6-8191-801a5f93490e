<manifest xmlns:android="http://schemas.android.com/apk/res/android">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.CAMERA"/>
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.USE_FINGERPRINT" />
  <uses-permission android:name="android.permission.USE_BIOMETRIC" />

  <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
  >
    <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:theme="@style/SplashTheme"
        android:windowSoftInputMode="adjustPan"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="akinaconnectstaging" />
      </intent-filter>
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/maternal-health-motherhood/post/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/entrepreneurship-career-development/post/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/mental-health-wellness/post/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/videos/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/social-connect/post/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/social-connect/user/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/podcast/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/music/" />
        <data android:scheme="https" android:host="web-staging.akinaconnect.com" android:pathPrefix="/ask-akina/" />
      </intent-filter>
    </activity>
  </application>
</manifest>
