buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.0"
        androidxCoreVersion = "1.13.1"
        androidxActivityVersion = "1.9.3"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.5.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath('com.google.gms:google-services:4.4.2')
    }
}

allprojects {
    repositories {
        maven {
            url "$rootDir/../node_modules/react-native-video-cache/android/libs"
        }
    }
}
apply plugin: "com.facebook.react.rootproject"
