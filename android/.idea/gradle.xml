<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <compositeConfiguration>
          <compositeBuild compositeDefinitionSource="SCRIPT">
            <builds>
              <build path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" name="react-native-gradle-plugin">
                <projects>
                  <project path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" />
                </projects>
              </build>
            </builds>
          </compositeBuild>
        </compositeConfiguration>
        <option name="testRunner" value="CHOOSE_PER_TEST" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="#GRADLE_LOCAL_JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/../node_modules/@notifee/react-native/android" />
            <option value="$PROJECT_DIR$/../node_modules/@pusher/pusher-websocket-react-native/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-clipboard/clipboard/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/blur/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/slider/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-cookies/cookies/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/analytics/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/app/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-firebase/messaging/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-masked-view/masked-view/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" />
            <option value="$PROJECT_DIR$/../node_modules/@sentry/react-native/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-background-upload/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-biometrics/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-compressor/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-config/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-create-thumbnail/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-date-picker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-fast-image/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-fs/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-gesture-handler/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-image-crop-picker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-image-picker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-linear-gradient/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-mmkv/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-pager-view/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-permissions/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-reanimated/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-safe-area-context/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-screens/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-share/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-track-player/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-vector-icons/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-video-cache/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-video/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-webview/android" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>