diff --git a/node_modules/react-native-background-upload/android/build.gradle b/node_modules/react-native-background-upload/android/build.gradle
index fa6963f..7a3c908 100755
--- a/node_modules/react-native-background-upload/android/build.gradle
+++ b/node_modules/react-native-background-upload/android/build.gradle
@@ -1,9 +1,9 @@
 buildscript {
     ext {
-        kotlinVersion = '1.6.0'
-        buildToolsVersion = '29.0.2'
-        compileSdkVersion = 29
-        targetSdkVersion = 29
+        kotlinVersion = '1.8.21'
+        buildToolsVersion = '34.0.8'
+        compileSdkVersion = 34
+        targetSdkVersion = 34
         minSdkVersion = 18
     }
     ext.detoxKotlinVersion = ext.kotlinVersion
@@ -20,7 +20,7 @@ buildscript {
 
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'kotlin-android-extensions'
+apply plugin: 'kotlin-parcelize'
 
 def DEFAULT_COMPILE_SDK_VERSION = 28
 def DEFAULT_BUILD_TOOLS_VERSION = "28.0.3"
@@ -64,7 +64,7 @@ dependencies {
 
     implementation "org.jetbrains.kotlin:$_kotlinStdlib:$_kotlinVersion"
 
-    implementation 'net.gotev:uploadservice-okhttp:4.7.0'
+    implementation 'net.gotev:uploadservice-okhttp:4.9.2'
 
     implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.0.0'
 }
