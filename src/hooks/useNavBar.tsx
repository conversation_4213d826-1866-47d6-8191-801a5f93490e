import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useNavigation } from "@react-navigation/native";
import { View, TouchableOpacity, Text } from "react-native";
import CrossIconNav from "@/theme/assets/images/CrossIconNav.png"
import ChevronLeftIcon from "@/theme/assets/images/SocialConnect/ChevronLeftIcon.png";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";

export const useNavBar = (title?: string, hideBottomLine?: boolean, isModal?: boolean) => {
    const navigation = useNavigation();
    const c = colorTokens();
    const { fonts, gutters } = useTheme();

    function headerBackground(): JSX.Element {
        return(
            <View style={[
                gutters.marginHorizontal_0, 
                {
                    width: '100%', height: '100%',
                    backgroundColor: c.background.default.neutrals.default, 
                    borderBottomWidth: hideBottomLine ? 0 : 1, borderBottomColor: c.stoke.default.default
                }
            ]} />
        )       
    }

    function headerLeftButton(): JSX.Element {
        return (
            <AKIcon source={isModal ? CrossIconNav : ChevronLeftIcon} size={24} onPress={navigation.goBack} tintColor={c.content.default.default} styles={[{ marginLeft: 10 }]} />
        );
    }

    function setupNavBar(): void {
        navigation.setOptions({
            title,
            headerStyle: {
               backgroundColor: c.background.default.neutrals.default,
            },
            headerTitleStyle: [
               fonts.SemiBold,
               fonts.fontSizes.body.sm, 
               {color: c.content.default.emphasis}
            ],
            headerTitleAlign: 'center',
            headerTintColor: c.content.default.default,
            headerShown: true, 
            headerShadowVisible: false,
            headerBackground: headerBackground,
            headerLeft: headerLeftButton,
       })
    }

    function updateRightButton(props: any, isVisible: boolean = true): void {
        navigation.setOptions({
            headerRight: () => isVisible ? (
                <TouchableOpacity onPress={props.onPress} disabled={props.disabled} style={[gutters.marginRight_16]}>
                    <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, { color: props.disabled ? c.content.default.disabled : c.content.default.default }]}>Save</Text>
                </TouchableOpacity>
            ) : null
        })
    }

    function updateCustomRightButton(component: JSX.Element): void {
        navigation.setOptions({
            headerRight: () => component
        })
    }

    return { setupNavBar, updateRightButton, updateCustomRightButton };
}