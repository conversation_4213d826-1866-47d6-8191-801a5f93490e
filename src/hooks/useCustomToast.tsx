import {resolveValue, toast, ToastPosition, Toast} from "@backpackapp-io/react-native-toast";
import { useTheme } from "@/theme";
import { View, Text } from "react-native";
import { Dimensions } from "react-native";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import CrossCircleFilledIcon from "@/theme/assets/images/SocialConnect/CrossCircleFilledIcon.png";
import CheckCircleFilledIcon from "@/theme/assets/images/SocialConnect/CheckCircleFilledIcon.png";

export const useCustomToast = () => {

    const {
        gutters,
        fonts,
        borders,
    } = useTheme();

    const c = colorTokens();
    
    const { width: screenWidth } = Dimensions.get('window');

    function getCustomToast(toast: Toast, isSuccess: boolean) {

        const toastIcon = isSuccess ? CheckCircleFilledIcon : CrossCircleFilledIcon;
        return (
            <View style={[ borders.rounded_8, gutters.paddingVertical_12 ,{ flexDirection: 'row', alignItems: 'center', width: screenWidth - 32, backgroundColor: c.background.bold.neutral.default }]}>
                <AKIcon source={toastIcon} styles={[gutters.marginRight_12, gutters.marginLeft_12]} size={24} />
                <Text style={[fonts.body, fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, { color: c.content.onBold.default.default }]}>{resolveValue(toast.message, toast)}</Text>
            </View>
        )
    }

    function showToast(message: string, isSuccess: boolean = true) {
        toast(message, {
            customToast: toast => getCustomToast(toast, isSuccess),
            position: ToastPosition.BOTTOM,
            id: 'bookmark',
            duration: 2000,
            width: screenWidth-32
            
        });
    }

    return showToast;
};
