import { useTranslation } from "react-i18next";
import { useMutation } from "@tanstack/react-query";
import tempAuth from "@/services/users/tempAuth";
import { useNavigation } from "@react-navigation/native";
import { Alert } from "react-native";

export const usePremiumMemberPopup = () => {

    const navigation = useNavigation();
    const { t } = useTranslation(['home', 'sideTabs']);

    const tempAuthMutation = useMutation({
        mutationFn: () => tempAuth(),
        onSuccess: (response) => {
            navigation.navigate('AKWebView', {source: `${response.web_url}?paymentKey=${response.key}`})
        },
        onError: (error) => {
            Alert.alert("Error!", error.message)
        }
    })

    function showPremiumMemberPopup() {
        let options = [{text: t("home:Upgrade"), onPress: async () => {
            tempAuthMutation.mutate()
        }}, {text: t("home:Cancel"), onPress: async () => {
            
        }}]
        Alert.alert(t("home:PremiumUser"), t("home:PremiumUserDesc"), options)
    }

    return showPremiumMemberPopup;
};
