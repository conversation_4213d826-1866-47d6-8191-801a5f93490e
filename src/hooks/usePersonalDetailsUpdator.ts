import { useState } from "react";
import { Alert } from "react-native";
import { useMutation } from "@tanstack/react-query";
import updateProfile from "@/services/users/updateProfile";
import { useNavigation } from "@react-navigation/native";

export const usePersonalDetailsUpdator = (successCallback?: () => void) => {

    const navigation = useNavigation();
    const [isLoading, setIsLoading] = useState(false);

    const updateMutation = useMutation(
        {
            mutationFn: (data: any) => updateProfile(data),
            onSuccess: (response) => {
                setIsLoading(false)
                if (successCallback != null) {
                    successCallback()
                }
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );

    function onUpdateProfile(data: any): void {
        data['_method'] = 'PUT'
        setIsLoading(true)
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
            formData.append(key, data[key]);
        });
        updateMutation.mutate(formData)    
    }

    return {isLoading, onUpdateProfile};
}