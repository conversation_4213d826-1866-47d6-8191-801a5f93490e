import { storage } from "@/App";
import { getFcmToken } from "@/integrations/fireabase/messaging";
import storeFcmToken from "@/services/users/storeFcmToken";
import { FCM_TOKEN_STORAGE_KEY } from "@/utils/constants";
import { useMutation } from "@tanstack/react-query";

export const useHandleTokenUpdate = () => {
    async function handleTokenUpdate() {
		try {
			const token = await getFcmToken();
			console.log("FCM::", token)
		  	const storedToken = await storage.getString(FCM_TOKEN_STORAGE_KEY);
			if (!!token && token !== storedToken) {
				storeFcmMutation.mutate({
					device_token: token,
					device_type: 'ios'
				});
			} else {
				console.log('Same token');
			}
		} catch (error) {
		  	console.log(error);
		}
	}

	const storeFcmMutation = useMutation(
		{
			mutationFn: (data: any) => storeFcmToken(data),
			onSuccess: (response) => {
				console.log(response);
			},
			onError: (error) => {
				console.log(error.message)
			}
		},
	);
    return handleTokenUpdate

}