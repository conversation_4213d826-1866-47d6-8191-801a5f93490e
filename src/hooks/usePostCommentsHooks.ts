import { Alert } from "react-native";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { useState } from "react";

import getPostComments from "@/services/socialConnect/getPostComments";
import addPostComment from "@/services/socialConnect/addPostComment";
import addPostCommentReply from "@/services/socialConnect/addPostCommentReply";
import deletePostComment from "@/services/socialConnect/deletePostComment";
import deletePostCommentReply from "@/services/socialConnect/deletePostCommentReply";
import editPostComment from "@/services/socialConnect/editPostComment";
import editPostCommentReply from "@/services/socialConnect/editPostCommentReply";
import { getProfile } from "@/services/users";
import { usePremiumMemberPopup } from "@/hooks/usePremiumMemberPopup";

import { socialConnectPostCommentSchema } from "@/types/schemas/socialConnectPostComment";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { Comment } from "@/types/models/comment";
import { UserTypes } from "@/utils/constants";
import { ReportingContent } from "@/components/molecules/ReportPostBottomSheet/ReportPostBottomSheet";
import basicProfile from "@/services/users/basicProfile";

type EditMutationData = {
    data: {
        reply?: string;
        comment?: string;
    };
    commentId?: number;
    replyId?: number;
};

type DeleteMutationData = {
    commentId: number;
    replyId?: number;
}

type AddMutationData = {
    comment: string;
}

type ReplyMutationData = {
    commentId?: number;
    payload: {
        reply: string;
    }
}

type CommentActionsReturnType = {
    onSendPress: () => void;
    onEditComment: (item: Comment) => void;
    onDeleteComment: (item: Comment) => void;
    onPressCross: () => void;
    onPressComment: (value: any) => void;
    isSending: boolean;
    commentText: string;
    setCommentText: (text: string) => void;
    replyingComment: Comment | null;
    setReplyingComment: (comment: Comment) => void
    editingComment: Comment | null;
    selectedComment: Comment | null;
    commentCoordinates: { x: number; y: number } | null;
    onReportComment: (commentID: number, replyID?: number, userID?: number) => void;
    reportingContent: ReportingContent;
    setReportingContent: (reportingContent: ReportingContent) => void;
};

/**
 * Hook for handling post comment mutations
 */
export const usePostCommentsMutations = (postId: number, isPost?: boolean) => {
    const queryClient = useQueryClient();

    function updateCachedData(response: z.infer<typeof socialConnectPostCommentSchema>) {
        if (isPost) {
            queryClient.setQueryData([`social-connect-posts-${response.post.id}`], () => {
                return response.post;
            });
        } else {
            queryClient.setQueryData([`social-connect-posts-${postId}`], (cacheData: any) => {
                if (cacheData != null) {
                    return {
                        ...cacheData,
                        comments: response.post.comments,
                        comments_count: response.post.comments_count
                    };
                } else {
                    return {
                        ...response.post
                    }
                }
                
            });
        }
        queryClient.setQueryData([`social-connect-post-${response.post.id}`], () => {
            return response.post;
        });
        updateCachedDataOnListing(response);
    }

    function updateCachedDataOnListing(response: z.infer<typeof socialConnectPostCommentSchema>) {
        queryClient.setQueriesData(
            {
                predicate: (query) => ['social-connect-posts',`social-connect-user-posts-${response.post.user?.id}` ,'social-connect-posts-following'].includes(query?.queryKey[0]),
            }, 
            (cacheData: any) => {
                if (cacheData != null && cacheData?.pages != null) {
                    let pages = cacheData?.pages?.map((page: any) => {
                        let pageData = page?.data?.map((post: z.infer<typeof socialConnectPostSchema>) => {
                            if (post.id === response.post.id) {
                                return response.post;
                            } else {
                                return post;
                            }
                        });
                        return {
                            ...page,
                            data: pageData,
                        };
                    });
                    return {
                        ...cacheData,
                        pages: pages,
                    };
                }
            }
        );
    }

    const addReplyMutation = useMutation({
        mutationFn: (data: ReplyMutationData) => 
            addPostCommentReply(postId, data.commentId, data.payload),
        onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
            updateCachedData(response);
        },
        onError: (error: any) => {
            Alert.alert("Error!", error.message);
        }
    });

    const addCommentMutation = useMutation({
        mutationFn: (data: AddMutationData) => 
            addPostComment(postId, data),
        onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
            updateCachedData(response);
        },
        onError: (error: any) => {
            Alert.alert("Error!", error.message);
        }
    });

    const deleteCommentMutation = useMutation({
        mutationFn: (data: DeleteMutationData) => {
            if (!!data.replyId) {
                return deletePostCommentReply(postId, data.commentId, data.replyId);
            } else {
                return deletePostComment(postId, data.commentId);
            }
        },
        onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
            updateCachedData(response);
        },
        onError: (error: any) => {
            Alert.alert("Error!", error.message);
        }
    });

    const editCommentMutation = useMutation({
        mutationFn: (data: EditMutationData) => {
            if (!!data.replyId) {
                return editPostCommentReply(postId, data.commentId, data.replyId, data.data);
            } else {
                return editPostComment(postId, data.commentId, data.data);
            }
        },
        onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
            updateCachedData(response);
        },
        onError: (error: any) => {
            Alert.alert("Error!", error.message);
        }
    });

    return {
        addReplyMutation,
        addCommentMutation,
        deleteCommentMutation,
        editCommentMutation
    };
};

/**
 * Hook for fetching post comments
 */
export const usePostComments = (postId: number) => {
    const { data, isLoading } = useQuery({
        queryKey: [`social-connect-posts-${postId}`],
        queryFn: () => getPostComments(postId),
        refetchOnWindowFocus: true
    });

    return {
        data,
        isLoading
    };
};

/**
 * Hook for handling comment actions
 */
export const useCommentActions = (
    postId: number, 
    isPost?: boolean, 
    setIsBlurViewVisible?: (visible: boolean) => void
): CommentActionsReturnType => {
    const { addReplyMutation, addCommentMutation, deleteCommentMutation, editCommentMutation } = usePostCommentsMutations(postId);
    const showPremiumMemberPopup = usePremiumMemberPopup();
    
    const [commentText, setCommentText] = useState('');
    const [isSending, setIsSending] = useState(false);
    const [commentCoordinates, setCommentCoordinates] = useState(null);
    const [replyingComment, setReplyingComment] = useState<Comment | null>(null);
    const [editingComment, setEditingComment] = useState<Comment | null>(null);
    const [selectedComment, setSelectedComment] = useState<Comment | null>(null);
    const [isReportModalVisible, setIsReportModalVisible] = useState(false);
    const [reportingContent, setReportingContent] = useState<ReportingContent>(null);

    const { data: profileData } = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile
    });

    function clearTempComments() {
        setReplyingComment(null);
        setEditingComment(null);
    }

    function onSendPress(): void {
        if (profileData?.user.user_type === UserTypes.FREE) {
            showPremiumMemberPopup();
            return;
        }
        
        setIsSending(true);
        
        if (editingComment) {
            onEditing();
        } else if (replyingComment) {
            onCommentReply();
        } else {
            onCommentSend();
        }
    }

    function onEditing(): void {
        const bodyKey = !!editingComment?.replyId ? 'reply' : 'comment';
        let data: EditMutationData = {
            data: {
                [bodyKey]: commentText
            },
            commentId: editingComment?.id,
            replyId: editingComment?.replyId,
        };
        
        setCommentText('');
        editCommentMutation.mutate(data, {
            onSuccess: () => {
                setIsSending(false);
                clearTempComments();
            },
            onError: () => {
                setIsSending(false);
                clearTempComments();
            }
        });
    }

    function onCommentSend(): void {
        let data: AddMutationData = {
            comment: commentText
        };
        
        addCommentMutation.mutate(data, {
            onSuccess: () => {
                setCommentText('');
                setIsSending(false);
            },
            onError: () => {
                setIsSending(false);
            }
        });
    }

    function onCommentReply(): void {
        let data: ReplyMutationData = {
            commentId: replyingComment?.id,
            payload: {
                reply: commentText
            }
        };
        
        addReplyMutation.mutate(data, {
            onSuccess: () => {
                setCommentText('');
                setIsSending(false);
                clearTempComments();
            },
            onError: () => {
                setIsSending(false);
                clearTempComments();
            }
        });
    }

    function onEditComment(item: Comment): void {
        setCommentText(item.comment);
        setEditingComment(item);
    }

    function onDeleteComment(item: Comment): void {
        let data: DeleteMutationData = {
            commentId: item.id,
            replyId: item.replyId,
        };
        
        deleteCommentMutation.mutate(data, {
            onSuccess: () => {
                setIsSending(false);
            },
            onError: () => {
                setIsSending(false);
            }
        });
    }

    function onPressCross(): void {
        setReplyingComment(null);
        setEditingComment(null);
        setCommentText('')
    }

    function onPressComment(value: any) {
        setCommentCoordinates({
            x: value.xPos,
            y: value.yPos,
        });
        setSelectedComment(value.comment);
        if (!!setIsBlurViewVisible) {
            setIsBlurViewVisible(!!value.yPos);
        }
    }

    function onReportComment(commentID: number, replyID?: number, userID?: number) {
        setTimeout(() => {
            setReportingContent({
                postID: postId,
                commentID,
                replyID: replyID,
                userID: userID,
            });
        }, 650)
    }

    return {
        onSendPress,
        onEditComment,
        onDeleteComment,
        onPressCross,
        onPressComment,
        onReportComment,
        reportingContent,
        setReportingContent,
        isSending,
        commentText,
        setCommentText,
        replyingComment,
        setReplyingComment,
        editingComment,
        selectedComment,
        commentCoordinates
    };
};