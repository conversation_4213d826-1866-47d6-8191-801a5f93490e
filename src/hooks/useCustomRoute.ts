import { useRoute, RouteProp } from "@react-navigation/native";

type ParamStructure = {
    [key: string]: {
        outerKey?: string;
        innerKey?: string;
    };
};

const paramsStructure: ParamStructure = {
    VideoDetail: {},
    BlogDetail: {
        outerKey: 'moduleTitle',
    },
    NewsDetail: {
        outerKey: "sourceName",
        innerKey: "news_source_id",
    },
    MusicDetail: {},
    PodcastDetail: {},
    Chat: {}
};

type RouteParams = {
    id?: string;
    moduleId?: string;
    name?: string;
    [key: string]: any;
};

export const useCustomRoute = () => {
    const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();

    if (route.params?.item) {
        return route;
    }

    const structure = paramsStructure[route.name] || {};
    const { id, moduleId, name, ...rest } = route.params || {};

    route.params = {
        ...route.params,
        ...(structure.outerKey && {
            [structure.outerKey]: name,
        }),
        item: {
            id,
            module_id: moduleId,
            ...(structure.innerKey && {
                [structure.innerKey]: rest[structure.innerKey],
            }),
        },
    };

    return route;
};
