import basicProfile from "@/services/users/basicProfile";
import getReferralCode from "@/services/users/getReferralCode";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Alert, Platform } from "react-native";
import Share from 'react-native-share';

export const useReferralLink = () => {

    const [isLoading, setIsLoading] = useState(false);

    const { data: profileData, refetch } = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile,
    });

    const referralCodeMutation = useMutation({
        mutationFn: () => getReferralCode(),
        onSuccess: (response) => {
            refetch();
            setIsLoading(false)
            setTimeout(() => {
                generateReferralLink(response.referral_code);
            }, 200);
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert("Error!", error.message)
        }
    })

    async function openShareController(shareUrl: string, code: string) {
        const message = `${profileData?.user?.first_name} ${profileData?.user?.last_name} has invited you to join Akina!\nHere is the referral code: ${code}`;
        let shareOptions;
        if (Platform.OS == 'ios') {
            shareOptions = {
                activityItemSources: [
                    {
                        placeholderItem: { type: 'url', content: `${shareUrl}` },
                        item: {
                            default: {
                                type: 'text',
                                content: `${message}\n${shareUrl}`,
                            },
                            'com.burbn.instagram.shareextension': {
                                type: 'url',
                                content: `${shareUrl}`,
                            },
                        },
                    },
                ],
            };
        } else {
            shareOptions = {
                message: `${message}\n${shareUrl}`,
                url: shareUrl,
            };
        }
        try {
            await Share.open(shareOptions);
        } catch (error) {
            console.log('Sharing error:', error.message || error);
        }
    }

    function generateReferralLink(code: string) {
        const url = `${process.env.WEB_BASE_URL}register?ref=${code}`;
        openShareController(url, code);
    }

    function shareReferralLink() {
        if (!profileData?.share_able_referral_code) {
            setIsLoading(true)
            referralCodeMutation.mutate()
        } else {
            generateReferralLink(profileData?.share_able_referral_code)
        }
    }

    function getReferralLink() {
        return `${process.env.WEB_BASE_URL}register?ref=${profileData?.share_able_referral_code}`;
    }

    return {
        shareReferralLink,
        getReferralLink,
        isLoading
    };
}