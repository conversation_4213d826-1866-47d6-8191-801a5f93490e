import { queryClient, storage } from '@/App';
import { revokeFcmToken } from '@/integrations/fireabase/messaging';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import ky from 'ky';
import { NavigationRef } from '@/navigators/NavigationRef';

const prefixUrl = `${process.env.API_URL ? process.env.API_URL : ''}`;
let headers: any = {
	Accept: 'application/json',
	platform: 'mobile'
}
let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)


if (token != null) {
	headers = {
		Accept: 'application/json',
		Authorization: 'Bearer ' + token,
		platform: 'mobile'
	}
}

console.log("TOKEN::", token)

export const secureInstance = (authToken?: string) => instance.extend({
	hooks: {
		beforeRequest: [
			request => {
				request.headers.set('Authorization', 'Bearer ' + authToken);
			}
		],
		afterResponse: [
		async (_request, options, response) => {
			if (response.status === 401) {
				let keys = storage.getAllKeys()
				keys.map(key => {
					if (key != AUTH_STORRAGE_KEYS.USERID) {
						storage.delete(key)
					}
				} )
				await queryClient.cancelQueries()
				queryClient.removeQueries()
				console.log("Request", _request)
				revokeFcmToken()
				if(!(NavigationRef.current?.getCurrentRoute()?.name == 'NewLogin')) {
					NavigationRef.current?.reset({
						index: 0,
						routes: [{name: 'NewLogin'}]
					})
				}
				throw new Error('User session has been expired!')
			}
			return response
		},
		],
	}
});
console.log("Prefix::", prefixUrl)

export const instance = ky.extend({
	throwHttpErrors: false,
	prefixUrl,
	headers: headers,
	timeout: 100000
});

