import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { registerSchema } from '@/types/schemas/register';
import { updateNotificationResponseSchema } from '@/types/schemas/updateNotificationPreference';
import { updateProfileSchema } from '@/types/schemas/updateProfile';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (userId?: number,data?: any) => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    console.log("url::", `users/${userId}/notification-preferences`)
    console.log("Data::", data)
	const response: any = await secureInstance(token).post(`users/${userId}/notification-preferences`,{method: 'post', json: data}).json()
    console.log("Response:::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return updateNotificationResponseSchema.parse(response);
};
