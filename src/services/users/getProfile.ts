import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";


export default async () => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).get('get_profile', {method: 'get'}).json()
    return response as GetProfileResponse
}