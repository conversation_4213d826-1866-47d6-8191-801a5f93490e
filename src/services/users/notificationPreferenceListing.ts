import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { notificationResponseSchema } from "@/types/schemas/notificationPreference";


export default async (userId?: number) => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    console.log("User Id:::", userId)
	const response: any = await secureInstance(token).get(`users/${userId}/notification-preferences`, {method: 'get'}).json()
    console.log("Response:::", response)
    return notificationResponseSchema.parse(response)
}