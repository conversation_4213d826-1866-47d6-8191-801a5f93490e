import { GetReferralCodeResponse } from "@/types/models";
import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";


export default async () => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).post('referral-codes', {method: 'post'}).json();
    return response as GetReferralCodeResponse;
}