import updateProfile from '../updateProfile';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as updateProfileModule from '@/types/schemas/updateProfile';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

describe('updateProfile service', () => {
  const mockToken = 'test-token';
  const mockData = {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>'
  };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on successful update', async () => {
    const mockResponse = {
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: 123,
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        email: '<EMAIL>'
      }
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    jest.spyOn(updateProfileModule.updateProfileSchema, 'parse').mockReturnValue(mockResponse);

    const result = await updateProfile(mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(postMock).toHaveBeenCalledWith('update_profile', { method: 'post', body: mockData });
    expect(updateProfileModule.updateProfileSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Email already exists']
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    await expect(updateProfile(mockData)).rejects.toThrow('Email already exists');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      success: true,
      message: 123, // Invalid type for message
      user: {
        id: 'not-a-number' // Invalid type for id
      }
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    jest.spyOn(updateProfileModule.updateProfileSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(updateProfile(mockData)).rejects.toThrow('Schema validation failed');
  });
});