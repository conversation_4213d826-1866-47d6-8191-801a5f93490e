import userLikes from '../userLikes';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as userLikesModule from '@/types/schemas/userLikes';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

describe('userLikes service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on successful request', async () => {
    const mockResponse = {
      success: true,
      likes: [
        { id: 1, title: 'Post 1' },
        { id: 2, title: 'Post 2' }
      ]
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    jest.spyOn(userLikesModule.userLikesResponseSchema, 'parse').mockReturnValue(mockResponse);

    const result = await userLikes();

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('user-likes', { method: 'get' });
    expect(userLikesModule.userLikesResponseSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Unauthorized access']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    await expect(userLikes()).rejects.toThrow('Unauthorized access');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      success: true,
      likes: 'not an array' // Invalid type for likes
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    jest.spyOn(userLikesModule.userLikesResponseSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(userLikes()).rejects.toThrow('Schema validation failed');
  });
});