import fetchOne from '../fetchOne';
import { instance } from '@/services/instance';
import * as userModule from '@/types/schemas/user';

// Mock the instance
const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('fetchOne service', () => {
  const userId = 123;

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return user data on successful request', async () => {
    const mockResponse = {
      id: userId,
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>'
      // Add other fields as needed based on your user schema
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedInstance.get = getMock;

    jest.spyOn(userModule.userSchema, 'parse').mockReturnValue(mockResponse);

    const result = await fetchOne(userId);

    expect(getMock).toHaveBeenCalledWith(`users/${userId}`);
    expect(userModule.userSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      id: userId,
      // Missing required fields
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse)
    });

    mockedInstance.get = getMock;

    jest.spyOn(userModule.userSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(fetchOne(userId)).rejects.toThrow('Schema validation failed');
  });

  it('should throw error if API request fails', async () => {
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockRejectedValue(new Error('Network error'))
    });

    mockedInstance.get = getMock;

    await expect(fetchOne(userId)).rejects.toThrow('Network error');
  });
});