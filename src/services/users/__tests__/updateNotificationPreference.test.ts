import updateNotificationPreference from '../updateNotificationPreference';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as updateNotificationModule from '@/types/schemas/updateNotificationPreference';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(),
}));

describe('updateNotificationPreference service', () => {
  const mockToken = 'test-token';
  const userId = 123;
  const mockData = {
    preferences: [
      { type: 'email', enabled: true },
      { type: 'push', enabled: false }
    ]
  };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should update notification preferences on successful request', async () => {
    const mockResponse = {
      success: true,
      message: 'Preferences updated successfully'
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    jest.spyOn(updateNotificationModule.updateNotificationResponseSchema, 'parse').mockReturnValue(mockResponse);

    const result = await updateNotificationPreference(userId, mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(postMock).toHaveBeenCalledWith(`users/${userId}/notification-preferences`, {
      method: 'post',
      json: mockData
    });
    expect(updateNotificationModule.updateNotificationResponseSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Invalid preferences format']
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    (parseServerError as jest.Mock).mockReturnValue('Invalid preferences format');

    await expect(updateNotificationPreference(userId, mockData)).rejects.toThrow('Invalid preferences format');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      success: 'not a boolean' // Invalid type for success
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    jest.spyOn(updateNotificationModule.updateNotificationResponseSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(updateNotificationPreference(userId, mockData)).rejects.toThrow('Schema validation failed');
  });

  it('should log the URL and data being sent', async () => {
    const mockResponse = {
      success: true
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    jest.spyOn(console, 'log');
    jest.spyOn(updateNotificationModule.updateNotificationResponseSchema, 'parse').mockReturnValue(mockResponse);

    await updateNotificationPreference(userId, mockData);

    expect(console.log).toHaveBeenCalledWith("url::", `users/${userId}/notification-preferences`);
    expect(console.log).toHaveBeenCalledWith("Data::", mockData);
    expect(console.log).toHaveBeenCalledWith("Response:::", mockResponse);
  });
});