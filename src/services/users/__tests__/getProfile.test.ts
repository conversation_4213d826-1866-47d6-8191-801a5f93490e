import getProfile from '../getProfile';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { GetProfileResponse } from '@/types/models';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

describe('getProfile service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return profile data on successful request', async () => {
    const mockResponse: GetProfileResponse = {
      id: 123,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      // Add other fields as needed based on your GetProfileResponse type
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    const result = await getProfile();

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('get_profile', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should pass through any errors from the API', async () => {
    const errorResponse = new Error('API error');
    
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockRejectedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    await expect(getProfile()).rejects.toThrow('API error');
  });
});