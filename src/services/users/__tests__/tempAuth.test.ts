import tempAuth from '../tempAuth';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as tempAuthModule from '@/types/schemas/tempAuthSchema';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn()
  },
}));

jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn()
}));

describe('tempAuth service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return temporary auth key on successful request', async () => {
    const mockResponse = {
      key: 'temp-auth-key-123',
      expires_at: '2023-12-31T23:59:59Z'
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    jest.spyOn(tempAuthModule.tempAuthSchema, 'parse').mockReturnValue(mockResponse);

    const result = await tempAuth();

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('temporary-auth/generate-key', { method: 'get' });
    expect(tempAuthModule.tempAuthSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error when response contains errors', async () => {
    const mockResponse = {
      errors: { message: 'Unauthorized' }
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    (parseServerError as jest.Mock).mockReturnValue('Unauthorized');

    await expect(tempAuth()).rejects.toThrow('Unauthorized');
  });
});