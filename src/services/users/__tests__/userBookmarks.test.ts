import userBookmarks from '../userBookmarks';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as userBookmarksModule from '@/types/schemas/userBookmarks';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(),
}));

describe('userBookmarks service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return user bookmarks on successful request', async () => {
    const mockResponse = {
      bookmarks: [
        { id: 1, title: 'Bookmark 1', url: 'https://example.com/1' },
        { id: 2, title: 'Bookmark 2', url: 'https://example.com/2' }
      ]
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    jest.spyOn(userBookmarksModule.userBookmarksResponseSchema, 'parse').mockReturnValue(mockResponse);

    const result = await userBookmarks();

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('user-bookmarks', { method: 'get' });
    expect(userBookmarksModule.userBookmarksResponseSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Unauthorized access']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    (parseServerError as jest.Mock).mockReturnValue('Unauthorized access');

    await expect(userBookmarks()).rejects.toThrow('Unauthorized access');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      bookmarks: 'not an array' // Invalid type for bookmarks
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    jest.spyOn(userBookmarksModule.userBookmarksResponseSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(userBookmarks()).rejects.toThrow('Schema validation failed');
  });
});