import notificationPreferenceListing from '../notificationPreferenceListing';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as notificationModule from '@/types/schemas/notificationPreference';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

describe('notificationPreferenceListing service', () => {
  const mockToken = 'test-token';
  const userId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return notification preferences on successful request', async () => {
    const mockResponse = {
      // Add mock data based on your notification schema
      preferences: [
        { id: 1, type: 'email', enabled: true },
        { id: 2, type: 'push', enabled: false }
      ]
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    } as any);

    jest.spyOn(notificationModule.notificationResponseSchema, 'parse').mockReturnValue(mockResponse);

    const result = await notificationPreferenceListing(userId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`users/${userId}/notification-preferences`, { method: 'get' });
    expect(notificationModule.notificationResponseSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });
});