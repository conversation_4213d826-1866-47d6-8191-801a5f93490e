import storeFcmToken from '../storeFcmToken';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS, FCM_TOKEN_STORAGE_KEY } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

// Mock the secureInstance
const mockedSecureInstance = secureInstance as jest.Mocked<typeof secureInstance>;

jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
    set: jest.fn()
  },
}));

jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn()
}));

describe('storeFcmToken service', () => {
  const mockToken = 'test-token';
  const mockData = {
    device_token: 'fcm-token-123',
    device_type: 'android'
  };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should store FCM token on successful request', async () => {
    const mockResponse = {
      success: true
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    await storeFcmToken(mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(postMock).toHaveBeenCalledWith('push-notifications/register-device', {
      method: 'post',
      json: mockData
    });
    expect(storage.set).toHaveBeenCalledWith(FCM_TOKEN_STORAGE_KEY, mockData.device_token);
  });

  it('should throw error when response is not successful', async () => {
    const mockResponse = {
      success: false,
      errors: { message: 'Invalid token' }
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    } as any);

    (parseServerError as jest.Mock).mockReturnValue('Invalid token');

    await expect(storeFcmToken(mockData)).rejects.toThrow('Invalid token');
    expect(storage.set).not.toHaveBeenCalled();
  });
});