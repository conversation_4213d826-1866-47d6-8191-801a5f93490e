import profileOptions from '../profileOptions';
import { instance } from '@/services/instance';

// Mock the instance
const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('profileOptions service', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return profile options on successful request', async () => {
    const mockResponse = {
      // Add mock data based on your profile options structure
      options: {
        genders: ['Male', 'Female', 'Other'],
        countries: ['US', 'UK', 'Canada']
      }
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedInstance.get = getMock;

    const result = await profileOptions();

    expect(getMock).toHaveBeenCalledWith('profile_options', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should propagate errors from the API', async () => {
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockRejectedValue(new Error('Network error'))
    });

    mockedInstance.get = getMock;

    await expect(profileOptions()).rejects.toThrow('Network error');
  });
});