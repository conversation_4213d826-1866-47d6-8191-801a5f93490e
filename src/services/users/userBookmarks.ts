import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { userBookmarksResponseSchema } from "@/types/schemas/userBookmarks";


export default async () => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).get('user-bookmarks', {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return userBookmarksResponseSchema.parse(response);
}