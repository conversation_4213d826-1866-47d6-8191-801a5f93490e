import { GetProfileResponse } from "@/types/models";
import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { tempAuthSchema } from "@/types/schemas/tempAuthSchema";
import { parseServerError } from "@/utils/utility";


export default async () => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get('temporary-auth/generate-key', {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return tempAuthSchema.parse(response)
}