import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { basicSchema } from '@/types/schemas/basic';
import { registerSchema } from '@/types/schemas/register';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (data: any) => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).post('reset-password',{json: data, method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    console.log("Response::", response)
	return basicSchema.parse(response);
};
