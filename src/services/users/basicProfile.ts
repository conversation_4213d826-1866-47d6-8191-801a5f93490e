import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { basicProfileResponse } from "@/types/schemas/user";


export default async () => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get('profile', {method: 'get'}).json()
    return basicProfileResponse.parse(response)
}