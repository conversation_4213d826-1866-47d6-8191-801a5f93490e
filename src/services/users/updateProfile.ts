import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { registerSchema } from '@/types/schemas/register';
import { updateProfileSchema } from '@/types/schemas/updateProfile';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (data: any) => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).post('update_profile',{method: 'post', body: data}).json()
    console.log("SIGNUP RESPONSE::::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return updateProfileSchema.parse(response);
};
