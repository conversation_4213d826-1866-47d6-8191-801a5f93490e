import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { AUTH_STORRAGE_KEYS, FCM_TOKEN_STORAGE_KEY } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (data: any) => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

    const response: any = await secureInstance(token).post('push-notifications/register-device',{method: 'post', json: data}).json()
    console.log("FCM REGISTER", response)
    if (!response.success) {
        throw new Error(parseServerError(response))
    }
    storage.set(FCM_TOKEN_STORAGE_KEY, data.device_token);
    return;
};
