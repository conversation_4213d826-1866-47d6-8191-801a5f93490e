import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { basicStatusSchema } from "@/types/schemas/basic";


export default async () => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).delete('delete-account', {method: 'delete'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return basicStatusSchema.parse(response)
}