import { instance } from '@/services/instance';
import { registerSchema } from '@/types/schemas/register';
import { parseServerError } from '@/utils/utility';
export interface NewUser {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
}
export default async (data: NewUser) => {
	const response: any = await instance.post('register',{json: data, method: 'post'}).json()
    console.log("SIGNUP RESPONSE::::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return registerSchema.parse(response);
};
