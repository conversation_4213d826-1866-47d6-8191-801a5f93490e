import login, { LoginModel } from '../login';
import { instance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as verifyModule from '@/types/schemas/verify';

const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('login API function', () => {
  const mockData: LoginModel = {
    email: '<EMAIL>',
    password: 'password123',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should store token and return parsed response on success', async () => {
    const mockResponse = {
      token: 'mock-token',
      message: 'Login successful',
      user: {
        id: 123,
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
      },
    };


    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    const result = await login(mockData);

    expect(storage.set).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN, 'mock-token');
    expect(result).toEqual(mockResponse); // Assuming verifySchema.parse returns the same
  });

  it('should return response if user is null but success is true', async () => {
    const mockResponse = {
      token: 'mock-token',
      success: true,
      user: null,
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    const result = await login(mockData);

    expect(result).toEqual(mockResponse);
  });

  it('should throw error if success is false', async () => {
    const mockResponse = {
      success: false,
      message: 'Invalid credentials',
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    await expect(login(mockData)).rejects.toThrow('Parsed server error');
    expect(parseServerError).toHaveBeenCalledWith(mockResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      token: 'mock-token',
      success: true,
      user: { name: 'No ID' }, // assuming missing 'id' causes validation failure
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse),
    } as any);

    // Make verifySchema.parse throw a Zod error
    jest.spyOn(verifyModule.verifySchema, 'parse').mockImplementation(() => {
      throw new Error('Invalid schema');
    });

    await expect(login(mockData)).rejects.toThrow('Invalid schema');
  });
});
