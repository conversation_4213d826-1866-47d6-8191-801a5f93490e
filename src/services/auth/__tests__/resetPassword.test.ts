import resetPassword from '../resetPassword'; // Adjust path if filename is different
import { instance } from '@/services/instance';
import { parseServerError } from '@/utils/utility';
import * as basicModule from '@/types/schemas/basic';

const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('resetPassword service', () => {
  const inputData = { email: '<EMAIL>', code: '123456', password: 'newPassword' };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on success', async () => {
    const mockResponse = {
      success: true,
      message: 'Password reset successful',
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockReturnValue(mockResponse);

    const result = await resetPassword(inputData);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const mockErrorResponse = {
      errors: ['Invalid code'],
      success: false,
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockErrorResponse),
    } as any);

    await expect(resetPassword(inputData)).rejects.toThrow('Invalid code');
    expect(parseServerError).toHaveBeenCalledWith(mockErrorResponse);
  });

  it('should throw error if success is false without errors field', async () => {
    const mockErrorResponse = {
      success: false,
      message: 'Something went wrong',
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockErrorResponse),
    } as any);

    await expect(resetPassword(inputData)).rejects.toThrow('Parsed server error');
    expect(parseServerError).toHaveBeenCalledWith(mockErrorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      success: true,
      message: 1234, // invalid message type
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(resetPassword(inputData)).rejects.toThrow('Schema validation failed');
  });
});
