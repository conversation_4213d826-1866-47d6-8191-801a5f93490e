import logout from '../logout';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as basicModule from '@/types/schemas/basic';

const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('logout service', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should send logout request and return parsed response on success', async () => {
    const mockResponse = {
      success: true,
      message: 'Logout successful',
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn(() => ({
        json: jest.fn().mockResolvedValue(mockResponse),
      })),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockReturnValue(mockResponse);

    const result = await logout('device-token-abc');
    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const mockErrorResponse = {
      errors: ['Session not found'],
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn(() => ({
        json: jest.fn().mockResolvedValue(mockErrorResponse),
      })),
    } as any);

    await expect(logout('device-token-abc')).rejects.toThrow('Session not found');
    expect(parseServerError).toHaveBeenCalledWith(mockErrorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      message: 123, // assuming message should be string
      success: 'yes',
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn(() => ({
        json: jest.fn().mockResolvedValue(invalidResponse),
      })),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(logout('device-token-abc')).rejects.toThrow('Schema validation failed');
  });
});
