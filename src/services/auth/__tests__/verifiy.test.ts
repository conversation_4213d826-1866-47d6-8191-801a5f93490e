import verify from '../verify'; // Adjust path based on file structure
import { instance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as verifyModule from '@/types/schemas/verify';

const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('verify service', () => {
  const inputData = { email: '<EMAIL>', otp: '123456' };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should store token and return parsed response on success', async () => {
    const mockResponse = {
      token: 'abc123',
      user: { id: 1, name: 'John' },
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(verifyModule.verifySchema, 'parse').mockReturnValue(mockResponse);

    const result = await verify(inputData);

    expect(storage.set).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN, 'abc123');
    expect(result).toEqual(mockResponse);
  });

  it('should not store token if token is null', async () => {
    const mockResponse = {
      token: null,
      user: { id: 2, name: 'Jane' },
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(verifyModule.verifySchema, 'parse').mockReturnValue(mockResponse);

    const result = await verify(inputData);

    expect(storage.set).not.toHaveBeenCalled();
    expect(result).toEqual(mockResponse);
  });

  it('should throw an error if response has errors', async () => {
    const errorResponse = {
      errors: ['Invalid OTP'],
      user: { id: 3 },
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse),
    } as any);

    await expect(verify(inputData)).rejects.toThrow('Invalid OTP');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw an error if response.user is null', async () => {
    const errorResponse = {
      token: 'xyz789',
      user: null,
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse),
    } as any);

    await expect(verify(inputData)).rejects.toThrow('Parsed server error');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = {
      token: 'abc123',
      user: { id: 1, name: 'John' },
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(verifyModule.verifySchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(verify(inputData)).rejects.toThrow('Schema validation failed');
  });
});
