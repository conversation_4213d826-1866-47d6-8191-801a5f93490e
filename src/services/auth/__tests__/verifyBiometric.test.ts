import verifyBiometric from '../verifyBiometric'; // Adjust the import based on your structure
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as verifyModule from '@/types/schemas/verify';

const mockedSecureInstance = secureInstance as jest.Mock;

describe('verifyBiometric service', () => {
  const userId = 123;
  const inputData = { device_token: 'xyz' };

  const postMock = jest.fn();
  const jsonMock = jest.fn();

  beforeEach(() => {
    postMock.mockReturnValue({ json: jsonMock });
    mockedSecureInstance.mockReturnValue({ post: postMock } as any);
    (storage.getString as jest.Mock).mockReturnValue('fake_token');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should store token and return parsed response on success with user', async () => {
    const mockResponse = {
      token: 'new_token',
      success: true,
      user: { id: 1 },
    };

    jsonMock.mockResolvedValue(mockResponse);
    jest.spyOn(verifyModule.verifySchema, 'parse').mockReturnValue(mockResponse);

    const result = await verifyBiometric(userId, inputData);

    expect(secureInstance).toHaveBeenCalledWith('fake_token');
    expect(storage.set).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN, 'new_token');
    expect(result).toEqual(mockResponse);
  });

  it('should return response as-is if user is null and success is true', async () => {
    const mockResponse = {
      token: null,
      success: true,
      user: null,
    };

    jsonMock.mockResolvedValue(mockResponse);
    const result = await verifyBiometric(userId, inputData);

    expect(result).toEqual(mockResponse);
    expect(storage.set).not.toHaveBeenCalled();
  });

  it('should throw an error if success is false', async () => {
    const mockResponse = {
      token: null,
      success: false,
      user: null,
    };

    jsonMock.mockResolvedValue(mockResponse);

    await expect(verifyBiometric(userId, inputData)).rejects.toThrow('Parsed server error');
    expect(parseServerError).toHaveBeenCalledWith(mockResponse);
  });

  it('should throw if verifySchema.parse fails', async () => {
    const mockResponse = {
      token: 'some_token',
      success: true,
      user: { id: 1 },
    };

    jsonMock.mockResolvedValue(mockResponse);
    jest.spyOn(verifyModule.verifySchema, 'parse').mockImplementation(() => {
      throw new Error('Schema parse failed');
    });

    await expect(verifyBiometric(userId, inputData)).rejects.toThrow('Schema parse failed');
  });
});
