import registerBiometric from '../registerBiometric'; // adjust the path if necessary
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as basicModule from '@/types/schemas/basic';

const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('registerBiometric service', () => {
  const userId = 42;
  const inputData = {
    device_token: 'mock-device-token',
    platform: 'ios',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on success', async () => {
    const mockResponse = {
      success: true,
      message: 'Biometric registered',
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse),
      }),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockReturnValue(mockResponse);

    const result = await registerBiometric(userId, inputData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const mockErrorResponse = {
      errors: ['Something went wrong'],
      success: false,
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockErrorResponse),
      }),
    } as any);

    await expect(registerBiometric(userId, inputData)).rejects.toThrow('Something went wrong');
    expect(parseServerError).toHaveBeenCalledWith(mockErrorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      success: true,
      message: 123, // invalid type for schema
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(invalidResponse),
      }),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(registerBiometric(userId, inputData)).rejects.toThrow('Schema validation failed');
  });
});
