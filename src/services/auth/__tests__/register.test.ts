import register, { NewUser } from '../register';
import { instance } from '@/services/instance';
import { parseServerError } from '@/utils/utility';
import * as registerModule from '@/types/schemas/register';

const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('register service', () => {
  const mockUser: NewUser = {
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'securePassword123',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on successful registration', async () => {
    const mockResponse = {
      message: 'Account created successfully',
      user: {
        id: 456,
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
      },
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(registerModule.registerSchema, 'parse').mockReturnValue(mockResponse);

    const result = await register(mockUser);
    expect(result).toEqual(mockResponse);
  });

  it('should throw an error if response has errors', async () => {
    const errorResponse = {
      errors: ['Email already exists'],
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse),
    } as any);

    await expect(register(mockUser)).rejects.toThrow('Email already exists');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      message: 123, // invalid type
      user: null,
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse),
    } as any);

    jest.spyOn(registerModule.registerSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(register(mockUser)).rejects.toThrow('Schema validation failed');
  });
});
