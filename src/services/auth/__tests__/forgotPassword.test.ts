import forgotPassword from '../forgotPassword';
import { instance } from '@/services/instance';
import { parseServerError } from '@/utils/utility';
import * as basicModule from '@/types/schemas/basic';

const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('forgotPassword service', () => {
  const inputData = { email: '<EMAIL>' };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response when success is true', async () => {
    const mockResponse = {
      message: 'Password reset link sent to your email.',
      success: true,
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockReturnValue(mockResponse);

    const result = await forgotPassword(inputData);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error when success is false', async () => {
    const mockErrorResponse = {
      success: false,
      message: 'Email not registered',
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockErrorResponse),
    } as any);

    await expect(forgotPassword(inputData)).rejects.toThrow('Parsed server error');
    expect(parseServerError).toHaveBeenCalledWith(mockErrorResponse);
  });

  it('should throw error when schema validation fails', async () => {
    const invalidResponse = {
      success: true,
      message: 1234, // invalid type for message
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(forgotPassword(inputData)).rejects.toThrow('Schema validation failed');
  });
});
