import resendVerification from '../resendVerification';
import { instance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import * as basicModule from '@/types/schemas/basic';

const mockedInstance = instance as jest.Mocked<typeof instance>;

describe('resendVerification service', () => {
  const inputData = { email: '<EMAIL>' };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should store token and return parsed response on success', async () => {
    const mockResponse = {
      token: 'new-token-123',
      success: true,
      message: 'OTP sent successfully',
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockReturnValue(mockResponse);

    const result = await resendVerification(inputData);

    expect(storage.set).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN, 'new-token-123');
    expect(result).toEqual(mockResponse);
  });

  it('should not store token if it is not present and still parse response', async () => {
    const mockResponse = {
      success: true,
      message: 'OTP resent',
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockReturnValue(mockResponse);

    const result = await resendVerification(inputData);

    expect(storage.set).not.toHaveBeenCalled();
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response has errors', async () => {
    const errorResponse = {
      errors: ['User not found'],
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse),
    } as any);

    await expect(resendVerification(inputData)).rejects.toThrow('User not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const invalidResponse = {
      token: 'bad-token',
      message: 123, // invalid type for schema
    };

    mockedInstance.post.mockReturnValue({
      json: jest.fn().mockResolvedValue(invalidResponse),
    } as any);

    jest.spyOn(basicModule.basicSchema, 'parse').mockImplementation(() => {
      throw new Error('Schema validation failed');
    });

    await expect(resendVerification(inputData)).rejects.toThrow('Schema validation failed');
  });
});
