import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { basicSchema } from '@/types/schemas/basic';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (storedToken?: string) => {
    let url = 'logout';
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

    const data = {
        device_token: storedToken
    };

    const response: any = await secureInstance(token).post(url, {json: data, method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return basicSchema.parse(response);
};
