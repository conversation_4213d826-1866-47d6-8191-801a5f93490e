import { instance } from '@/services/instance';
import { basicSchema } from '@/types/schemas/basic';
import { parseServerError } from '@/utils/utility';
export default async (data: Object) => {
	const response: any = await instance.post('forgot-password',{json: data, method: 'post'}).json()
    if (response.success == true) {
        return basicSchema.parse(response);
    } else {
        throw new Error(parseServerError(response))
    }
};
