import { instance } from '@/services/instance';
import { verifySchema } from '@/types/schemas/verify';
import { parseServerError } from '@/utils/utility';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
export interface LoginModel {
    email: string;
    password: string;
}
export default async (data: LoginModel) => {
	const response: any = await instance.post('login',{json: data, method: 'post'}).json()

    if (response.token != null) {
        storage.set(AUTH_STORRAGE_KEYS.TOKEN, response.token)
    }
    if (response.success == false) {
        throw new Error(parseServerError(response))
    }
    if (response.user == null && response.success == true) {
        return response
    }
	return verifySchema.parse(response);
};
