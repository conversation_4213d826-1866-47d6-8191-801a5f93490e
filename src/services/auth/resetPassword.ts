import { instance } from '@/services/instance';
import { parseServerError } from '@/utils/utility';
import { basicSchema } from '@/types/schemas/basic';
export default async (data: Object) => {
	const response: any = await instance.post('forgot-password-verify',{json: data, method: 'post'}).json()
    if (response.errors != null || response.success == false) {
        throw new Error(parseServerError(response))
    }
	return basicSchema.parse(response);
};
