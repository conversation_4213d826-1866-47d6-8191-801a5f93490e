import { instance } from '@/services/instance';
import { verifySchema } from '@/types/schemas/verify';
import { parseServerError } from '@/utils/utility';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
export default async (data: Object) => {
	const response: any = await instance.post('verify',{json: data, method: 'post'}).json()
    if (response.token != null) {
        storage.set(AUTH_STORRAGE_KEYS.TOKEN, response.token)
    }
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    if (response.user == null) {
        throw new Error(parseServerError(response))
    }
	return verifySchema.parse(response);
};
