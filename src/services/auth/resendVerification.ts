import { instance } from '@/services/instance';
import { parseServerError } from '@/utils/utility';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { basicSchema } from '@/types/schemas/basic';
export default async (data: Object) => {
	const response: any = await instance.post('resend-otp',{json: data, method: 'post'}).json()
    if (response.token != null) {
        storage.set(AUTH_STORRAGE_KEYS.TOKEN, response.token)
    }
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    
	return basicSchema.parse(response);
};
