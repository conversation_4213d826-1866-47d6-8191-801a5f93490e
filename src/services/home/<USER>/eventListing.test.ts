import eventListing from '../eventListing';
import { secureInstance } from '../../instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as eventModule from '@/types/schemas/eventListing';

jest.mock('../../instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn()
  }
}));

const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('eventListing service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const locationId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch events with page=1', async () => {
    const mockResponse = {
      data: [
        { id: 1, title: 'Event 1', location: 'Location 1', date: '2023-01-01' },
        { id: 2, title: 'Event 2', location: 'Location 2', date: '2023-01-02' }
      ],
      current_page: 1,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(eventModule.eventListingResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await eventListing(moduleId, locationId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/events/locations/${locationId}`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Something went wrong']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    await expect(eventListing(moduleId, locationId)).rejects.toThrow('Something went wrong');
  });
});