import podcastDetail from '../podcastDetail';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('podcastDetail service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const podcastId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch podcast detail successfully', async () => {
    const mockResponse = {
      id: 1,
      category_id: 10,
      category: {
        id: 10,
        name: "Technology"
      },
      tag_id: null,
      title: "The Future of AI",
      guid: "abc-123-guid",
      description: "An insightful discussion about AI advancements.",
      module_id: 5,
      audio_link: "https://example.com/audio.mp3",
      thumbnail: "https://example.com/thumb.jpg",
      created_at: "2025-06-17T10:00:00Z",
      updated_at: "2025-06-17T12:00:00Z",
      previous: null,
      next: 2,
      comments: [],
      is_liked: true,
      is_bookmarked: false
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await podcastDetail(moduleId, podcastId);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/podcasts/${podcastId}`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(podcastDetail(moduleId, podcastId)).rejects.toThrow();
  });
});