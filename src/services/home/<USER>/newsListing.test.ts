import newsListing from '../newsListing';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('newsListing service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const sourceId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch news with default page', async () => {
    const mockResponse = {
      success: true,
      module: {
        id: 1,
        title: "Daily News",
        thumbnail: "https://example.com/images/news-thumbnail.jpg",
        mob_image: "https://example.com/images/news-mobile.jpg",
        order: 5,
        type: "news",
        bg_color: "#FFFFFF",
        logo_color: "#000000",
        header_content_value: "Stay updated with the latest headlines",
        header_content_type: "text",
        created_at: "2025-06-10T08:00:00Z",
        updated_at: "2025-06-14T08:00:00Z",
        slug: "daily-news"
      },
      news_source: {
        id: 10,
        external_id: 200,
        module_id: 1,
        name: "The Global Times",
        url: "https://globaltimes.example.com",
        base_href: "https://globaltimes.example.com",
        thumbnail: "https://example.com/images/global-times.jpg",
        created_at: "2025-06-01T09:00:00Z",
        updated_at: "2025-06-12T09:00:00Z"
      },
      news: {
        current_page: 1,
        data: [
          {
            id: 101,
            external_id: 501,
            news_source_id: 10,
            module_id: 1,
            title: "Economy Set to Bounce Back in 2025",
            description: "Experts predict strong economic growth fueled by tech sector and global recovery.",
            url: "https://globaltimes.example.com/articles/economy-bounce-2025",
            author: "Jane Doe",
            thumbnail: "https://example.com/images/economy.jpg",
            created_at: "2025-06-11T07:30:00Z",
            updated_at: "2025-06-12T07:30:00Z",
            is_liked: true,
            is_bookmarked: false,
            comments: []
          },
          {
            id: 102,
            external_id: 502,
            news_source_id: 10,
            module_id: 1,
            title: "New Tech Laws Announced",
            description: "Government introduces new laws for data protection and online privacy.",
            url: "https://globaltimes.example.com/articles/tech-laws",
            author: "John Smith",
            thumbnail: "https://example.com/images/tech-laws.jpg",
            created_at: "2025-06-11T08:00:00Z",
            updated_at: "2025-06-12T08:30:00Z",
            is_liked: false,
            is_bookmarked: true,
            comments: []
          }
        ],
        first_page_url: "https://api.example.com/news?page=1",
        from: 1,
        last_page: 3,
        last_page_url: "https://api.example.com/news?page=3",
        links: [
          {
            url: null,
            label: "&laquo; Previous",
            active: false
          },
          {
            url: "https://api.example.com/news?page=1",
            label: "1",
            active: true
          },
          {
            url: "https://api.example.com/news?page=2",
            label: "2",
            active: false
          },
          {
            url: "https://api.example.com/news?page=3",
            label: "3",
            active: false
          },
          {
            url: "https://api.example.com/news?page=2",
            label: "Next &raquo;",
            active: false
          }
        ],
        next_page_url: "https://api.example.com/news?page=2",
        path: "https://api.example.com/news",
        per_page: 2,
        prev_page_url: null,
        to: 2,
        total: 6
      }
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await newsListing(moduleId, sourceId, 1);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/news-sources/${sourceId}/news`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(newsListing(moduleId, sourceId)).rejects.toThrow();
  });
});