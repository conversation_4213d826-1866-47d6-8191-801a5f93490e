import empowerHerListing from '../empowerHerListing';
import { secureInstance } from '../../instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as empowerHerModule from '@/types/schemas/empowerHerListings';

jest.mock('../../instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn()
  }
}));


const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('empowerHerListing service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch empower her listings with page=1', async () => {
    const mockResponse = {
      data: [
        { id: 1, title: 'Empower Her 1', description: 'Description 1' },
        { id: 2, title: 'Empower Her 2', description: 'Description 2' }
      ],
      current_page: 1,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(empowerHerModule.empowerHerListingResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await empowerHerListing(moduleId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/empower-her`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Something went wrong']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    await expect(empowerHerListing(moduleId)).rejects.toThrow('Something went wrong');
  });
});