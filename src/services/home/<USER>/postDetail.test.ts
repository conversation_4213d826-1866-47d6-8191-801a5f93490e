import postDetail from '../postDetail';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('postDetail service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const postId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch post detail successfully', async () => {
    const mockResponse = {
      id: 101,
      module_id: 12,
      title: "Everything You Need to Know About Zod",
      content: "Zod is a TypeScript-first schema declaration and validation library...",
      thumbnail: "https://example.com/images/zod-thumbnail.jpg",
      created_at: "2025-06-17T09:00:00Z",
      updated_at: "2025-06-17T10:30:00Z",
      is_liked: true,
      is_bookmarked: false,
      comments: []
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await postDetail(moduleId, postId);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/posts/${postId}`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(postDetail(moduleId, postId)).rejects.toThrow();
  });
});