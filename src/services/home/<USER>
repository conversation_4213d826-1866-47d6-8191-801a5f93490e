import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { blogListingResponseSchema } from '@/types/schemas/blogListing';
import { eventLocationListingResponseSchema } from '@/types/schemas/eventLocationListing';
import { musicListingResponseSchema } from '@/types/schemas/musicListing';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (id?: number) => {
    let url = `modules/${id}/events`
    console.log("URL :::", url)
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return eventLocationListingResponseSchema.parse(response);
};
