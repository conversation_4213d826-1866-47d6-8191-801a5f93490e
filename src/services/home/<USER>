import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { blogListingResponseSchema } from '@/types/schemas/blogListing';
import { musicSchema } from '@/types/schemas/musicListing';
import { podcastSchema } from '@/types/schemas/podcastListing';
import { postDetailSchema } from '@/types/schemas/postDetail';
import { videoSchema } from '@/types/schemas/videoListing';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (moduleId?: number, podcastId?: number) => {
    let url = `modules/${moduleId}/podcasts/${podcastId}`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    } else if (response.status == false) {
        return response;
    }
	return podcastSchema.parse(response);
};
