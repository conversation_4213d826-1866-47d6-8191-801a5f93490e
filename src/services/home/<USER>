import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { blogListingResponseSchema } from '@/types/schemas/blogListing';
import { musicSchema } from '@/types/schemas/musicListing';
import { newsSchema } from '@/types/schemas/newsListing';
import { postDetailSchema } from '@/types/schemas/postDetail';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (moduleId?: number, sourceId?: number, newsId?: number) => {
    let url = `modules/${moduleId}/news-sources/${sourceId}/news/${newsId}`
    console.log("URL :::", url)
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    console.log("response::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return newsSchema.parse(response);
};
