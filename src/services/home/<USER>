import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { podcastListingResponseSchema } from '@/types/schemas/podcastListing';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (id?: number) => {
    let url = `modules/${id}/podcasts?request_source=web`
    console.log("URL :::", url)
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    console.log("Response::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return podcastListingResponseSchema.parse(response);
};
