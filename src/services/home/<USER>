import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { statusBasicSchema } from '@/types/schemas/statusBasicSchema';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import { basicMusicSchema, basicNewsSchema, basicPodcastSchema, basicPostSchema, basicSchema, basicVideSchema } from '@/types/schemas/basic';

export default async (moduleId?: number, moduleName?: string, postId?: number) => {
    let url = `modules/${moduleId}/${moduleName}/${postId}/bookmark`
    console.log("URL :::", url)
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).post(url, {method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    if (moduleName == 'news') {
        return basicNewsSchema.parse(response);
    }
    if (moduleName == 'podcasts') {
        return basicPodcastSchema.parse(response);
    }
    if (moduleName == 'posts') {
        return basicPostSchema.parse(response);
    }
    if (moduleName == 'videos') {
        return basicVideSchema.parse(response);
    }
    if (moduleName == 'music') {
        return basicMusicSchema.parse(response);
    }
};
