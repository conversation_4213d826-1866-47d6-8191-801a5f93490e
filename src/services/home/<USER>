import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { moduleResponseSchema } from '@/types/schemas/module';
import { registerSchema } from '@/types/schemas/register';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (data?: any) => {
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).get('home', {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return moduleResponseSchema.parse(response);
};
