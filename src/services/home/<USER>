import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { eventListingResponseSchema } from '@/types/schemas/eventListing';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (moduleId?: number, locationId?: number) => {
    let url = `modules/${moduleId}/events/locations/${locationId}`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    console.log("Response::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return eventListingResponseSchema.parse(response);
};
