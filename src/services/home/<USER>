import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { basicSchema } from "@/types/schemas/basic";


export default async (postId: number, data: any) => {
    var url = `report-comments/${postId}`;
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    console.log(postId, data);
    const response: any = await secureInstance(token).post(url, {json: data, method: 'post'}).json();
    console.log("Report Comment Response:::", response);
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return basicSchema.parse(response);
}