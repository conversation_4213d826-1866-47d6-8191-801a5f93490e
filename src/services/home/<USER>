import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { blogListingResponseSchema } from '@/types/schemas/blogListing';
import { empowerHerListingResponseSchema } from '@/types/schemas/empowerHerListings';
import { empowerHerPostsListingResponseSchema } from '@/types/schemas/empowerHerPostsListing';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (moduleId?: number, id?: number) => {
    let url = `modules/${moduleId}/empower-her/${id}`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return empowerHerPostsListingResponseSchema.parse(response);
};
