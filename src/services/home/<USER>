import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { blogListingResponseSchema } from '@/types/schemas/blogListing';
import { musicListingResponseSchema } from '@/types/schemas/musicListing';
import { newsSourceListingResponseSchema } from '@/types/schemas/newsSources';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (id?: number) => {
    let url = `modules/${id}/news-sources`
    console.log("URL :::", url)
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return  newsSourceListingResponseSchema.parse(response);
};
