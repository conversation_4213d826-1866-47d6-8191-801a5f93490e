import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { basicMusicSchema, basicNewsSchema, basicPodcastSchema, basicPostSchema, basicSchema, basicVideSchema } from '@/types/schemas/basic';
import { blogListingResponseSchema } from '@/types/schemas/blogListing';
import { postDetailSchema } from '@/types/schemas/postDetail';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (moduleId?: number, moduleName?: string, postId?: number, data?: any) => {
    let url = `modules/${moduleId}/${moduleName}/${postId}/comments`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).post(url, {json: data, method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    if (moduleName == 'news') {
        return basicNewsSchema.parse(response);
    }
    if (moduleName == 'podcasts') {
        return basicPodcastSchema.parse(response);
    }
    if (moduleName == 'posts') {
        return basicPostSchema.parse(response);
    }
    if (moduleName == 'videos') {
        return basicVideSchema.parse(response);
    }
    if (moduleName == 'music') {
        return basicMusicSchema.parse(response);
    }
	// return basicSchema.parse(response);
};
