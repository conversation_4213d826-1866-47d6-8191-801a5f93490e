import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { basicMusicSchema, basicNewsSchema, basicPodcastSchema, basicPostSchema, basicSchema, basicVideSchema } from '@/types/schemas/basic';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (moduleId?: number, moduleName?: string, postId?: number, commentId?: number ,data?: any) => {
    let url = `modules/${moduleId}/${moduleName}/${postId}/comments/${commentId}`
    console.log("URL :::", url)
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).patch(url, {json: data, method: 'patch'}).json()
    console.log("Response:::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    if (moduleName == 'news') {
        return basicNewsSchema.parse(response);
    }
    if (moduleName == 'podcasts') {
        return basicPodcastSchema.parse(response);
    }
    if (moduleName == 'posts') {
        return basicPostSchema.parse(response);
    }
    if (moduleName == 'videos') {
        return basicVideSchema.parse(response);
    }
    if (moduleName == 'music') {
        return basicMusicSchema.parse(response);
    }
};
