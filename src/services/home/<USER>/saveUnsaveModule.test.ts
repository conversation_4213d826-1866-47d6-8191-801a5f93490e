import saveUnsaveModule from '../saveUnsaveModule';
import { secureInstance } from '../../instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';

jest.mock('../../instance');
jest.mock('@/App');

describe('saveUnsaveModule service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const itemId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should bookmark post module item successfully', async () => {
    const mockResponse = {
      success: true,
      message: "Post retrieved successfully",
      post: {
        id: 3,
        module_id: 7,
        title: "Understanding TypeScript",
        content: "TypeScript is a superset of JavaScript...",
        thumbnail: "https://example.com/post.jpg",
        created_at: "2025-06-15T08:00:00Z",
        updated_at: "2025-06-15T09:30:00Z",
        is_liked: true,
        is_bookmarked: true,
        comments: []
      }
    };
    const moduleName = 'posts';
    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ post: postMock });

    await saveUnsaveModule(moduleId, moduleName, itemId);
    
    expect(postMock).toHaveBeenCalledWith(`modules/${moduleId}/${moduleName}/${itemId}/bookmark`, {
      method: 'post'
    });
  });

  it('should bookmark news module item successfully', async () => {
    const mockResponse = {
      success: true,
      message: "News fetched successfully",
      news: {
        id: 4,
        external_id: null,
        news_source_id: null,
        module_id: 8,
        title: "Tech Conference 2025 Highlights",
        description: "The most anticipated tech event...",
        url: "https://news.example.com/article",
        author: "Jane Doe",
        thumbnail: "https://example.com/news.jpg",
        created_at: "2025-06-14T11:00:00Z",
        updated_at: "2025-06-14T12:00:00Z",
        is_liked: false,
        is_bookmarked: false,
        comments: []
      }
    };
    const moduleName = 'news';
    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ post: postMock });

    await saveUnsaveModule(moduleId, moduleName, itemId);
    
    expect(postMock).toHaveBeenCalledWith(`modules/${moduleId}/${moduleName}/${itemId}/bookmark`, {
      method: 'post'
    });
  });

  it('should bookmark podcast module item successfully', async () => {
    const mockResponse = {
      success: true,
      message: "Podcast fetched successfully",
      podcast: {
        id: 1,
        category_id: 10,
        category: {
          id: 10,
          name: "Technology"
        },
        tag_id: null,
        title: "The Future of AI",
        guid: "abc-123-guid",
        description: "An insightful discussion about AI advancements.",
        module_id: 5,
        audio_link: "https://example.com/audio.mp3",
        thumbnail: "https://example.com/thumb.jpg",
        created_at: "2025-06-17T10:00:00Z",
        updated_at: "2025-06-17T12:00:00Z",
        previous: null,
        next: 2,
        comments: [],
        is_liked: true,
        is_bookmarked: false
      }
    };
    const moduleName = 'podcasts';
    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ post: postMock });

    await saveUnsaveModule(moduleId, moduleName, itemId);
    
    expect(postMock).toHaveBeenCalledWith(`modules/${moduleId}/${moduleName}/${itemId}/bookmark`, {
      method: 'post'
    });
  });

  it('should bookmark video module item successfully', async () => {
    const mockResponse = {
      success: true,
      message: "Video loaded successfully",
      video: {
        id: 5,
        category_id: 30,
        tag_id: null,
        title: "How to build a mobile app",
        module_id: 9,
        video_link: "https://example.com/video.mp4",
        video_type: "mp4",
        thumbnail: "https://example.com/video.jpg",
        created_at: "2025-06-13T10:00:00Z",
        updated_at: "2025-06-13T11:00:00Z",
        previous: null,
        next: null,
        is_liked: true,
        is_bookmarked: true,
        comments: []
      }
    };
    const moduleName = 'videos';
    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ post: postMock });

    await saveUnsaveModule(moduleId, moduleName, itemId);
    
    expect(postMock).toHaveBeenCalledWith(`modules/${moduleId}/${moduleName}/${itemId}/bookmark`, {
      method: 'post'
    });
  });

  it('should bookmark music module item successfully', async () => {
    const mockResponse = {
      success: true,
      message: "Music fetched successfully",
      music: {
        id: 2,
        category_id: 20,
        tag_id: null,
        title: "Chill Vibes",
        module_id: 6,
        audio_link: "https://example.com/music.mp3",
        thumbnail: "https://example.com/music.jpg",
        created_at: "2025-06-16T14:00:00Z",
        updated_at: "2025-06-16T15:00:00Z",
        previous: null,
        next: null,
        is_liked: false,
        is_bookmarked: true,
        comments: []
      }
    };
    const moduleName = 'music';
    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ post: postMock });

    await saveUnsaveModule(moduleId, moduleName, itemId);
    
    expect(postMock).toHaveBeenCalledWith(`modules/${moduleId}/${moduleName}/${itemId}/bookmark`, {
      method: 'post'
    });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const moduleName = 'posts';
    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ post: postMock });

    await expect(saveUnsaveModule(moduleId, moduleName, itemId)).rejects.toThrow();
  });
});