import podcastListing from '../podcastListing';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('podcastListing service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch podcasts with default page', async () => {
    const mockResponse = {
      success: true,
      module: {
        id: 1,
        title: "Podcast Hub",
        thumbnail: "https://example.com/thumbs/podcast-hub.jpg",
        mob_image: "https://example.com/thumbs/podcast-hub-mobile.jpg",
        order: 4,
        type: "podcast",
        bg_color: "#FFFFFF",
        logo_color: "#333333",
        header_content_value: "Listen to inspiring podcasts",
        header_content_type: "text",
        created_at: "2025-06-12T09:00:00Z",
        updated_at: "2025-06-13T09:30:00Z",
        slug: "podcast-hub"
      },
      categories: [
        {
          id: 1,
          module_id: 1,
          name: "Motivation",
          layout: "list",
          created_at: "2025-06-11T10:00:00Z",
          updated_at: "2025-06-13T08:00:00Z",
          podcasts_count: 2,
          is_Loading: false,
          podcasts: [
            {
              id: 101,
              category_id: 1,
              category: {
                id: 1,
                name: "Motivation"
              },
              tag_id: null,
              title: "Rise and Grind",
              guid: "ep-001",
              description: "Start your day with powerful motivation.",
              module_id: 1,
              audio_link: "https://example.com/audio/rise-and-grind.mp3",
              thumbnail: "https://example.com/thumbs/rise-and-grind.jpg",
              created_at: "2025-06-10T09:00:00Z",
              updated_at: "2025-06-12T10:00:00Z",
              previous: null,
              next: 102,
              is_liked: true,
              is_bookmarked: false,
              comments: []
            },
            {
              id: 102,
              category_id: 1,
              category: {
                id: 1,
                name: "Motivation"
              },
              tag_id: 7,
              title: "Winning Mindset",
              guid: "ep-002",
              description: "Develop a mindset for success.",
              module_id: 1,
              audio_link: "https://example.com/audio/winning-mindset.mp3",
              thumbnail: "https://example.com/thumbs/winning-mindset.jpg",
              created_at: "2025-06-11T09:30:00Z",
              updated_at: "2025-06-13T09:00:00Z",
              previous: 101,
              next: null,
              is_liked: false,
              is_bookmarked: true,
              comments: []
            }
          ]
        }
      ]
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await podcastListing(moduleId);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/podcasts?request_source=web`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(podcastListing(moduleId)).rejects.toThrow();
  });
});