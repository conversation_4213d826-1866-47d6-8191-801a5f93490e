import empowerherPostDetail from '../empowerherPostDetail';
import { secureInstance } from '../../instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as empowerherPostDetailModule from '@/types/schemas/empowerHerPostsListing';

jest.mock('../../instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn()
  }
}));

const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('empowerherPostDetail service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const empowerHerId = 456;
  const postId = 789;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch empower her post detail successfully', async () => {
    const mockResponse = {
      id: postId,
      title: 'Post Title',
      content: 'Post Content',
      author: 'Author Name',
      created_at: '2023-01-01'
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(empowerherPostDetailModule.empowerHerPostSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await empowerherPostDetail(moduleId, empowerHerId, postId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/empower-her/${empowerHerId}/posts/${postId}`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Something went wrong']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    await expect(empowerherPostDetail(moduleId, empowerHerId, postId)).rejects.toThrow('Something went wrong');
  });
});