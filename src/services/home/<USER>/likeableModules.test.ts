import likeableModules from '../likeableModules';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('likeableModules service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch likeable modules successfully', async () => {
    const mockResponse = {
      success: true,
      modules: [
        {
          id: 1,
          title: "Music",
          thumbnail: "https://example.com/images/music-thumbnail.jpg",
          mob_image: "https://example.com/images/music-mobile.jpg",
          order: 1,
          type: "music",
          bg_color: "#F5F5F5",
          logo_color: "#333333",
          header_content_value: "Discover the latest music",
          header_content_type: "text",
          created_at: "2025-06-10T08:00:00Z",
          updated_at: "2025-06-14T08:00:00Z",
          slug: "music"
        },
        {
          id: 2,
          title: "Podcasts",
          thumbnail: "https://example.com/images/podcasts-thumbnail.jpg",
          mob_image: null,
          order: 2,
          type: "podcast",
          bg_color: "#FFFFFF",
          logo_color: "#000000",
          header_content_value: "Stream trending podcasts",
          header_content_type: "text",
          created_at: "2025-06-09T10:30:00Z",
          updated_at: "2025-06-14T09:15:00Z",
          slug: "podcasts"
        },
        {
          id: 3,
          title: "Videos",
          thumbnail: "https://example.com/images/videos-thumbnail.jpg",
          mob_image: "https://example.com/images/videos-mobile.jpg",
          order: 3,
          type: "video",
          bg_color: "#EFEFEF",
          logo_color: "#111111",
          header_content_value: "Watch popular videos",
          header_content_type: "text",
          created_at: "2025-06-08T12:00:00Z",
          updated_at: "2025-06-14T11:00:00Z",
          slug: "videos"
        }
      ]
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    const result = await likeableModules();
    
    expect(getMock).toHaveBeenCalledWith('modules/like-able', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(likeableModules()).rejects.toThrow();
  });
});