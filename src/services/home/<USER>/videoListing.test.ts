import videoListing from '../videoListing';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('videoListing service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch videos with default page', async () => {
    const mockResponse = {
      success: true,
      module: {
        id: 1,
        title: "Video Module",
        thumbnail: "https://example.com/thumbs/video-module.jpg",
        mob_image: null,
        order: 2,
        type: "video",
        bg_color: "#F5F5F5",
        logo_color: "#222222",
        header_content_value: "Watch trending and featured videos",
        header_content_type: "text",
        created_at: "2025-06-10T10:00:00Z",
        updated_at: "2025-06-12T09:00:00Z",
        slug: "video-module"
      },
      categories: [
        {
          id: 1,
          module_id: 1,
          name: "Featured",
          layout: "carousel",
          created_at: "2025-06-10T08:00:00Z",
          updated_at: "2025-06-11T08:00:00Z",
          videos: [
            {
              id: 301,
              category_id: 1,
              tag_id: null,
              title: "Inspiring Journeys",
              module_id: 1,
              video_link: "https://example.com/videos/inspiring-journeys.mp4",
              video_type: "mp4",
              thumbnail: "https://example.com/thumbs/inspiring.jpg",
              created_at: "2025-06-10T10:00:00Z",
              updated_at: "2025-06-11T10:00:00Z",
              previous: null,
              next: 302,
              is_liked: true,
              is_bookmarked: false,
              comments: []
            },
            {
              id: 302,
              category_id: 1,
              tag_id: 10,
              title: "Tech Reviews 2025",
              module_id: 1,
              video_link: "https://example.com/videos/tech-reviews.mp4",
              video_type: "mp4",
              thumbnail: "https://example.com/thumbs/tech.jpg",
              created_at: "2025-06-11T12:00:00Z",
              updated_at: "2025-06-12T13:00:00Z",
              previous: 301,
              next: null,
              is_liked: false,
              is_bookmarked: true,
              comments: []
            }
          ]
        }
      ]
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await videoListing(moduleId);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/videos`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(videoListing(moduleId)).rejects.toThrow();
  });
});