import videoDetail from '../videoDetail';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('videoDetail service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const videoId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch video detail successfully', async () => {
    const mockResponse = {
      id: 5,
      category_id: 30,
      tag_id: null,
      title: "How to build a mobile app",
      module_id: 9,
      video_link: "https://example.com/video.mp4",
      video_type: "mp4",
      thumbnail: "https://example.com/video.jpg",
      created_at: "2025-06-13T10:00:00Z",
      updated_at: "2025-06-13T11:00:00Z",
      previous: null,
      next: null,
      is_liked: true,
      is_bookmarked: true,
      comments: []
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await videoDetail(moduleId, videoId);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/videos/${videoId}`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(videoDetail(moduleId, videoId)).rejects.toThrow();
  });
});