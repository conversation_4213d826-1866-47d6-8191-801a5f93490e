import podcastsByCategory from '../podcastsByCategory';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('podcastsByCategory service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const categoryId = 456;
  const page = 1;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch podcasts by category with default page', async () => {
    const mockResponse = {
      success: true,
      podcasts: {
        current_page: 1,
        data: [
          {
            id: 1,
            category_id: 2,
            category: {
              id: 2,
              name: "Technology"
            },
            tag_id: null,
            title: "The Future of AI",
            guid: "podcast-001",
            description: "Exploring the future of artificial intelligence and its impact on society.",
            module_id: 10,
            audio_link: "https://example.com/audio/ai-future.mp3",
            thumbnail: "https://example.com/thumbnails/ai.jpg",
            created_at: "2025-06-01T10:00:00Z",
            updated_at: "2025-06-01T12:00:00Z",
            previous: null,
            next: 2,
            comments: [],
            is_liked: true,
            is_bookmarked: false
          },
          {
            id: 2,
            category_id: 2,
            category: {
              id: 2,
              name: "Technology"
            },
            tag_id: null,
            title: "Quantum Computing Explained",
            guid: "podcast-002",
            description: "A beginner-friendly overview of quantum computing.",
            module_id: 10,
            audio_link: "https://example.com/audio/quantum.mp3",
            thumbnail: "https://example.com/thumbnails/quantum.jpg",
            created_at: "2025-06-02T10:00:00Z",
            updated_at: "2025-06-02T12:00:00Z",
            previous: 1,
            next: null,
            comments: [],
            is_liked: false,
            is_bookmarked: true
          }
        ],
        first_page_url: "https://example.com/api/podcasts?page=1",
        from: 1,
        last_page: 1,
        last_page_url: "https://example.com/api/podcasts?page=1",
        links: [
          { url: null, label: "&laquo; Previous", active: false },
          { url: "https://example.com/api/podcasts?page=1", label: "1", active: true },
          { url: null, label: "Next &raquo;", active: false }
        ],
        next_page_url: null,
        path: "https://example.com/api/podcasts",
        per_page: 10,
        prev_page_url: null,
        to: 2,
        total: 2
      }
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await podcastsByCategory(moduleId, categoryId, page);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/podcasts-by-category/${categoryId}?page=${page}&limit=10`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(podcastsByCategory(moduleId, categoryId)).rejects.toThrow();
  });
});