import bookmarkableModules from '../bookmarkableModules';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('bookmarkableModules service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch bookmarkable modules successfully', async () => {
    const mockResponse = {
      success: true,
      modules: [
        {
          id: 1,
          title: "Music",
          thumbnail: "https://example.com/thumbs/music.jpg",
          mob_image: null,
          order: 1,
          type: "music",
          bg_color: "#EFEFEF",
          logo_color: "#000000",
          header_content_value: "Enjoy your favorite tunes",
          header_content_type: "text",
          created_at: "2025-06-15T10:00:00Z",
          updated_at: "2025-06-16T10:00:00Z",
          slug: "music"
        },
        {
          id: 2,
          title: "Podcasts",
          thumbnail: "https://example.com/thumbs/podcasts.jpg",
          mob_image: "https://example.com/thumbs/podcasts-mobile.jpg",
          order: 2,
          type: "podcast",
          bg_color: "#FFFFFF",
          logo_color: "#333333",
          header_content_value: "Top trending podcasts",
          header_content_type: "text",
          created_at: "2025-06-14T08:30:00Z",
          updated_at: "2025-06-16T09:00:00Z",
          slug: "podcasts"
        },
        {
          id: 3,
          title: "Videos",
          thumbnail: "https://example.com/thumbs/videos.jpg",
          mob_image: null,
          order: 3,
          type: "video",
          bg_color: "#F0F0F0",
          logo_color: "#111111",
          header_content_value: "Explore videos by creators",
          header_content_type: "text",
          created_at: "2025-06-13T12:00:00Z",
          updated_at: "2025-06-15T18:00:00Z",
          slug: "videos"
        }
      ]
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    const result = await bookmarkableModules();
    
    expect(getMock).toHaveBeenCalledWith('modules/bookmark-able', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(bookmarkableModules()).rejects.toThrow();
  });
});