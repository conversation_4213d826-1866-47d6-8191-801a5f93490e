import eventLocationListing from '../eventLocationListing';
import { secureInstance } from '../../instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as eventLocationModule from '@/types/schemas/eventLocationListing';

jest.mock('../../instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn()
  }
}));

const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('eventLocationListing service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch event locations successfully', async () => {
    const mockResponse = {
      data: [
        { id: 1, name: 'Location 1', city: 'City 1' },
        { id: 2, name: 'Location 2', city: 'City 2' }
      ]
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(eventLocationModule.eventLocationListingResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await eventLocationListing(moduleId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/events`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Something went wrong']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    await expect(eventLocationListing(moduleId)).rejects.toThrow('Something went wrong');
  });
});