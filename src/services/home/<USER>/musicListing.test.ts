import musicListing from '../musicListing';
import { secureInstance } from '../../instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';

jest.mock('../../instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn()
  }
}));

const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('musicListing service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch music with default page', async () => {
    const mockResponse = {
      success: true,
      module: {
        id: 1,
        title: "Music Module",
        thumbnail: "https://example.com/thumbs/music-module.jpg",
        mob_image: null,
        order: 1,
        type: "music",
        bg_color: "#FAFAFA",
        logo_color: "#333333",
        header_content_value: "Top music collections by category",
        header_content_type: "text",
        created_at: "2025-06-15T10:00:00Z",
        updated_at: "2025-06-15T12:00:00Z",
        slug: "music-module"
      },
      categories: [
        {
          id: 1,
          module_id: 1,
          name: "Pop Hits",
          layout: "grid",
          created_at: "2025-06-10T10:00:00Z",
          updated_at: "2025-06-10T11:00:00Z",
          music: [
            {
              id: 201,
              category_id: 1,
              tag_id: null,
              title: "Summer Vibes",
              module_id: 1,
              audio_link: "https://example.com/audio/summer-vibes.mp3",
              thumbnail: "https://example.com/thumbs/summer-vibes.jpg",
              created_at: "2025-06-11T08:00:00Z",
              updated_at: "2025-06-11T09:00:00Z",
              previous: null,
              next: 202,
              is_liked: true,
              is_bookmarked: false,
              comments: []
            },
            {
              id: 202,
              category_id: 1,
              tag_id: null,
              title: "Night Drive",
              module_id: 1,
              audio_link: "https://example.com/audio/night-drive.mp3",
              thumbnail: "https://example.com/thumbs/night-drive.jpg",
              created_at: "2025-06-12T08:00:00Z",
              updated_at: "2025-06-12T09:00:00Z",
              previous: 201,
              next: null,
              is_liked: false,
              is_bookmarked: true,
              comments: []
            }
          ]
        }
      ]
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    const result = await musicListing(moduleId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/music`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Something went wrong']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    await expect(musicListing(moduleId)).rejects.toThrow();
  });
});