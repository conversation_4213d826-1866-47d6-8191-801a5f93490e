import musicDetail from '../musicDetail';
import { secureInstance } from '../../instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';

jest.mock('../../instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn()
  }
}));

const mockedSecureInstance = secureInstance as jest.MockedFunction<typeof secureInstance>;

describe('musicDetail service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const musicId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch music detail successfully', async () => {
    const mockResponse = {
        id: 1,
        category_id: 1,
        tag_id: 1,
        title: 'Test Music',
        module_id: 1,
        audio_link: 'https://example.com/audio.mp3',
        thumbnail: 'https://example.com/thumbnail.jpg',
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        previous: 1,
        next: 1,
        is_liked: true,
        is_bookmarked: true,
        comments: [],
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    const result = await musicDetail(moduleId, musicId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/music/${musicId}`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Something went wrong']
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    await expect(musicDetail(moduleId, musicId)).rejects.toThrow();
  });
});