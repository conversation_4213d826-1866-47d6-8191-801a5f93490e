import newsDetail from '../newsDetail';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('newsDetail service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  const newsId = 456;
  const sourceId = 789;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch news detail successfully', async () => {
    const mockResponse = {
        id: 4,
        external_id: null,
        news_source_id: null,
        module_id: 8,
        title: "Tech Conference 2025 Highlights",
        description: "The most anticipated tech event...",
        url: "https://news.example.com/article",
        author: "<PERSON>",
        thumbnail: "https://example.com/news.jpg",
        created_at: "2025-06-14T11:00:00Z",
        updated_at: "2025-06-14T12:00:00Z",
        is_liked: false,
        is_bookmarked: false,
        comments: []
    };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await newsDetail(moduleId, sourceId, newsId);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/news-sources/${sourceId}/news/${newsId}`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(newsDetail(moduleId, newsId)).rejects.toThrow();
  });
});