import newsSources from '../newsSources';
import { secureInstance } from '../../instance';
import { storage } from '@/App';

jest.mock('../../instance');
jest.mock('@/App');

describe('newsSources service', () => {
  const mockToken = 'test-token';
  const moduleId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  it('should fetch news sources successfully', async () => {
    const mockResponse = {
      success: true,
      module: {
        id: 1,
        title: "News Module",
        thumbnail: "https://example.com/thumbs/news-module.jpg",
        mob_image: null,
        order: 1,
        type: "news",
        bg_color: "#FFFFFF",
        logo_color: "#000000",
        header_content_value: "Latest news from trusted sources",
        header_content_type: "text",
        created_at: "2025-06-15T10:00:00Z",
        updated_at: "2025-06-16T12:00:00Z",
        slug: "news-module"
      },
      news_sources: [
        {
          id: 101,
          external_id: 201,
          module_id: 1,
          name: "TechCrunch",
          url: "https://techcrunch.com",
          base_href: "/tech",
          thumbnail: "https://example.com/thumbs/techcrunch.jpg",
          created_at: "2025-06-15T10:00:00Z",
          updated_at: "2025-06-15T12:00:00Z"
        },
        {
          id: 102,
          external_id: 202,
          module_id: 1,
          name: "BBC News",
          url: "https://bbc.com/news",
          base_href: "/bbc",
          thumbnail: "https://example.com/thumbs/bbc.jpg",
          created_at: "2025-06-14T09:00:00Z",
          updated_at: "2025-06-14T10:30:00Z"
        }
      ]
    }
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await newsSources(moduleId);
    
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/news-sources`, { method: 'get' });
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = { errors: ['Error'] };
    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(errorResponse)
    });
    (secureInstance as jest.Mock).mockReturnValue({ get: getMock });

    await expect(newsSources(moduleId)).rejects.toThrow();
  });
});