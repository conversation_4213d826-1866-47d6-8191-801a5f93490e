import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { z } from "zod";
import { conversationResponseSchema, conversationSchema } from "@/types/schemas/conversationSchema";


export default async (moduleId: number) => {
    var url = `ask-akina/conversations`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    console.log("Conversation response::", response )
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return conversationResponseSchema.parse(response)
}