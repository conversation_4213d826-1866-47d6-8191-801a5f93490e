import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { z } from "zod";
import { conversationSchema } from "@/types/schemas/conversationSchema";


export default async (moduleId: number, conversationId?: number) => {
    var url = `ask-akina/conversations/${conversationId}`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    console.log("Detail::", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return conversationSchema.parse(response)
}