import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { likeDislikeSchema } from "@/types/schemas/likeDislikeSchema";


export default async (moduleId: number, messageId: string, data: any) => {
    var url = `ask-akina/messages/${messageId}/like`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).patch(url, {method: 'patch', json: data}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return likeDislikeSchema.parse(response)
}