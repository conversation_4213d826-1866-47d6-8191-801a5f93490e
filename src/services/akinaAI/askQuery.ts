import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";

interface StreamToken {
    index: number;
    token: {
        id: number;
        text: string;
        logprob: number;
        special: boolean;
    };
    generated_text: string | null;
    details: any | null;
    remaining_tokens?: boolean;
}

interface StreamingOptions {
    onToken?: (token: StreamToken) => void;
    onTextChunk?: (text: string) => void;
    onComplete?: (fullText: string) => void;
    onError?: (error: Error) => void;
    abortController?: AbortController;
}

// Function to parse Server-Sent Events format
function parseSSEStream(buffer: string): { parsedObjects: any[], remainingBuffer: string } {
    const parsedObjects: any[] = [];
    let remainingBuffer = buffer;
    
    // Split by double newlines (SSE event separator)
    const events = remainingBuffer.split(/\n\s*\n/);
    
    // Keep the last part as remaining buffer if it doesn't end with double newline
    const hasCompleteLastEvent = buffer.endsWith('\n\n') || buffer.endsWith('\r\n\r\n');
    remainingBuffer = hasCompleteLastEvent ? '' : (events.pop() || '');
    
    for (const event of events) {
        if (!event.trim()) continue;
        
        const lines = event.split(/\r?\n/);
        let eventType = '';
        let eventData = '';
        let eventId = '';
        
        for (const line of lines) {
            const colonIndex = line.indexOf(':');
            if (colonIndex === -1) continue;
            
            const field = line.substring(0, colonIndex).trim();
            const value = line.substring(colonIndex + 1).trim();
            
            switch (field) {
                case 'event':
                    eventType = value;
                    break;
                case 'data':
                    eventData = value;
                    break;
                case 'id':
                    eventId = value;
                    break;
            }
        }
        
        // Only process token events with data
        if (eventType === 'token' && eventData) {
            try {
                const parsed = JSON.parse(eventData);
                parsedObjects.push(parsed);
            } catch (parseError) {
                console.warn('Failed to parse SSE data:', eventData, parseError);
            }
        }
    }
    
    return { parsedObjects, remainingBuffer };
}

export default async (data: any, options: StreamingOptions = {}): Promise<any> => {
    const { onToken, onTextChunk, onComplete, onError, abortController } = options;
    const url = `${process.env.AI_LABMDA_URL ? process.env.AI_LABMDA_URL : ''}`;
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

    return new Promise((resolve, reject) => {
        try {
            let requestBody: string;
            let requestContentType = 'application/json';
            
            if (typeof data === 'string') {
                requestBody = data;
            } else if (data instanceof FormData) {
                requestBody = data as any;
                requestContentType = 'multipart/form-data';
            } else {
                try {
                    requestBody = JSON.stringify(data, null, 0);
                } catch (stringifyError) {
                    console.error('Failed to stringify data:', stringifyError);
                    const error = new Error('Invalid data format - cannot serialize to JSON');
                    onError?.(error);
                    reject(error);
                    return;
                }
            }

            const xhr = new XMLHttpRequest();
            let fullText = '';
            let buffer = '';
            let lastProcessedIndex = 0;

            xhr.onreadystatechange = () => {
                if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
                    if (xhr.status !== 200) {
                        const errorMessage = `HTTP error! status: ${xhr.status} - ${xhr.statusText}`;
                        const error = new Error(errorMessage);
                        onError?.(error);
                        reject(error);
                        return;
                    }
                }
                
                if (xhr.readyState === XMLHttpRequest.LOADING || xhr.readyState === XMLHttpRequest.DONE) {
                    // Get new data since last processing
                    const newData = xhr.responseText.slice(lastProcessedIndex);
                    lastProcessedIndex = xhr.responseText.length;
                    
                    if (newData) {
                        buffer += newData;
                        
                        // Parse SSE events
                        const { parsedObjects, remainingBuffer } = parseSSEStream(buffer);
                        buffer = remainingBuffer;
                        
                        // Process each parsed token
                        for (const tokenData of parsedObjects) {
                            try {
                                // Validate it's a StreamToken
                                if (tokenData && typeof tokenData === 'object' && 
                                    'index' in tokenData && 'token' in tokenData) {
                                    
                                    // Call token callback
                                    onToken?.(tokenData as StreamToken);
                                    
                                    // Extract text and call text callback
                                    const text = tokenData.token?.text;
                                    if (text) {
                                        fullText += text;
                                        onTextChunk?.(text);
                                    }
                                }
                            } catch (processError) {
                                console.warn('Failed to process token data:', tokenData, processError);
                            }
                        }
                    }
                }
                
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    // Process any remaining buffer one final time
                    if (buffer.trim()) {
                        // Force process remaining buffer by adding double newline
                        const { parsedObjects } = parseSSEStream(buffer + '\n\n');
                        
                        for (const tokenData of parsedObjects) {
                            try {
                                if (tokenData && typeof tokenData === 'object' && 
                                    'index' in tokenData && 'token' in tokenData) {
                                    
                                    onToken?.(tokenData as StreamToken);
                                    
                                    const text = tokenData.token?.text;
                                    if (text) {
                                        fullText += text;
                                        onTextChunk?.(text);
                                    }
                                }
                            } catch (processError) {
                                console.warn('Failed to process final token data:', tokenData, processError);
                            }
                        }
                    }
                    
                    onComplete?.(fullText);
                    
                    resolve({
                        ok: xhr.status >= 200 && xhr.status < 300,
                        status: xhr.status,
                        statusText: xhr.statusText,
                        headers: new Headers(xhr.getAllResponseHeaders()),
                        text: () => Promise.resolve(fullText),
                        json: () => Promise.resolve({ generated_text: fullText })
                    });
                }
            };

            xhr.onerror = () => {
                const error = new Error('Network error occurred');
                console.error('XMLHttpRequest error:', error);
                onError?.(error);
                reject(error);
            };

            xhr.ontimeout = () => {
                const error = new Error('Request timeout');
                console.error('XMLHttpRequest timeout:', error);
                onError?.(error);
                reject(error);
            };

            if (abortController) {
                abortController.signal.addEventListener('abort', () => {
                    xhr.abort();
                    const error = new Error('Request aborted');
                    onError?.(error);
                    reject(error);
                });
            }

            xhr.open('POST', url, true);
            
            if (!(data instanceof FormData)) {
                xhr.setRequestHeader('Content-Type', requestContentType);
            }
            try {
                xhr.setRequestHeader('Transfer-Encoding', 'chunked');
                xhr.setRequestHeader('Connection', 'keep-alive');
                xhr.setRequestHeader('Authorization', `Bearer ${token}`)

            } catch (e) {
                console.warn('Could not set transfer encoding headers:', e);
            }
            // console.log("Token:", )

            xhr.send(requestBody);
            
        } catch (error) {
            console.error('Streaming error:', error);
            onError?.(error as Error);
            reject(error);
        }
    });
};