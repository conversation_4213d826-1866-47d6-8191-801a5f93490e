import startConversation from '../startConversation';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as conversationModule from '@/types/schemas/conversationSchema';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('startConversation service', () => {
  const moduleId = 123;
  const inputData = { 
    title: 'New Conversation',
    message: 'Hello AI'
  };
  
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on successful request', async () => {
    const mockResponse = {
      id: 1,
      title: 'New Conversation',
      created_at: '2023-01-01T00:00:00Z'
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');
    
    jest.spyOn(conversationModule.startConversationSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await startConversation(moduleId, inputData);

    expect(secureInstance).toHaveBeenCalledWith('fake_token');
    expect(postMock).toHaveBeenCalledWith(
      `modules/${moduleId}/akina-ai/conversations`, 
      { method: 'post', json: inputData }
    );
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const mockResponse = {
      errors: ['Invalid input']
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');

    await expect(startConversation(moduleId, inputData)).rejects.toThrow();
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');
    
    jest.spyOn(conversationModule.startConversationSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(startConversation(moduleId, inputData)).rejects.toThrow('Validation error');
  });
});