import getConversations from '../getConversations';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as conversationModule from '@/types/schemas/conversationSchema';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('getConversations service', () => {
  const moduleId = 123;
  
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on successful request', async () => {
    const mockResponse = {
      conversations: [
        { id: 1, title: 'Conversation 1' },
        { id: 2, title: 'Conversation 2' }
      ]
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');
    
    jest.spyOn(conversationModule.conversationResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getConversations(moduleId);

    expect(secureInstance).toHaveBeenCalledWith('fake_token');
    expect(getMock).toHaveBeenCalledWith(`modules/${moduleId}/akina-ai/conversations`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const mockResponse = {
      errors: ['Something went wrong']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');

    await expect(getConversations(moduleId)).rejects.toThrow();
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');
    
    jest.spyOn(conversationModule.conversationResponseSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(getConversations(moduleId)).rejects.toThrow('Validation error');
  });
});