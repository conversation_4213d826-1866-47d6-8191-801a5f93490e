import conversationDetail from '../conversationDetail';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as conversationModule from '@/types/schemas/conversationSchema';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('conversationDetail service', () => {
  const moduleId = 123;
  const conversationId = 456;
  
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return parsed response on successful request', async () => {
    const mockResponse = {
      id: conversationId,
      title: 'Test Conversation',
      messages: [
        { id: 1, content: 'Hello', sender: 'user' },
        { id: 2, content: 'Hi there!', sender: 'ai' }
      ]
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');
    
    jest.spyOn(conversationModule.conversationSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await conversationDetail(moduleId, conversationId);

    expect(secureInstance).toHaveBeenCalledWith('fake_token');
    expect(getMock).toHaveBeenCalledWith(
      `modules/${moduleId}/akina-ai/conversations/${conversationId}`, 
      { method: 'get' }
    );
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const mockResponse = {
      errors: ['Conversation not found']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');

    await expect(conversationDetail(moduleId, conversationId)).rejects.toThrow();
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    (storage.getString as jest.Mock).mockReturnValue('fake_token');
    
    jest.spyOn(conversationModule.conversationSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(conversationDetail(moduleId, conversationId)).rejects.toThrow('Validation error');
  });
});