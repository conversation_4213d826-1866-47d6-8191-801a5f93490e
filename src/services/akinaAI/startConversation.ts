import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { z } from "zod";
import { startConversationSchema } from "@/types/schemas/conversationSchema";


export default async (moduleId: number, data: any) => {
    var url = `modules/${moduleId}/akina-ai/conversations`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).post(url, {method: 'post', json: data,}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return startConversationSchema.parse(response)
}