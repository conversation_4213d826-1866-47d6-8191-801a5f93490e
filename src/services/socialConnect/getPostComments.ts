import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { socialConnectUsersResponseSchema } from "@/types/schemas/socialConnectUsers";
import { socialConnectPostsResponseSchema } from "@/types/schemas/socialConnectPost";
import { scPostCommentListingResponseSchema } from "@/types/schemas/socialConnectPostComment";


export default async (postId: number) => {
    var url = `social-connect/posts/${postId}/comments`
    console.log("URL::", url)
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    try {
        return scPostCommentListingResponseSchema.parse(response);

    } catch(e) {
        console.log("ERROR:::", e)
    }
}