import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { basicSchema } from '@/types/schemas/basic';


export default async (postId: number) => {
    const url = `social-connect/posts/${postId}`;
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).delete(url, {method: 'delete'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return basicSchema.parse(response);
}