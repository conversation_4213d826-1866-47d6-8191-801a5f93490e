
import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { socialConnectPostLikeSchema } from '@/types/schemas/socialConnectPostLike';
import { statusBasicSchema } from '@/types/schemas/statusBasicSchema';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (postId?: number) => {
    let url = `social-connect/posts/${postId}/like`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).post(url, {method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return socialConnectPostLikeSchema.parse(response);
};
