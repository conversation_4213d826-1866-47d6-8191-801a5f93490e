import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { socialConnectUserPost } from '@/types/schemas/socialConnectUserPost';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import { Platform } from 'react-native';
import Upload from 'react-native-background-upload';

let currentUploadId: string | null = null;

export const cancelUpload = async () => {
    if (currentUploadId) {
        let status = true;
        try {
            await Upload.cancelUpload(currentUploadId);
            console.log('Upload cancelled');
        } catch (err) {
            console.error('Failed to cancel upload:', err);
            status = false;
        } finally {
            return status;
        }
    }
};

export default async (data: any, postId?: number, onProgress?: (progress: number) => void) => {
    const token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN);
    const prefixUrl = `${process.env.API_URL ? process.env.API_URL : ''}`;

    const apiUrl = `${prefixUrl}social-connect/posts/${postId}`;
    const hasMedia = !!data._parts.find(([key]) => key === 'image_file');
    if (hasMedia) {

        const textualParts = data._parts.filter(([key, value]) => key != 'image_file');
        const mediaPart = data._parts.find(([key]) => key === 'image_file');
        
        const parameters = {};
        textualParts.forEach(([key, value]) => {
            parameters[key] = value;
        })

        const file = mediaPart[1];

        let path;
        if (file.uri.startsWith('https://')) {
            path = file.uri;
        } else {
            path = Platform.OS === 'ios' ? `file://${file.uri}` : `${file.uri.replace('file://', '')}`;
        }

        const options = {
            url: apiUrl,
            path,
            method: 'POST',
            type: 'multipart',
            field: 'image_file',
            headers: {
                Authorization: `Bearer ${token}`,
            },
            parameters,
        };

        return new Promise((resolve, reject) => {
            Upload.startUpload(options)
            .then(uploadId => {

                currentUploadId = uploadId;

                Upload.addListener('progress', uploadId, (data) => {
                    const progress = data.progress / 100;
                    console.log(`Upload progress: ${progress}`);
                    if (onProgress) onProgress(progress);
                });

                Upload.addListener('error', uploadId, (data) => {
                    console.error('Upload error:', data.error);
                    reject(new Error(data.error));
                });

                Upload.addListener('completed', uploadId, (data) => {
                    console.log('Upload completed:', data.responseBody);
                    try {
                        const responseJson = JSON.parse(data.responseBody);
                        resolve(responseJson);
                    } catch (e) {
                        reject(new Error('Failed to parse server response'));
                    }
                });
            })
            .catch(err => {
                console.error('Upload initiation error:', err);
                reject(err);
            });
        });
    } else {
        if (onProgress) onProgress(1);
        const response: any = await secureInstance(token).post(`social-connect/posts/${postId}`, {
            method: 'put',
            body: data,
        }).json();
        
        if (response.errors != null) {
            throw new Error(parseServerError(response));
        }
        
        return socialConnectUserPost.parse(response);
    }
};
