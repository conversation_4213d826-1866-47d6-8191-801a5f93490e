import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { socialConnectUsersResponseSchema } from "@/types/schemas/socialConnectUsers";
import { socialConnectPostsResponseSchema } from "@/types/schemas/socialConnectPost";


export default async (pageNumber: number) => {
    var url = `social-connect/posts`
    if (pageNumber != 1) {
        url = `${url}?page=${pageNumber}`
    }
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return socialConnectPostsResponseSchema.parse(response);
}