import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { basicSchema } from '@/types/schemas/basic';
import { registerSchema } from '@/types/schemas/register';
import { socialConnectUserPost } from '@/types/schemas/socialConnectUserPost';
import { updateProfileSchema } from '@/types/schemas/updateProfile';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';
import { Platform } from 'react-native';
import Upload from 'react-native-background-upload';

// export default async (data: any,  onProgress?: (progress: number) => void) => {
//     let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

// 	const response: any = await secureInstance(token).post('social-connect/posts',{method: 'post', body: data, onUploadProgress: (progress) =>{
//         if (onProgress != null) {
//             onProgress(Math.round(progress.percent))
//         }
//     } 
//     }).json()
//     console.log("Add Post RESPONSE::::", response)
//     if (response.errors != null) {
//         throw new Error(parseServerError(response))
//     }
// 	return basicSchema.parse(response);
// };

let currentUploadId: string | null = null;

export const cancelUpload = async () => {
    if (currentUploadId) {
        let status = true;
        try {
            await Upload.cancelUpload(currentUploadId);
            console.log('Upload cancelled');
        } catch (err) {
            console.error('Failed to cancel upload:', err);
            status = false;
        } finally {
            return status;
        }
    }
};

export default async (data: any, onProgress?: (progress: number) => void) => {
    const token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN);
    const prefixUrl = `${process.env.API_URL ? process.env.API_URL : ''}`;

    const apiUrl = `${prefixUrl}social-connect/posts`;
    const hasMedia = !!data._parts.find(([key]) => key === 'image_file');
    if (hasMedia) {

        const textualParts = data._parts.filter(([key, value]) => key != 'image_file');
        const mediaPart = data._parts.find(([key]) => key === 'image_file');
        
        const parameters = {};
        textualParts.forEach(([key, value]) => {
            parameters[key] = value;
        })

        const file = mediaPart[1];

        const options = {
            url: apiUrl,
            path: Platform.OS === 'ios' ? `file://${file.uri}` : `${file.uri.replace('file://', '')}`,
            method: 'POST',
            type: 'multipart',
            field: 'image_file',
            headers: {
                Authorization: `Bearer ${token}`,
            },
            parameters,
        };

        return new Promise((resolve, reject) => {
            Upload.startUpload(options)
            .then(uploadId => {

                currentUploadId = uploadId;

                Upload.addListener('progress', uploadId, (data) => {
                    const progress = data.progress / 100;
                    console.log(`Upload progress: ${progress}`);
                    if (onProgress) onProgress(progress);
                });

                Upload.addListener('error', uploadId, (data) => {
                    console.error('Upload error:', data.error);
                    reject(new Error(data.error));
                });

                Upload.addListener('completed', uploadId, (data) => {
                    console.log('Upload completed:', data.responseBody);
                    try {
                        const responseJson = JSON.parse(data.responseBody);
                        resolve(responseJson);
                    } catch (e) {
                        reject(new Error('Failed to parse server response'));
                    }
                });
            })
            .catch(err => {
                console.error('Upload initiation error:', err);
                reject(err);
            });
        });
    } else {
        // For non-file uploads, you can use your regular approach
        // If you still want to use ky for this:
        if (onProgress) onProgress(1);
        const response: any = await secureInstance(token).post('social-connect/posts', {
            body: data,
        }).json();
        
        console.log("Add Post RESPONSE::::", response);
        
        if (response.errors != null) {
            throw new Error(parseServerError(response));
        }
        
        return socialConnectUserPost.parse(response);
    }
};

