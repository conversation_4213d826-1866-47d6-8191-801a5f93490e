import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { socialConnectPostCommentSchema } from '@/types/schemas/socialConnectPostComment';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async ( postId?: number, commentId?: number, replyId?: number, data?: any) => {
    let url = `social-connect/posts/${postId}/comments/${commentId}/replies/${replyId}`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).patch(url, {json: data, method: 'patch'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return socialConnectPostCommentSchema.parse(response);
};
