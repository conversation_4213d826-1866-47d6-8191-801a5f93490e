import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { socialConnectPostsResponseSchema } from "@/types/schemas/socialConnectPost";

export default async (userId: number, pageNumber: number) => {
    var url = `social-connect/posts/by-a-user/${userId}`;
    if (pageNumber != 1) {
        url = `${url}?page=${pageNumber}`
    }
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    // try {
        return socialConnectPostsResponseSchema.parse(response);
    // } catch(e) {
    //     console.log("Error:::", e)
    // }
}