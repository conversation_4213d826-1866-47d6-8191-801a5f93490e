import { storage } from '@/App';
import { instance, secureInstance } from '@/services/instance';
import { basicSchema } from '@/types/schemas/basic';
import { blogListingResponseSchema } from '@/types/schemas/blogListing';
import { postDetailSchema } from '@/types/schemas/postDetail';
import { socialConnectPostCommentSchema } from '@/types/schemas/socialConnectPostComment';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async (postId?: number, commentId?: number, data?: any) => {
    let url = `social-connect/posts/${postId}/comments/${commentId}/replies`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
	const response: any = await secureInstance(token).post(url, {json: data, method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return socialConnectPostCommentSchema.parse(response);
};
