import deletePost from '../deletePost';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as basicModule from '@/types/schemas/basic';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('deletePost service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should delete post successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Post deleted successfully'
    };

    const deleteMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      delete: deleteMock
    });

    jest.spyOn(basicModule.basicSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await deletePost(postId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(deleteMock).toHaveBeenCalledWith(`social-connect/posts/${postId}`, { method: 'delete' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Post not found']
    };

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(deletePost(postId)).rejects.toThrow('Post not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(basicModule.basicSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(deletePost(postId)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(deletePost(postId)).rejects.toThrow('Network error');
  });
});