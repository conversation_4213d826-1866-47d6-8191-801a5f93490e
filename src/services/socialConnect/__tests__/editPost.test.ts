import editPost from '../editPost';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as editPostModule from '@/types/schemas/socialConnectUserPost';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('editPost service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  const mockData = {
    content: 'Updated post content',
    media: [{ uri: 'file://updated.jpg', type: 'image/jpeg', name: 'updated.jpg' }]
  };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should edit post successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Post updated successfully',
      post: {
        id: postId,
        content: 'Updated post content',
        media: ['https://example.com/media/updated.jpg']
      }
    };

    const putMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      put: putMock
    });

    jest.spyOn(editPostModule.socialConnectUserPost, 'parse')
      .mockReturnValue(mockResponse);

    const result = await editPost(postId, mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(putMock).toHaveBeenCalledWith(`social-connect/posts/${postId}`, { 
      json: mockData, 
      method: 'put' 
    });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Post not found']
    };

    mockedSecureInstance.mockReturnValue({
      put: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(editPost(postId, mockData)).rejects.toThrow('Post not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      put: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(editPostModule.socialConnectUserPost, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(editPost(postId, mockData)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      put: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(editPost(postId, mockData)).rejects.toThrow('Network error');
  });
});