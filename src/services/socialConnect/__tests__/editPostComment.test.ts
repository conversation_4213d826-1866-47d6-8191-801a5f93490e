import editPostComment from '../editPostComment';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as commentModule from '@/types/schemas/socialConnectPostComment';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('editPostComment service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  const commentId = 456;
  const mockData = { 
    content: 'Updated comment content'
  };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should edit comment successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Comment updated successfully',
      comment: {
        id: commentId,
        content: 'Updated comment content',
        user: {
          id: 101,
          name: 'Test User'
        }
      }
    };

    const patchMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      patch: patchMock
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await editPostComment(postId, commentId, mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(patchMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/comments/${commentId}`, { 
      json: mockData, 
      method: 'patch' 
    });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Comment not found']
    };

    mockedSecureInstance.mockReturnValue({
      patch: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(editPostComment(postId, commentId, mockData)).rejects.toThrow('Comment not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      patch: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(editPostComment(postId, commentId, mockData)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      patch: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(editPostComment(postId, commentId, mockData)).rejects.toThrow('Network error');
  });
});