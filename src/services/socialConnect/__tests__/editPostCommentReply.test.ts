import editPostCommentReply from '../editPostCommentReply';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as commentModule from '@/types/schemas/socialConnectPostComment';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('editPostCommentReply service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  const commentId = 456;
  const replyId = 789;
  const mockData = { 
    content: 'Updated reply content'
  };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should edit comment reply successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Reply updated successfully',
      reply: {
        id: replyId,
        content: 'Updated reply content',
        user: {
          id: 101,
          name: 'Test User'
        }
      }
    };

    const patchMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      patch: patchMock
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await editPostCommentReply(postId, commentId, replyId, mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(patchMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/comments/${commentId}/replies/${replyId}`, { 
      json: mockData, 
      method: 'patch' 
    });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Reply not found']
    };

    mockedSecureInstance.mockReturnValue({
      patch: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(editPostCommentReply(postId, commentId, replyId, mockData)).rejects.toThrow('Reply not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      patch: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(editPostCommentReply(postId, commentId, replyId, mockData)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      patch: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(editPostCommentReply(postId, commentId, replyId, mockData)).rejects.toThrow('Network error');
  });
});