import addPostCommentReply from '../addPostCommentReply';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as commentModule from '@/types/schemas/socialConnectPostComment';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('addPostCommentReply service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  const commentId = 456;
  const mockData = { 
    content: 'This is a test reply'
  };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should add comment reply successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Reply added successfully',
      reply: {
        id: 1,
        content: 'This is a test reply',
        user: {
          id: 101,
          name: 'Test User'
        }
      }
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await addPostCommentReply(postId, commentId, mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(postMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/comments/${commentId}/replies`, { 
      json: mockData, 
      method: 'post' 
    });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Comment not found']
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(addPostCommentReply(postId, commentId, mockData)).rejects.toThrow('Comment not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(addPostCommentReply(postId, commentId, mockData)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(addPostCommentReply(postId, commentId, mockData)).rejects.toThrow('Network error');
  });
});