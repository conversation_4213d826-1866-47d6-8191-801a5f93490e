import likeUnlikePost from '../likeUnlikePost';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as likeModule from '@/types/schemas/socialConnectPostLike';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('likeUnlikePost service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should like/unlike post successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Post liked successfully',
      is_liked: true,
      likes_count: 10
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    });

    jest.spyOn(likeModule.socialConnectPostLikeSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await likeUnlikePost(postId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(postMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/like`, { 
      method: 'post' 
    });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Post not found']
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(likeUnlikePost(postId)).rejects.toThrow('Post not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(likeModule.socialConnectPostLikeSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(likeUnlikePost(postId)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(likeUnlikePost(postId)).rejects.toThrow('Network error');
  });
});