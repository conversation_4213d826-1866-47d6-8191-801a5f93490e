import bookmarkUnbookmarkPost from '../bookmarkUnbookmarkPost';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as bookmarkModule from '@/types/schemas/socialConnectPostLike';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('bookmarkUnbookmarkPost service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should bookmark/unbookmark post successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Post bookmarked successfully',
      is_bookmarked: true
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    });

    jest.spyOn(bookmarkModule.socialConnectPostLikeSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await bookmarkUnbookmarkPost(postId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(postMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/bookmark`, { 
      method: 'post' 
    });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Post not found']
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(bookmarkUnbookmarkPost(postId)).rejects.toThrow('Post not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(bookmarkModule.socialConnectPostLikeSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(bookmarkUnbookmarkPost(postId)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(bookmarkUnbookmarkPost(postId)).rejects.toThrow('Network error');
  });
});