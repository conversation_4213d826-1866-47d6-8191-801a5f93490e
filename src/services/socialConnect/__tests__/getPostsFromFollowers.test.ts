import getPostsFromFollowers from '../getPostsFromFollowers';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as postsModule from '@/types/schemas/socialConnectPost';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('getPostsFromFollowers service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch posts from followers with page=1', async () => {
    const mockResponse = {
      data: [
        { id: 1, content: 'Post 1', user: { id: 101, name: 'User 1' } },
        { id: 2, content: 'Post 2', user: { id: 102, name: 'User 2' } }
      ],
      current_page: 1,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(postsModule.socialConnectPostsResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getPostsFromFollowers(1);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('social-connect/posts/from-followers', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should fetch posts from followers with page>1', async () => {
    const mockResponse = {
      data: [
        { id: 3, content: 'Post 3', user: { id: 103, name: 'User 3' } },
        { id: 4, content: 'Post 4', user: { id: 104, name: 'User 4' } }
      ],
      current_page: 2,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(postsModule.socialConnectPostsResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getPostsFromFollowers(2);

    expect(getMock).toHaveBeenCalledWith('social-connect/posts/from-followers?page=2', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Failed to fetch posts from followers']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(getPostsFromFollowers(1)).rejects.toThrow('Failed to fetch posts from followers');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(postsModule.socialConnectPostsResponseSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(getPostsFromFollowers(1)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(getPostsFromFollowers(1)).rejects.toThrow('Network error');
  });
});