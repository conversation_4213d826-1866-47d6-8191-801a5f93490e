import getPostComments from '../getPostComments';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as commentsModule from '@/types/schemas/socialConnectPostComment';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('getPostComments service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch post comments with page=1', async () => {
    const mockResponse = {
      data: [
        { id: 1, content: 'Comment 1', user: { id: 101, name: 'User 1' } },
        { id: 2, content: 'Comment 2', user: { id: 102, name: 'User 2' } }
      ],
      current_page: 1,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(commentsModule.scPostCommentListingResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getPostComments(postId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/comments`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Comments not found']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(getPostComments(postId)).rejects.toThrow('Comments not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(commentsModule.scPostCommentListingResponseSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });
    const result = await getPostComments(postId);
    expect(result).toBeUndefined();
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(getPostComments(postId)).rejects.toThrow('Network error');
  });
});