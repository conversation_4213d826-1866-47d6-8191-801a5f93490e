import deletePostCommentReply from '../deletePostCommentReply';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as commentModule from '@/types/schemas/socialConnectPostComment';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('deletePostCommentReply service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  const commentId = 456;
  const replyId = 789;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should delete comment reply successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Reply deleted successfully'
    };

    const deleteMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      delete: deleteMock
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await deletePostCommentReply(postId, commentId, replyId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(deleteMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/comments/${commentId}/replies/${replyId}`, { method: 'delete' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Reply not found']
    };

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(deletePostCommentReply(postId, commentId, replyId)).rejects.toThrow('Reply not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(deletePostCommentReply(postId, commentId, replyId)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(deletePostCommentReply(postId, commentId, replyId)).rejects.toThrow('Network error');
  });
});