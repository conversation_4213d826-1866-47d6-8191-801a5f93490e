import deletePostComment from '../deletePostComment';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as commentModule from '@/types/schemas/socialConnectPostComment';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('deletePostComment service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  const commentId = 456;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should delete comment successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Comment deleted successfully'
    };

    const deleteMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      delete: deleteMock
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await deletePostComment(postId, commentId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(deleteMock).toHaveBeenCalledWith(`social-connect/posts/${postId}/comments/${commentId}`, { method: 'delete' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Comment not found']
    };

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(deletePostComment(postId, commentId)).rejects.toThrow('Comment not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(commentModule.socialConnectPostCommentSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(deletePostComment(postId,commentId)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      delete: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(deletePostComment(postId,commentId)).rejects.toThrow('Network error');
  });
});