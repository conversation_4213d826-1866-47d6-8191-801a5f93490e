import getPostDetail from '../getPostDetail';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as postDetailModule from "@/types/schemas/socialConnectPost";
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('getPostDetail service', () => {
  const mockToken = 'test-token';
  const postId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch post details successfully', async () => {
    const mockResponse = {
      post: {
        id: postId,
        content: 'Post content',
        user: {
          id: 101,
          name: 'User 1'
        },
        likes_count: 10,
        comments_count: 5,
        is_liked: true,
        media: ['https://example.com/image.jpg']
      }
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(postDetailModule.socialConnectPostSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getPostDetail(postId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`social-connect/posts/${postId}`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Post not found']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(getPostDetail(postId)).rejects.toThrow('Post not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(postDetailModule.socialConnectPostSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(getPostDetail(postId)).rejects.toThrow('Validation error');
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(getPostDetail(postId)).rejects.toThrow('Network error');
  });
});