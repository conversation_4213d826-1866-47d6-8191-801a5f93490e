import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";


export default async (postId: number) => {
    const url = `social-connect/posts/${postId}`;
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    } else if (response.status == false) {
        return response;
    }
    return socialConnectPostSchema.parse(response);
}