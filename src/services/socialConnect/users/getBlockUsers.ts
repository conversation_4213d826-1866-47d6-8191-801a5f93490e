import { secureInstance } from "../../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { socialConnectUsersResponseSchema } from "@/types/schemas/socialConnectUsers";
import { blockUserResponseSchema } from "@/types/schemas/user";

export default async () => {
    var url = `block-users`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    try {
        return blockUserResponseSchema.parse(response);
    } catch (e) {
        console.log("Error::", e)
    }
}