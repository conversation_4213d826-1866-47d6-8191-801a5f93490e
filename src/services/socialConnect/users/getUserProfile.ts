import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { followSchema } from '@/types/schemas/followResponseSchema';
import { profileResponseSchema } from '@/types/schemas/profile';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async ( userId?: number) => {
    console.log("User ID::", userId)
    let url = `social-connect/user-profile/${userId}`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, { method: 'get'}).json()
    console.log("Profile Response", response)
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    try {
        return profileResponseSchema.parse(response);
    } catch(e) {
        console.log("Error::", e)
    }
};
