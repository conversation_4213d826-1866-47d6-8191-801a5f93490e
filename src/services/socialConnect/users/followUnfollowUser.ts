import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { followSchema } from '@/types/schemas/followResponseSchema';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async ( data?: any) => {
    let url = `social-connect/follow-user`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

	const response: any = await secureInstance(token).post(url, {json: data, method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
	return followSchema.parse(response);
};
