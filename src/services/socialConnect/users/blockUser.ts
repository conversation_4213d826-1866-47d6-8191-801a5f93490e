import { storage } from '@/App';
import { secureInstance } from '@/services/instance';
import { basicSchema } from '@/types/schemas/basic';
import { followSchema } from '@/types/schemas/followResponseSchema';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import { parseServerError } from '@/utils/utility';

export default async ( userId?: number) => {
    let url = `block-users/${userId}`
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)

    const response: any = await secureInstance(token).post(url, {json: {}, method: 'post'}).json()
    if (response.errors != null) {
        throw new Error(parseServerError(response))
    }
    return basicSchema.parse(response);
};
