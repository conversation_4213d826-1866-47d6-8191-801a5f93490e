import getUserProfile from '../getUserProfile';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as profileModule from '@/types/schemas/profile';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('getUserProfile service', () => {
  const mockToken = 'test-token';
  const userId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch user profile successfully', async () => {
    const mockResponse = {
      user_profile: {
        id: userId,
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        profile_photo: 'https://example.com/photo.jpg'
      }
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(profileModule.profileResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getUserProfile(userId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`social-connect/user-profile/${userId}`, { method: 'get' });
    expect(profileModule.profileResponseSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['User not found']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(getUserProfile(userId)).rejects.toThrow('User not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should return undefined if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(profileModule.profileResponseSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    const result = await getUserProfile(userId);
    expect(result).toBeUndefined();
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(getUserProfile(userId)).rejects.toThrow('Network error');
    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
  });
});