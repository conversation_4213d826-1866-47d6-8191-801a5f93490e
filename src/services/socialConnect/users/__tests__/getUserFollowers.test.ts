import getUserFollowers from '../getUserFollowers';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as socialConnectUsersModule from '@/types/schemas/socialConnectUsers';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('getUserFollowers service', () => {
  const mockToken = 'test-token';
  const userId = 123;
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch user followers with page=1', async () => {
    const mockResponse = {
      data: [
        { id: 1, first_name: 'John', last_name: 'Doe', email: '<EMAIL>' },
        { id: 2, first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>' }
      ],
      current_page: 1,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getUserFollowers(userId, 1);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith(`social-connect/get-user-followers/${userId}`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should fetch user followers with page>1', async () => {
    const mockResponse = {
      data: [
        { id: 3, first_name: 'Bob', last_name: 'Johnson', email: '<EMAIL>' },
        { id: 4, first_name: 'Alice', last_name: 'Williams', email: '<EMAIL>' }
      ],
      current_page: 2,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await getUserFollowers(userId, 2);

    expect(getMock).toHaveBeenCalledWith(`social-connect/get-user-followers/${userId}?page=2`, { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['User not found']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(getUserFollowers(userId, 1)).rejects.toThrow('User not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(getUserFollowers(userId, 1)).rejects.toThrow('Validation error');
  });
});