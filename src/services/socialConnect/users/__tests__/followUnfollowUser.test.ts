import followUnfollowUser from '../followUnfollowUser';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as followModule from '@/types/schemas/followResponseSchema';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('followUnfollowUser service', () => {
  const mockToken = 'test-token';
  const mockData = { user_id: 123 };
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should follow/unfollow user successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'User followed successfully',
      is_followed: true
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    });

    jest.spyOn(followModule.followSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await followUnfollowUser(mockData);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(postMock).toHaveBeenCalledWith('social-connect/follow-user', { json: mockData, method: 'post' });
    expect(followModule.followSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['User not found']
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(followUnfollowUser(mockData)).rejects.toThrow('User not found');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(followModule.followSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(followUnfollowUser(mockData)).rejects.toThrow('Validation error');
  });
});