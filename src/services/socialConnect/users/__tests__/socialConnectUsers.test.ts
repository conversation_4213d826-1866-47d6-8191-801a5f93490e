import socialConnectUsers from '../socialConnectUsers';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as socialConnectUsersModule from '@/types/schemas/socialConnectUsers';
import { parseServerError } from '@/utils/utility';

jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));
jest.mock('@/utils/utility', () => ({
  parseServerError: jest.fn(response => response.errors.join(', ')),
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('socialConnectUsers service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
    console.log = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch users with null searchData and page=1', async () => {
    const mockResponse = {
      data: [
        { id: 1, first_name: 'John', last_name: 'Doe', email: '<EMAIL>' },
        { id: 2, first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>' }
      ],
      current_page: 1,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await socialConnectUsers(null, 1);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('social-connect/users', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should fetch users with null searchData and page>1', async () => {
    const mockResponse = {
      data: [
        { id: 3, first_name: 'Bob', last_name: 'Johnson', email: '<EMAIL>' },
        { id: 4, first_name: 'Alice', last_name: 'Williams', email: '<EMAIL>' }
      ],
      current_page: 2,
      last_page: 2
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await socialConnectUsers(null, 2);

    expect(getMock).toHaveBeenCalledWith('social-connect/users?page=2', { method: 'get' });
    expect(result).toEqual(mockResponse);
  });

  it('should search users with searchData and page=1', async () => {
    const searchData = { query: 'John' };
    const mockResponse = {
      data: [
        { id: 1, first_name: 'John', last_name: 'Doe', email: '<EMAIL>' }
      ],
      current_page: 1,
      last_page: 1
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await socialConnectUsers(searchData, 1);

    expect(postMock).toHaveBeenCalledWith('social-connect/search-users', { json: searchData, method: 'post' });
    expect(result).toEqual(mockResponse);
  });

  it('should search users with searchData and page>1', async () => {
    const searchData = { query: 'John' };
    const mockResponse = {
      data: [
        { id: 5, first_name: 'Johnny', last_name: 'Cash', email: '<EMAIL>' }
      ],
      current_page: 2,
      last_page: 2
    };

    const postMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      post: postMock
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockReturnValue(mockResponse);

    const result = await socialConnectUsers(searchData, 2);

    expect(postMock).toHaveBeenCalledWith('social-connect/search-users?page=2', { json: searchData, method: 'post' });
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if response contains errors', async () => {
    const errorResponse = {
      errors: ['Invalid search parameters']
    };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(socialConnectUsers(null, 1)).rejects.toThrow('Invalid search parameters');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if response contains errors and searchData is provided', async () => {
    const searchData = { query: 'John' };
    const errorResponse = {
      errors: ['Invalid search parameters']
    };

    mockedSecureInstance.mockReturnValue({
      post: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(errorResponse)
      })
    });

    await expect(socialConnectUsers(searchData, 1)).rejects.toThrow('Invalid search parameters');
    expect(parseServerError).toHaveBeenCalledWith(errorResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(socialConnectUsersModule.socialConnectUsersResponseSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(socialConnectUsers(null, 1)).rejects.toThrow('Validation error');
  });
});