import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { parseServerError } from "@/utils/utility";
import { socialConnectUsersResponseSchema } from "@/types/schemas/socialConnectUsers";


export default async (searchData: any,pageNumber: number) => {
    var url = `social-connect/users`
    if (searchData != null) {
        url = `social-connect/search-users`
    }
    if (pageNumber != 1) {
        url = `${url}?page=${pageNumber}`
    }
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    console.log("Token:::", token)
    console.log("API CALLED", searchData)
    console.log("URL:::", url)
    if (searchData != null) {
        const response: any = await secureInstance(token).post(url, {json: searchData, method: 'post'}).json()
        console.log("Response:::", response)
        if (response.errors != null) {
            throw new Error(parseServerError(response))
        }
        return socialConnectUsersResponseSchema.parse(response);
    } else {
        const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
        if (response.errors != null) {
            throw new Error(parseServerError(response))
        }
        // console.log("Response NULL DATA:::", response)
        return socialConnectUsersResponseSchema.parse(response);
    }
	
}