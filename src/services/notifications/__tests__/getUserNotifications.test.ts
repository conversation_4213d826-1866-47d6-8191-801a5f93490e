import getUserNotifications from '../getUserNotifications';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import * as notificationModule from '@/types/schemas/notificationSchema';

// Mock the secureInstance
jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('getUserNotifications service', () => {
  const mockToken = 'test-token';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch notifications with page=1 correctly', async () => {
    const mockResponse = {
      data: [
        { id: '1', title: 'Notification 1', created_at: '2023-01-15T10:00:00Z' },
        { id: '2', title: 'Notification 2', created_at: '2023-01-15T14:30:00Z' }
      ],
      meta: {
        current_page: 1,
        total: 10
      }
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(notificationModule.paginatedNotificationSchema, 'parse').mockReturnValue(mockResponse);

    const result = await getUserNotifications(1);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('push-notifications', { method: 'get' });
    expect(notificationModule.paginatedNotificationSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should fetch notifications with page > 1 correctly', async () => {
    const pageNumber = 2;
    const mockResponse = {
      data: [
        { id: '3', title: 'Notification 3', created_at: '2023-01-14T10:00:00Z' },
        { id: '4', title: 'Notification 4', created_at: '2023-01-14T14:30:00Z' }
      ],
      meta: {
        current_page: 2,
        total: 10
      }
    };

    const getMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      get: getMock
    });

    jest.spyOn(notificationModule.paginatedNotificationSchema, 'parse').mockReturnValue(mockResponse);

    const result = await getUserNotifications(pageNumber);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(getMock).toHaveBeenCalledWith('push-notifications?page=2', { method: 'get' });
    expect(notificationModule.paginatedNotificationSchema.parse).toHaveBeenCalledWith(mockResponse);
    expect(result).toEqual(mockResponse);
  });

  it('should throw error if schema validation fails', async () => {
    const mockResponse = { invalid: 'data' };

    mockedSecureInstance.mockReturnValue({
      get: jest.fn().mockReturnValue({
        json: jest.fn().mockResolvedValue(mockResponse)
      })
    });

    jest.spyOn(notificationModule.paginatedNotificationSchema, 'parse')
      .mockImplementation(() => {
        throw new Error('Validation error');
      });

    await expect(getUserNotifications(1)).rejects.toThrow('Validation error');
  });
});