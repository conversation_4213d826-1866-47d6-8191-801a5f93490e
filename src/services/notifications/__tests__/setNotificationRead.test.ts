import setNotificationRead from '../setNotificationRead';
import { secureInstance } from '@/services/instance';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';

// Mock the secureInstance
jest.mock('@/services/instance');
jest.mock('@/App', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

const mockedSecureInstance = secureInstance as jest.Mock;

describe('setNotificationRead service', () => {
  const mockToken = 'test-token';
  const notificationId = '123';
  
  beforeEach(() => {
    (storage.getString as jest.Mock).mockReturnValue(mockToken);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should mark notification as read successfully', async () => {
    const mockResponse = {
      success: true,
      message: 'Notification marked as read'
    };

    const patchMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      patch: patchMock
    });

    const result = await setNotificationRead(notificationId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(patchMock).toHaveBeenCalledWith(`push-notifications/${notificationId}`, { method: 'patch' });
    expect(result).toEqual(mockResponse);
  });

  it('should handle error response', async () => {
    const mockResponse = {
      success: false,
      message: 'Notification not found'
    };

    const patchMock = jest.fn().mockReturnValue({
      json: jest.fn().mockResolvedValue(mockResponse)
    });

    mockedSecureInstance.mockReturnValue({
      patch: patchMock
    });

    const result = await setNotificationRead(notificationId);

    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
    expect(patchMock).toHaveBeenCalledWith(`push-notifications/${notificationId}`, { method: 'patch' });
    expect(result).toEqual(mockResponse);
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network error');

    mockedSecureInstance.mockReturnValue({
      patch: jest.fn().mockImplementation(() => {
        throw networkError;
      })
    });

    await expect(setNotificationRead(notificationId)).rejects.toThrow('Network error');
    expect(storage.getString).toHaveBeenCalledWith(AUTH_STORRAGE_KEYS.TOKEN);
    expect(mockedSecureInstance).toHaveBeenCalledWith(mockToken);
  });
});