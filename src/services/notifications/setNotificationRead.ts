import { secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";


export default async (nofificationId: string) => {
    const url = `push-notifications/${nofificationId}`;
    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN);
    const response: any = await secureInstance(token).patch(url, {method: 'patch'}).json();
    return response;
}
