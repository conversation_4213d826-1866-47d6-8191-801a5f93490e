import { GetProfileResponse } from "@/types/models";
import { instance, secureInstance } from "../instance";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { paginatedNotificationSchema } from "@/types/schemas/notificationSchema";


export default async (pageNumber: number) => {
    var url = `push-notifications`
    if (pageNumber != 1) {
        url = `${url}?page=${pageNumber}`
    }

    let token = storage.getString(AUTH_STORRAGE_KEYS.TOKEN)
    const response: any = await secureInstance(token).get(url, {method: 'get'}).json()
    return paginatedNotificationSchema.parse(response)
}