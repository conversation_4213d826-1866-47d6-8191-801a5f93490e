import "react-native-gesture-handler";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { MMKV } from "react-native-mmkv";
import { Toasts } from "@backpackapp-io/react-native-toast";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import { ThemeProvider } from "@/theme";

import ApplicationNavigator from "./navigators/Application";
import "./translations";
import { ActionSheetProvider } from "@expo/react-native-action-sheet";
import { configureTouchaleOpacityDefaultSettings } from "./utils/utility";
import { useEffect } from "react";
import { initializeIntegrations } from "./integrations/integrations";
import * as Sentry from "@sentry/react-native";
import { SENTRY_CONFIG } from "../config/sentry";

Sentry.init({
  dsn: SENTRY_CONFIG.dsn,
  environment: SENTRY_CONFIG.environment,
  debug: SENTRY_CONFIG.debug,

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay
  // replaysSessionSampleRate: 0.1,
  // replaysOnErrorSampleRate: 1,
  // integrations: [
  //   Sentry.mobileReplayIntegration(),
  //   Sentry.feedbackIntegration(),
  // ],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});
export const queryClient = new QueryClient();

export const storage = new MMKV();

configureTouchaleOpacityDefaultSettings();

function App() {
  useEffect(() => {
    initializeIntegrations();
  }, []);

  return (
    <SafeAreaProvider>
      <GestureHandlerRootView>
        <ActionSheetProvider>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider storage={storage}>
              <ApplicationNavigator />
              <Toasts />
            </ThemeProvider>
          </QueryClientProvider>
        </ActionSheetProvider>
      </GestureHandlerRootView>
    </SafeAreaProvider>
  );
}

export default Sentry.wrap(App);
