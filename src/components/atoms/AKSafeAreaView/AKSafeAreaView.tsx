import React, { ReactNode } from 'react';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { useTheme } from "@/theme";

type Props = {
    children: ReactNode;
    edges?: Edge[];
    style?: object;
};

function AKSafeAreaView({ children, edges = ['bottom'], style = {}}: Props) {

    const {
        layout,
        backgrounds,
    } = useTheme();

    return (
        <SafeAreaView edges={edges} style={[layout.flex_1, backgrounds.white, style]}>
            {children}
        </SafeAreaView>
    );
}

export default AKSafeAreaView;
