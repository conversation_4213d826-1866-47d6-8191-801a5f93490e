import React from 'react';
import { render } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AKSafeAreaView from '../AKSafeAreaView';
import { Text } from 'react-native';

// Mock SafeAreaView from react-native-safe-area-context
jest.mock('react-native-safe-area-context', () => {
  return {
    SafeAreaView: jest.fn(({ children, edges, style }) => (
      <mock-safe-area-view testID="mock-safe-area-view" edges={edges} style={style}>
        {children}
      </mock-safe-area-view>
    )),
  };
});

describe('AKSafeAreaView Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  beforeEach(() => {
    storage = new MMKV();
  });

  it('renders correctly with default props', () => {
    const { getByTestId, getByText } = render(
      <ThemeProvider storage={storage}>
        <AKSafeAreaView>
          <Text>Test Content</Text>
        </AKSafeAreaView>
      </ThemeProvider>
    );

    const safeAreaView = getByTestId('mock-safe-area-view');
    expect(safeAreaView).toBeTruthy();
    expect(getByText('Test Content')).toBeTruthy();
    
    // Check default edges prop
    expect(safeAreaView.props.edges).toEqual(['bottom']);
    
    // Check style contains flex_1 and white background
    expect(safeAreaView.props.style).toEqual(
      expect.arrayContaining([
        expect.anything(), // layout.flex_1
        expect.anything(), // backgrounds.white
        {} // default empty style
      ])
    );
  });

  it('applies custom edges correctly', () => {
    const customEdges = ['top', 'right'];
    
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKSafeAreaView edges={customEdges}>
          <Text>Test Content</Text>
        </AKSafeAreaView>
      </ThemeProvider>
    );

    const safeAreaView = getByTestId('mock-safe-area-view');
    expect(safeAreaView.props.edges).toEqual(customEdges);
  });

  it('applies custom style correctly', () => {
    const customStyle = { backgroundColor: 'red', padding: 10 };
    
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKSafeAreaView style={customStyle}>
          <Text>Test Content</Text>
        </AKSafeAreaView>
      </ThemeProvider>
    );

    const safeAreaView = getByTestId('mock-safe-area-view');
    expect(safeAreaView.props.style).toEqual(
      expect.arrayContaining([
        expect.anything(), // layout.flex_1
        expect.anything(), // backgrounds.white
        customStyle
      ])
    );
  });

  it('renders children correctly', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKSafeAreaView>
          <Text>First Child</Text>
          <Text>Second Child</Text>
        </AKSafeAreaView>
      </ThemeProvider>
    );

    expect(getByText('First Child')).toBeTruthy();
    expect(getByText('Second Child')).toBeTruthy();
  });
});