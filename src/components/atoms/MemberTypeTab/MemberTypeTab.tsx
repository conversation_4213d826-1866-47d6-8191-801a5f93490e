import { useTheme } from "@/theme";
import { Text, TouchableOpacity, View } from "react-native"
import { colorTokens } from "@/theme/colorTokens";

type Props = {
    text: string;
    badgeCount: number;
    isSelected: boolean;
    onPress: (text: string) => void;
}

function MemberTypeTab({ text, badgeCount, isSelected, onPress }: Props) {
    
    const {
        layout,
        gutters,
        borders,
        fonts,
    } = useTheme();

    const c = colorTokens();

    function onPressTab() {
        onPress(text);
    }

    const borderColor = isSelected ? c.stoke.primary.default : 'white';
    const badgeColor = isSelected ? c.background.default.primary.default : c.background.default.neutrals.secondary;
    const textColor = isSelected ? c.content.default.default : c.content.default.subdued;

    return (
        <TouchableOpacity onPress={onPressTab} style={[layout.flex_1, layout.itemsCenter]}>
            <View style={[layout.row, layout.itemsCenter, gutters.paddingHorizontal_4, gutters.paddingTop_8, gutters.paddingBottom_12, { gap: 8, }]}>
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: textColor }]}>{text}</Text>
                <View style={[layout.itemsCenter, layout.justifyCenter, borders.rounded_16, {backgroundColor: badgeColor, width: 20, height: 22 }]}>
                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Medium]}>{badgeCount}</Text>
                </View>
                <View style={[borders.rounded_4,{ height: 3, backgroundColor: borderColor, position: 'absolute', left: 4, right: 4, bottom: 0 }]} />
            </View>
        </TouchableOpacity>
    )

}

export default MemberTypeTab;
