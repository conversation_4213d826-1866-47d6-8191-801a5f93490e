import { useTheme } from "@/theme"
import { Text, TouchableOpacity } from "react-native"
import _Icon from 'react-native-vector-icons/AntDesign';
import { colorTokens } from "@/theme/colorTokens"
import AKIcon from "../AKIcon/AKIcon";

type Props = {
    text: string;
    icon: any;
    onPress: () => void;
}

function ProfileActionButton(props: Props) {

    const { text, icon, onPress } = props;
    
    const {
        layout,
        gutters,
        fonts,
        borders,
    } = useTheme();

    const c = colorTokens();

    return (
        <TouchableOpacity onPress={onPress} style={[layout.flex_1, layout.row, layout.itemsCenter, layout.justifyCenter, gutters.paddingVertical_8, borders.rounded_8, { borderWidth: 1, borderColor: c.stoke.default.default, gap: 8 }]}>
            {
                icon && (
                    <AKIcon source={icon} size={16} />
                )
            }
            <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, { color: c.content.default.default }]}>{text}</Text>
        </TouchableOpacity>
    )
}
export default ProfileActionButton;
