import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AKButton from '../AKButton';

describe('AKButton Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  // Mock onPress function
  const mockOnPress = jest.fn();

  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" />
      </ThemeProvider>
    );

    // Check if the button text is rendered
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when button is pressed', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" onPress={mockOnPress} />
      </ThemeProvider>
    );

    // Simulate press on the button
    fireEvent.press(getByText('Test Button'));

    // Check if onPress was called
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('does not call onPress when disabled', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" onPress={mockOnPress} disabled={true} />
      </ThemeProvider>
    );

    // Simulate press on the button
    fireEvent.press(getByText('Test Button'));

    // Check that onPress was not called
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('applies custom height and width', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" height={60} width={200} />
      </ThemeProvider>
    );

    const button = getByText('Test Button');
    const touchable = button.parent;
    const view = touchable.parent;
    
    expect(view.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          height: 60,
          width: 200
        })
      ])
    );
  });

  it('applies custom background color', () => {

    const customColor = '#FF0000';
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" backgroundColor={customColor} />
      </ThemeProvider>
    );

    const button = getByText('Test Button');
    const touchable = button.parent;
    const view = touchable.parent;

    expect(view.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          backgroundColor: customColor
        })
      ])
    );
  });

  it('applies custom text style', () => {
    const customTextStyle = { color: '#FF0000', fontSize: 18 };
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" textStyle={customTextStyle} />
      </ThemeProvider>
    );

    // Get the text element
    const textElement = getByText('Test Button');
    
    // Check if the text has the custom style
    expect(textElement.props.style).toEqual(customTextStyle);
  });

  it('applies default text style when no custom style is provided', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" />
      </ThemeProvider>
    );

    // Get the text element
    const textElement = getByText('Test Button');
    
    // Check if the text has the default style (which is an array of styles)
    expect(Array.isArray(textElement.props.style)).toBe(true);
    expect(textElement.props.style.length).toBeGreaterThan(0);
  });

  it('applies custom border radius', () => {

    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" borderRadius={10} />
      </ThemeProvider>
    );

    const button = getByText('Test Button');
    const touchable = button.parent;
    const view = touchable.parent;

    expect(view.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          borderRadius: 10
        })
      ])
    );
  });

  it('applies custom view style', () => {
    const customViewStyle = { margin: 10 };

    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" viewStyle={customViewStyle} />
      </ThemeProvider>
    );

    const touchable = getByTestId('AKButton');
    
    expect(touchable.props.style).toEqual(
      expect.objectContaining({
        margin: 10
      })
    );
  });

  it('calculates border radius from height when not explicitly provided', () => {
    const height = 60;

    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKButton title="Test Button" height={height} />
      </ThemeProvider>
    );

    const button = getByText('Test Button');
    const touchable = button.parent;
    const view = touchable.parent;

    expect(view.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          borderRadius: height
        })
      ])
    );
  });
});
