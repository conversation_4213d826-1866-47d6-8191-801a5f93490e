import { View, DimensionValue, TouchableOpacity, Text, ViewStyle, StyleProp } from "react-native";
import { useTheme } from '@/theme';
import { colorTokens } from "@/theme/colorTokens";

type Props = {
	height?: DimensionValue;
	width?: DimensionValue;
    title: string;
    backgroundColor?: any;
    textStyle?: any
    onPress?: () => void
    viewStyle?: StyleProp<ViewStyle> //ViewStyle | [ViewStyle]
    borderRadius?: number
    disabled?: boolean
};
function AKButton({height, borderRadius,width, title, backgroundColor, textStyle, onPress, viewStyle, disabled = false}: Props) {
    const {
		layout,
		gutters,
        fonts
	} = useTheme();

    const c = colorTokens();

    const defaultTextStyles = [
        fonts.fontSizes.utility.sm,
        fonts.SemiBold,
        fonts.lineHeight.utility.sm,
        {
            color: c.content.onBold.default.default
        }
    ];
    return (
        
        <TouchableOpacity testID="AKButton" disabled={disabled} style={[layout.flex_1, viewStyle ]} onPress={onPress} >
            <View style={[layout.justifyCenter, layout.itemsCenter, {borderRadius: borderRadius ? borderRadius : (height ?? Number(height!)/2.0) , height: height, width: width, backgroundColor: !!backgroundColor ? backgroundColor : c.fill.bold.neutrals.rest}]}>
                <Text style={textStyle ? textStyle : defaultTextStyles}>{title}</Text>
            </View>
        </TouchableOpacity>
            
        
    )
}

AKButton.defaultProps = {
	height: 50,
    title: '',
};

export default AKButton;