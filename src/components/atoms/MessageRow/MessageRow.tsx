import { AKFastImage, ImageVariant } from "@/components/atoms";
import { Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { useTheme } from "@/theme";
import { timeSince } from "@/utils";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import Tooltip from 'rn-tooltip';
import Menu from "@/theme/assets/images/Home/Menu.png"
import { Comment } from "@/types/models/comment";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import EyeCutIcon from "@/theme/assets/images/EyeCutIcon.png";
import ExcmalimationCircleIcon from "@/theme/assets/images/ExcmalimationCircleIcon.png";

export type Props = {
    comment: Comment
    isTooltipVisible: boolean
    onCommentReply?: (commentId: number, reply: string) => void
    onEditComment?: (comment: Comment) => void
    onDeleteComment?: (comment: Comment) => void
    onCommentSend?: (commentText: string, isEdit: boolean, comment: Comment | undefined, commentRef: any) => void
    onReportComment?: (postId: number, commentID: number, replyID?: number, userID?: number) => void
    userId?: number
    setIsReported?: (isReported: boolean) => void
}

function MessageRow(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();

    const c = colorTokens();

    const { t } = useTranslation(['home']);
    const tooltipRef = useRef<any>(null);

    const [isReported, setIsReported] = useState(props?.comment?.reported_comment_exists || props?.comment?.reported_reply_exists);

    useEffect(() => {
        setIsReported(props?.comment?.reported_comment_exists || props?.comment?.reported_reply_exists);
    }, [props?.comment?.reported_comment_exists || props?.comment?.reported_reply_exists]);

    function onEditButton(): void {
        if (props.onEditComment != null) {
            props.onEditComment(props.comment)
            tooltipRef?.current.toggleTooltip();
        }
    }
    function onDeleteButton(): void {
        if (props.onDeleteComment != null) {
            props.onDeleteComment(props.comment)
            tooltipRef?.current.toggleTooltip();
        }
    }
    function onReportButton(): void {
        if (props.onReportComment != null) {
            props.onReportComment(null, props.comment.id, props.comment.replyId, null)
            tooltipRef?.current.toggleTooltip();
        }
    }
    function onPressEyeCutIcon() {
        setIsReported(prev => !prev);
        if(props.setIsReported != null) {
            props.setIsReported(prev => !prev);
        }
    }
    function popoverContent(): JSX.Element {
        return (
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter, {width: 100, gap: 8} ]}>
                {
                    popoverOptions.map(option => (
                        <TouchableOpacity key={option.text} onPress={option.onPress}>
                            <View style={[layout.itemsCenter, layout.justifyCenter, { width: 100, height: 30 }]}>
                                <Text style={[fonts.Medium, {color: c.content.default.default}]}>{option.text}</Text>
                            </View>
                        </TouchableOpacity>
                    ))
                }
            </View>
        )
    }

    let popoverOptions = [
        {
            text: t('home:Edit'),
            onPress: onEditButton,
        },
        {
            text: t('home:Delete'),
            onPress: onDeleteButton,
        },
    ]

    if (props.comment.user_id != props.userId) {
        popoverOptions = [
            {
                text: t('home:Report'),
                onPress: onReportButton,
            },
        ]
    }

    return (
        <View>
            <View style={[layout.row, layout.itemsCenter, layout.justifyBetween ,{height: 40}]}>
                <View style={[layout.row, layout.itemsCenter ,{height: 40}]}>
                    <AKFastImage
                        uri={props.comment.user.profile_photo}
                        style={[{width: 32, height: 32, borderRadius: 16}]}
                    />
                    <Text style={[fonts.Bold, gutters.marginHorizontal_12, { color: c.content.default.default }]}>{`${ props.comment.user.first_name} ${ props.comment.user.last_name}`}</Text>
                    <Text style={[fonts.gray400]}>{timeSince(new Date(props.comment.updated_at))}</Text>
                </View>
                <View>
                    {
                        !isReported && (
                            <Tooltip 
                                ref={tooltipRef}
                                withOverlay={false} 
                                pointerColor={c.content.default.default} 
                                height={50 * popoverOptions.length} 
                                width={100} 
                                containerStyle={{borderWidth: 1, borderLeftColor: c.content.default.default, borderRightColor: c.content.default.default ,borderBlockColor: c.content.default.default, alignItems: 'center', justifyContent: 'center'}} 
                                backgroundColor={colors.gray50} 
                                actionType="press" 
                                popover={popoverContent()}
                                
                            >
                                <ImageVariant
                                    source={Menu}
                                    style={ [layout.itemsCenter, {tintColor: c.content.default.default, width: 24, height: 24}]}
                                />
                            </Tooltip>
                        )
                    }
                </View>
            </View>
            {
                isReported ? (
                    <View style={[layout.row, layout.flex_1, layout.itemsCenter]}>
                        <View style={[layout.row, layout.flex_1, gutters.paddingHorizontal_12, gutters.paddingVertical_8, borders.rounded_12, gutters.marginTop_4, { gap: 2, backgroundColor: c.background.default.neutrals.secondary }]}>
                            <AKIcon source={ExcmalimationCircleIcon} size={16} styles={[gutters.marginRight_4]} />
                            <View style={[layout.flex_1]}>
                                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, {color: c.content.default.default}]}>
                                    This comment has been reported and is under review
                                </Text>
                            </View>
                        </View>
                        <AKIcon testID="eye-cut-icon" source={EyeCutIcon} size={20} styles={[gutters.marginLeft_16]} onPress={onPressEyeCutIcon} />
                    </View>
                ) : (
                    <View>
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Regular, {color: c.content.default.default}]}>{props.comment.comment}</Text>
                    </View>
                )
            }
        </View>
    )
}
export default MessageRow;
