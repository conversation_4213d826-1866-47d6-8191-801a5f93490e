import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import MessageRow from '../MessageRow';
import { Comment } from '@/types/models/comment';

// Mock dependencies
jest.mock('react-native-fast-image', () => {
  const { Image } = require('react-native');
  return Image;
});

jest.mock('@/components/atoms', () => ({
  ImageVariant: require('react-native').Image,
}));

jest.mock('@/components/atoms/AKIcon/AKIcon', () => {
  const { TouchableOpacity, Image } = require('react-native');
  return ({ onPress, source, testID }) => (
    <TouchableOpacity onPress={onPress} testID={testID}>
      <Image source={source} />
    </TouchableOpacity>
  );
});

// Fix the Tooltip mock to properly handle refs
jest.mock('rn-tooltip', () => {
  const React = require('react');
  const { View, TouchableOpacity, Text } = require('react-native');

  const MockTooltip = React.forwardRef((props, ref) => {
    // Attach a mock method to the ref
    React.useImperativeHandle(ref, () => ({
      toggleTooltip: jest.fn(),
    }));

    return (
      <View testID="tooltip">
        {props.children}
        <View testID="tooltip-content">
          {props.popover}
        </View>
      </View>
    );
  });

  return MockTooltip;
});

jest.mock('@/utils', () => ({
  timeSince: jest.fn().mockReturnValue('2 hours ago'),
}));

jest.mock('@/theme/colorTokens', () => ({
  colorTokens: jest.fn().mockReturnValue({
    content: {
      default: {
        default: '#000000',
      }
    },
    background: {
      default: {
        neutrals: {
          secondary: '#f5f5f5'
        }
      }
    }
  }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations = {
        'home:Edit': 'Edit',
        'home:Delete': 'Delete',
        'home:Report': 'Report',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock theme
jest.mock('@/theme', () => ({
  ...jest.requireActual('@/theme'),
  useTheme: jest.fn().mockReturnValue({
    layout: {
      row: { flexDirection: 'row' },
      flex_1: { flex: 1 },
      itemsCenter: { alignItems: 'center' },
      justifyCenter: { justifyContent: 'center' },
      justifyBetween: { justifyContent: 'space-between' },
    },
    gutters: {
      marginHorizontal_12: { marginHorizontal: 12 },
      marginRight_4: { marginRight: 4 },
      marginLeft_16: { marginLeft: 16 },
      marginTop_4: { marginTop: 4 },
      paddingHorizontal_12: { paddingHorizontal: 12 },
      paddingVertical_8: { paddingVertical: 8 },
    },
    fonts: {
      bold: { fontWeight: 'bold' },
      gray400: { color: '#9e9e9e' },
      body: { fontFamily: 'System' },
      fontSizes: {
        body: {
          xs: { fontSize: 12 },
        },
      },
      lineHeight: {
        body: {
          xs: { lineHeight: 16 },
        },
      },
    },
    colors: {
      gray50: '#fafafa',
    },
    borders: {
      rounded_12: { borderRadius: 12 },
    },
    backgrounds: {},
  }),
}));

// Mock assets
jest.mock('@/theme/assets/images/Home/Menu.png', () => ({}), { virtual: true });
jest.mock('@/theme/assets/images/EyeCutIcon.png', () => ({}), { virtual: true });
jest.mock('@/theme/assets/images/ExcmalimationCircleIcon.png', () => ({}), { virtual: true });

describe('MessageRow Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  // Mock comment data
  const mockComment: Comment = {
    id: 1,
    comment: 'This is a test comment',
    user: {
      id: 1,
      first_name: 'John',
      last_name: 'Doe',
      profile_photo: 'https://example.com/profile.jpg',
    },
    user_id: 1,
    created_at: '2023-01-01T12:00:00Z',
    updated_at: '2023-01-01T12:00:00Z',
    reported_comment_exists: false,
    reported_reply_exists: false,
  };
  
  // Mock callback functions
  const mockOnEditComment = jest.fn();
  const mockOnDeleteComment = jest.fn();
  const mockOnReportComment = jest.fn();
  const mockSetIsReported = jest.fn();
  
  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly with required props', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={mockComment} 
          isTooltipVisible={false}
          userId={1}
        />
      </ThemeProvider>
    );

    // Check if user name is rendered
    expect(getByText('John Doe')).toBeTruthy();
    
    // Check if comment text is rendered
    expect(getByText('This is a test comment')).toBeTruthy();
    
    // Check if timestamp is rendered
    expect(getByText('2 hours ago')).toBeTruthy();
  });

  it('renders tooltip when menu icon is present', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={mockComment} 
          isTooltipVisible={true}
          userId={1}
        />
      </ThemeProvider>
    );

    // Check if tooltip is rendered
    expect(getByTestId('tooltip')).toBeTruthy();
  });

  it('shows Edit and Delete options when comment belongs to current user', () => {
    const { getByTestId, getByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={mockComment} 
          isTooltipVisible={true}
          userId={1}
          onEditComment={mockOnEditComment}
          onDeleteComment={mockOnDeleteComment}
        />
      </ThemeProvider>
    );

    // Check if Edit and Delete options are present in the tooltip content
    const tooltipContent = getByTestId('tooltip-content');
    expect(getByText('Edit')).toBeTruthy();
    expect(getByText('Delete')).toBeTruthy();
  });

  it('shows Report option when comment does not belong to current user', () => {
    const { getByTestId, getByText, queryByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={mockComment} 
          isTooltipVisible={true}
          userId={2} // Different user ID
          onReportComment={mockOnReportComment}
        />
      </ThemeProvider>
    );

    // Check if Report option is present and Edit/Delete are not
    const tooltipContent = getByTestId('tooltip-content');
    expect(getByText('Report')).toBeTruthy();
    expect(queryByText('Edit')).toBeNull();
    expect(queryByText('Delete')).toBeNull();
  });

  it('displays reported comment message when comment is reported', () => {
    const reportedComment = {
      ...mockComment,
      reported_comment_exists: true,
    };

    const { getByText, queryByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={reportedComment} 
          isTooltipVisible={false}
          userId={1}
        />
      </ThemeProvider>
    );

    // Check if reported message is shown and comment text is hidden
    expect(getByText('This comment has been reported and is under review')).toBeTruthy();
    expect(queryByText('This is a test comment')).toBeNull();
  });

  it('toggles reported state when eye-cut icon is pressed', () => {
    const reportedComment = {
      ...mockComment,
      reported_comment_exists: true,
    };

    const { getByTestId, queryByText, getByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={reportedComment} 
          isTooltipVisible={false}
          userId={1}
          setIsReported={mockSetIsReported}
        />
      </ThemeProvider>
    );

    // Initially shows reported message
    expect(getByText('This comment has been reported and is under review')).toBeTruthy();
    
    // Find and press the eye-cut icon
    const eyeCutIcon = getByTestId('eye-cut-icon');
    fireEvent.press(eyeCutIcon);
    
    // Check if setIsReported was called
    expect(mockSetIsReported).toHaveBeenCalled();
  });

  it('calls onEditComment when Edit option is pressed', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={mockComment} 
          isTooltipVisible={true}
          userId={1}
          onEditComment={mockOnEditComment}
        />
      </ThemeProvider>
    );

    // Press the Edit option
    fireEvent.press(getByText('Edit'));
    
    // Check if onEditComment was called with the correct comment
    expect(mockOnEditComment).toHaveBeenCalledWith(mockComment);
  });

  it('calls onDeleteComment when Delete option is pressed', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={mockComment} 
          isTooltipVisible={true}
          userId={1}
          onDeleteComment={mockOnDeleteComment}
        />
      </ThemeProvider>
    );

    // Press the Delete option
    fireEvent.press(getByText('Delete'));
    
    // Check if onDeleteComment was called with the correct comment
    expect(mockOnDeleteComment).toHaveBeenCalledWith(mockComment);
  });

  it('calls onReportComment when Report option is pressed', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <MessageRow 
          comment={mockComment} 
          isTooltipVisible={true}
          userId={2} // Different user ID
          onReportComment={mockOnReportComment}
        />
      </ThemeProvider>
    );

    // Press the Report option
    fireEvent.press(getByText('Report'));
    
    // Check if onReportComment was called with the correct parameters
    expect(mockOnReportComment).toHaveBeenCalledWith(null, mockComment.id, mockComment.replyId, null);
  });
});
