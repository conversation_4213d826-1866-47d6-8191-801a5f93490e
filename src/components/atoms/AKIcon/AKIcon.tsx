import { StyleProp, TouchableOpacity, ViewStyle } from "react-native";
import ImageVariant from "../ImageVariant/ImageVariant";

type Props = {
    size?: number;
    styles?: StyleProp<ViewStyle>;
    onPress?: () => void;
    source: any;
} & Parameters<typeof ImageVariant>[0];

type DefaultStyles = {
    height: number; 
    width: number; 
    resizeMode: string,
}

function AKIcon({ source, size = 24, styles = [], onPress, ...props }: Props) {

    const iconDefaultStyles: DefaultStyles = {
        height: size, 
        width: size, 
        resizeMode: 'contain',
    }

    const isDisabled: boolean = !onPress;

    return (

        <TouchableOpacity testID="IconTouchable" onPress={onPress} disabled={isDisabled}>
            <ImageVariant
                source={source}
                style={[iconDefaultStyles, ...styles]}
                {...props}
            />
        </TouchableOpacity>
    )
}

export default AKIcon;
