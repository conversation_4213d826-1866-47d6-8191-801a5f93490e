import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AKIcon from '../AKIcon';

describe('AKIcon Component', () => {
  let storage: MMKV;
  const mockOnPress = jest.fn();
  const mockSource = { uri: 'https://example.com/icon.png' };

  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKIcon source={mockSource} testID="test-icon" />
      </ThemeProvider>
    );

    const imageVariant = getByTestId('test-icon');
    expect(imageVariant).toBeTruthy();
    expect(imageVariant.props.source).toBe(mockSource);
    
    expect(imageVariant.props.style[0]).toEqual({
      height: 24,
      width: 24,
      resizeMode: 'contain'
    });
  });

  it('applies custom size correctly', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKIcon source={mockSource} size={48} testID="test-icon" />
      </ThemeProvider>
    );

    const imageVariant = getByTestId('test-icon');
    expect(imageVariant.props.style[0]).toEqual({
      height: 48,
      width: 48,
      resizeMode: 'contain'
    });
  });

  it('applies custom styles correctly', () => {
    const customStyle = { borderRadius: 8, backgroundColor: 'red' };
    
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKIcon source={mockSource} styles={[customStyle]} testID="test-icon" />
      </ThemeProvider>
    );

    const imageVariant = getByTestId('test-icon');
    expect(imageVariant.props.style).toContainEqual(customStyle);
  });

  it('calls onPress when pressed', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKIcon source={mockSource} onPress={mockOnPress} />
      </ThemeProvider>
    );

    const touchable = getByTestId('IconTouchable');
    fireEvent.press(touchable);
    
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('disables touch when no onPress is provided', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKIcon source={mockSource} />
      </ThemeProvider>
    );

    const touchable = getByTestId('IconTouchable');
    expect(touchable.props.accessibilityState.disabled).toBe(true);
  });

  it('passes additional props to ImageVariant', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKIcon 
          source={mockSource} 
          resizeMode="cover"
          tintColor="blue"
          testID="test-icon"
        />
      </ThemeProvider>
    );

    const imageVariant = getByTestId('test-icon');
    expect(imageVariant.props.resizeMode).toBe('cover');
    expect(imageVariant.props.tintColor).toBe('blue');
  });
});