import { useTheme } from "@/theme";
import { View, Text } from "react-native";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { colorTokens } from "@/theme/colorTokens";

type Props = {
    text: string;
}
function BulletText(props: Props) {

    const { text } = props;

    const {
        layout,
        gutters,
        fonts,
    } = useTheme();

    const c = colorTokens();

    return (
        <View style={{ flexDirection: 'row' }}>
            <Text style={{fontSize: 16}}>{'\u2022'}</Text>
            <Text style={[layout.flex_1, fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, gutters.marginLeft_4, { color: c.content.default.default }]}>{text}</Text>
        </View>
    )
}

export default BulletText;
