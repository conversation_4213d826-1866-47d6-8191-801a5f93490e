import React from 'react';
import { render } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import HomeItem from '../HomeItem';

// Mock FastImage
jest.mock('react-native-fast-image', () => {
  const { Image } = require('react-native');
  return Image;
});

// Mock ImageVariant
jest.mock('../../ImageVariant/ImageVariant', () => {
  const { Image } = require('react-native');
  return Image;
});

// Mock useTheme
jest.mock('@/theme', () => ({
  ...jest.requireActual('@/theme'),
  useTheme: jest.fn().mockReturnValue({
    fonts: {
      size_16: { fontSize: 16 },
      bold: { fontWeight: 'bold' },
      gray50: { color: '#fafafa' }
    },
    layout: {
      absolute: { position: 'absolute' },
      row: { flexDirection: 'row' },
      flex_1: { flex: 1 }
    },
    gutters: {
      marginHorizontal_16: { marginHorizontal: 16 },
      marginTop_16: { marginTop: 16 },
      marginLeft_12: { marginLeft: 12 },
      marginTop_4: { marginTop: 4 }
    },
    borders: {
      rounded_4: { borderRadius: 4 }
    },
    backgrounds: {
      transparent: { backgroundColor: 'transparent' }
    }
  })
}));

describe('HomeItem Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  // Mock item data
  const mockItem = {
    id: 1,
    title: 'Test Module',
    thumbnail: 'https://example.com/image.jpg',
    bg_color: '#f5f5f5',
    logo_color: '#ff0000',
    slug: 'test-module',
    is_active: true
  };

  beforeEach(() => {
    storage = new MMKV();
  });

  it('renders correctly with item data', () => {
    const { getByText, getByTestId } = render(
      <ThemeProvider storage={storage}>
        <HomeItem item={mockItem} />
      </ThemeProvider>
    );

    expect(getByText('Test Module')).toBeTruthy();
    
    const fastImage = getByTestId('thumbnail-image');
    expect(fastImage.props.source).toEqual({ uri: 'https://example.com/image.jpg' });
    
    const container = getByTestId('home-item-container');
    expect(container.props.style).toEqual(expect.arrayContaining([
      { marginHorizontal: 16 },
      { marginTop: 16 },
      { backgroundColor: '#f5f5f5', borderBottomLeftRadius: 50, borderRadius: 8, height: 184, overflow: 'hidden' }
    ]));
  });

  it('renders correctly without item data', () => {
    const { queryByText, queryByTestId } = render(
      <ThemeProvider storage={storage}>
        <HomeItem />
      </ThemeProvider>
    );

    // Check that no title is rendered
    expect(queryByText('Test Module')).toBeNull();
    
    // Check that the container is still rendered
    expect(queryByTestId('home-item-container')).toBeTruthy();
  });

  it('applies correct styles to the container', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <HomeItem item={mockItem} />
      </ThemeProvider>
    );

    const container = getByTestId('home-item-container');

    expect(container.props.style).toEqual(expect.arrayContaining([
      { marginHorizontal: 16 },
      { marginTop: 16 },
      { backgroundColor: '#f5f5f5', borderBottomLeftRadius: 50, borderRadius: 8, height: 184, overflow: 'hidden' }
    ]));
  });

  it('applies correct styles to the image', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <HomeItem item={mockItem} />
      </ThemeProvider>
    );

    const image = getByTestId('thumbnail-image');
    
    // Check image styles
    expect(image.props.style).toEqual(expect.arrayContaining([
      { marginLeft: 12 },
      { borderRadius: 4 },
      { backgroundColor: "transparent", borderBottomLeftRadius: 60, height: 184}
    ]));
  });

  it('applies correct styles to the logo', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <HomeItem item={mockItem} />
      </ThemeProvider>
    );

    const logo = getByTestId('home-item-logo');
    
    // Check logo styles
    expect(logo.props.style).toEqual(expect.arrayContaining([
      { tintColor: '#ff0000', resizeMode: 'contain', width: 24, height: 40 },
    ]));
  });

  it('applies correct styles to the title', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <HomeItem item={mockItem} />
      </ThemeProvider>
    );

    const title = getByText('Test Module');
    
    // Check title styles
    expect(title.props.style).toContainEqual({ fontSize: 16 });
    expect(title.props.style).toContainEqual({ fontWeight: 'bold' });
    expect(title.props.style).toContainEqual({ color: '#fafafa' });
  });
});