import { Image, ImageBackground, Text, View } from "react-native";
import { moduleSchema } from "@/types/schemas/module";
import { useTheme } from "@/theme";
import FastImage from "react-native-fast-image";
import ImageVariant from "../ImageVariant/ImageVariant";
import AkinaLogo from "@/theme/assets/images/AkinaLogo.png"
type Props = {
    item?: typeof moduleSchema['_output']
}
function HomeItem(props: Props) {
    const {
        fonts,
		layout,
		gutters,
        borders,
        backgrounds
	} = useTheme();
    return (
        <View testID="home-item-container" style={[gutters.marginHorizontal_16 ,gutters.marginTop_16,{height: 184, borderRadius: 8 ,borderBottomLeftRadius: 50, overflow: 'hidden', backgroundColor: props.item?.bg_color}]}>
            <View style={[{height: 184, overflow: 'hidden'}]}>
                <FastImage testID="thumbnail-image" style={[ gutters.marginLeft_12, borders.rounded_4 ,{height: 184 ,backgroundColor: 'transparent', borderBottomLeftRadius: 60,} ]} source={{uri: props.item?.thumbnail}}/>
                <View style={[layout.absolute, layout.row, {bottom: 8, left: 55, right: 8}]}>
                    <View style={[layout.flex_1, gutters.marginTop_4 ]}>
                        <Text style={[fonts.size_16 ,fonts.bold, fonts.gray50]}>{props.item?.title}</Text>
                    </View>
                    <ImageVariant
                        testID="home-item-logo"
                        source={AkinaLogo}
                        style={ [ {tintColor:  props.item?.logo_color, resizeMode: 'contain', width: 24, height: 40 }]}
                    />
                </View>
            </View>
            
        </View>
    )
}

export default HomeItem