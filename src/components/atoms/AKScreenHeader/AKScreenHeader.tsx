import React from 'react';
import { Text, View } from 'react-native';
import { colorTokens } from "@/theme/colorTokens";
import { useTheme } from "@/theme";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import ChevronLeftIcon from "@/theme/assets/images/SocialConnect/ChevronLeftIcon.png";
import { useNavigation } from '@react-navigation/native';

type Props = {
    text: string;
};

function AKScreenHeader({ text }: Props) {

    const {
        layout,
        gutters,
        fonts,
        backgrounds,
    } = useTheme();

    const c = colorTokens();
    const navigation = useNavigation();

    return (
        <View style={[ layout.row, layout.itemsCenter, gutters.marginLeft_12, gutters.marginVertical_24 ]}>
            <AKIcon tintColor='black' source={ChevronLeftIcon} onPress={navigation.goBack} styles={[gutters.marginRight_12]} />
            <Text style={[fonts.fontSizes.headings.H3, fonts.lineHeight.headings.H3, fonts.Bold, {color: c.content.default.emphasis}]}>{text}</Text>
        </View>
    );
}

export default AKScreenHeader;
