import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AKScreenHeader from '../AKScreenHeader';

// Mock @react-navigation/native
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    goBack: jest.fn()
  }),
  // Add mock for DarkTheme
  DarkTheme: {
    colors: {
      primary: '#fff',
      background: '#000',
      card: '#000',
      text: '#fff',
      border: '#000',
      notification: '#fff'
    }
  }
}));

describe('AKScreenHeader Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  const mockGoBack = jest.fn();
  
  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
    
    // Update the mock implementation for useNavigation
    jest.spyOn(require('@react-navigation/native'), 'useNavigation').mockImplementation(() => ({
      goBack: mockGoBack
    }));
  });

  it('renders correctly with provided text', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKScreenHeader text="Screen Title" />
      </ThemeProvider>
    );

    expect(getByText('Screen Title')).toBeTruthy();
  });

  it('calls navigation.goBack when back icon is pressed', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKScreenHeader text="Screen Title" />
      </ThemeProvider>
    );

    const backIcon = getByTestId('IconTouchable');
    fireEvent.press(backIcon);
    expect(mockGoBack).toHaveBeenCalledTimes(1);
  });

  it('applies correct styling to header text', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKScreenHeader text="Screen Title" />
      </ThemeProvider>
    );

    const headerText = getByText('Screen Title');
    
    expect(headerText.props.style).toEqual(
      expect.arrayContaining([
        {"fontSize": 24}, {"lineHeight": 32}, {"fontFamily": "SFProText-Bold"}, {"color": "#1A1A1A"}
      ])
    );
  });
});
