import { useTheme } from '@/theme';
import { colorTokens } from '@/theme/colorTokens';
import { bookmarkSchema } from '@/types/schemas/userBookmarks';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { z } from 'zod';

type Category = z.infer<typeof bookmarkSchema> | undefined;

type Props = {
    categories?: Category[]
    onCategoryPressed: (item: any) => void
    selectedCategory: any
}

function CategoryHorizontalList(props: Props) {
	const { categories, onCategoryPressed, selectedCategory } = props;

	const { layout, gutters, colors } = useTheme();
	const c = colorTokens();

	function renderCategoryItem({ item }: any) {
		return (
			<TouchableOpacity onPress={() => onCategoryPressed(item)}>
				<View
					testID={`category-item-${item.id}`}
					style={[
						layout.justifyCenter,
						layout.itemsCenter,
						{ height: 32, borderRadius: 10, backgroundColor: item.id == selectedCategory?.id
							? c.content.primary.default
							: colors.gray100 },
					]}
				>
					<Text style={[gutters.marginHorizontal_12, { color: item.id == selectedCategory?.id
							? colors.white
							: colors.black }]}>{item.title}</Text>
				</View>
			</TouchableOpacity>
		);
	}

	return (
		<View style={[gutters.marginTop_12]}>
			<FlatList
				horizontal
				showsVerticalScrollIndicator={false}
				showsHorizontalScrollIndicator={false}
				contentContainerStyle={[gutters.paddingHorizontal_16, { gap: 16 }]}
				data={categories}
				keyExtractor={(_, index) => index.toString()}
				renderItem={renderCategoryItem}
			/>
		</View>
	);
}
export default CategoryHorizontalList;
