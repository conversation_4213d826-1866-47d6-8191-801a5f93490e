import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import CategoryHorizontalList from '../CategoryHorizontalList';

// Mock colorTokens
jest.mock('@/theme/colorTokens', () => ({
  colorTokens: jest.fn().mockReturnValue({
    content: {
      primary: {
        default: '#1E88E5'
      }
    }
  })
}));

describe('CategoryHorizontalList Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  // Mock categories data
  const mockCategories = [
    { id: 1, title: 'Category 1' },
    { id: 2, title: 'Category 2' },
    { id: 3, title: 'Category 3' }
  ];
  
  // Mock onCategoryPressed function
  const mockOnCategoryPressed = jest.fn();

  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly with categories', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <CategoryHorizontalList 
          categories={mockCategories}
          onCategoryPressed={mockOnCategoryPressed}
          selectedCategory={mockCategories[0]}
        />
      </ThemeProvider>
    );

    // Check if all category titles are rendered
    expect(getByText('Category 1')).toBeTruthy();
    expect(getByText('Category 2')).toBeTruthy();
    expect(getByText('Category 3')).toBeTruthy();
  });

  it('renders correctly with no categories', () => {
    const { queryByText } = render(
      <ThemeProvider storage={storage}>
        <CategoryHorizontalList 
          categories={[]}
          onCategoryPressed={mockOnCategoryPressed}
          selectedCategory={null}
        />
      </ThemeProvider>
    );

    // Check that no category titles are rendered
    expect(queryByText('Category 1')).toBeNull();
  });

  it('renders correctly with undefined categories', () => {
    const { queryByText } = render(
      <ThemeProvider storage={storage}>
        <CategoryHorizontalList 
          onCategoryPressed={mockOnCategoryPressed}
          selectedCategory={null}
        />
      </ThemeProvider>
    );

    // Check that no category titles are rendered
    expect(queryByText('Category 1')).toBeNull();
  });

  it('calls onCategoryPressed when a category is pressed', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <CategoryHorizontalList 
          categories={mockCategories}
          onCategoryPressed={mockOnCategoryPressed}
          selectedCategory={mockCategories[0]}
        />
      </ThemeProvider>
    );

    // Press the second category
    fireEvent.press(getByText('Category 2'));
    
    // Verify callback was called with the correct category
    expect(mockOnCategoryPressed).toHaveBeenCalledWith(mockCategories[1]);
  });

  it('applies correct styles to selected category', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <CategoryHorizontalList 
          categories={mockCategories}
          onCategoryPressed={mockOnCategoryPressed}
          selectedCategory={mockCategories[0]}
        />
      </ThemeProvider>
    );

    // Get the selected category text element
    const selectedCategoryText = getByText('Category 1');
    
    // Check that the text color is white (for selected category)
    expect(selectedCategoryText.props.style[1].color).toBe("#FFFFFF");
    
    // Get the non-selected category text element
    const nonSelectedCategoryText = getByText('Category 2');
    
    // Check that the text color is black (for non-selected category)
    expect(nonSelectedCategoryText.props.style[1].color).toBe("#000000");
    
    // Verify the colors are different
    expect(selectedCategoryText.props.style[1].color).not.toBe(
      nonSelectedCategoryText.props.style[1].color
    );
  });

  it('applies correct background color to selected category', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <CategoryHorizontalList 
          categories={mockCategories}
          onCategoryPressed={mockOnCategoryPressed}
          selectedCategory={mockCategories[0]}
        />
      </ThemeProvider>
    );

    const selectedCategoryContainer = getByTestId('category-item-1');
    expect(selectedCategoryContainer.props.style[2].backgroundColor).toBe('#1E88E5');
    
    const nonSelectedCategoryContainer = getByTestId('category-item-2');
    expect(nonSelectedCategoryContainer.props.style[2].backgroundColor).not.toBe('#1E88E5');
  });
});