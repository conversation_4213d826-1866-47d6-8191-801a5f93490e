import { OutlinedTextField } from 'rn-material-ui-textfield';
import { ReturnKeyTypeOptions } from 'react-native';
import { colorTokens } from '@/theme/colorTokens';

type Props = {
    inputRef?: any
    label: string
	value?: string;
	tintColor?: string;
    baseColor: string;
    backgroundColor?: any;
    textColor?: any
    onChangeValue: (label: string,text: string) => void
    error?: string | null
    name: string
    isSecure?: boolean
    keyboardType?: string
    autoCapitalize?: string
    rightAccessory?: () => void
    leftAccessory?: () => void
    returnKeyType?: ReturnKeyTypeOptions
    onSubmitEditing?: () => void
    blurOnSubmit?: boolean
};
function AKTextField({inputRef,label,value, tintColor, baseColor, onChangeValue, error, name, isSecure = false, keyboardType, autoCapitalize, rightAccessory, returnKeyType, onSubmitEditing, blurOnSubmit, leftAccessory}: Props) {

    const c = colorTokens();

    const textFieldTintColor = tintColor ? tintColor : c.content.default.default;
    return (
        <OutlinedTextField
            ref={inputRef}
            label={label}
            tintColor={textFieldTintColor}
            baseColor={baseColor}
            value={value}
            onChangeText={(text: string) => onChangeValue(name, text)}  
            error={error}
            secureTextEntry={isSecure}
            keyboardType={keyboardType}
            autoCapitalize={autoCapitalize}
            renderRightAccessory={rightAccessory}
            renderLeftAccessory={leftAccessory}
            returnKeyType={returnKeyType}
            onSubmitEditing={onSubmitEditing}
            blurOnSubmit={blurOnSubmit}
        />
    )
}

export default AKTextField