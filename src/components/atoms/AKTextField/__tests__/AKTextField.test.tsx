import React from 'react';
import { render } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AKTextField from '../AKTextField';
import { colorTokens } from '@/theme/colorTokens';

// Mock colorTokens
jest.mock('@/theme/colorTokens', () => ({
  colorTokens: jest.fn().mockReturnValue({
    content: {
      default: {
        default: '#333333'
      }
    }
  })
}));

// Mock rn-material-ui-textfield
jest.mock('rn-material-ui-textfield', () => {
  const React = require('react');
  const { View, Text } = require('react-native');
  
  const OutlinedTextField = React.forwardRef(({ 
    label, 
    value, 
    onChangeText, 
    error, 
    secureTextEntry,
    tintColor,
    baseColor,
    keyboardType,
    autoCapitalize,
    renderRightAccessory,
    renderLeftAccessory,
    returnKeyType,
    onSubmitEditing,
    blurOnSubmit
  }, ref) => (
    <View 
      ref={ref}
      testID="outlined-text-field"
      label={label}
      value={value}
      error={error}
      tintColor={tintColor}
      baseColor={baseColor}
      secureTextEntry={secureTextEntry}
      keyboardType={keyboardType}
      autoCapitalize={autoCapitalize}
      returnKeyType={returnKeyType}
      blurOnSubmit={blurOnSubmit}
      onChangeText={text => onChangeText && onChangeText(text)}
      onSubmitEditing={onSubmitEditing}
    >
      {renderRightAccessory && <View testID="right-accessory">{renderRightAccessory()}</View>}
      {renderLeftAccessory && <View testID="left-accessory">{renderLeftAccessory()}</View>}
    </View>
  ));
  
  return { OutlinedTextField };
});

describe('AKTextField Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  const mockOnChangeValue = jest.fn();
  const mockOnSubmitEditing = jest.fn();
  
  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly with required props', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    expect(getByTestId('outlined-text-field')).toBeTruthy();
    expect(getByTestId('outlined-text-field').props.label).toBe('Test Field');
  });

  it('uses default tint color when tintColor is not provided', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    // The default tint color from colorTokens should be used
    expect(getByTestId('outlined-text-field').props.tintColor).toBe('#333333');
    expect(colorTokens).toHaveBeenCalled();
  });

  it('uses provided tint color when tintColor is specified', () => {
    const customTintColor = '#FF5500';
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          tintColor={customTintColor}
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    // The custom tint color should be used
    expect(getByTestId('outlined-text-field').props.tintColor).toBe(customTintColor);
  });

  it('passes value prop correctly', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          value="Test Value"
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    expect(getByTestId('outlined-text-field').props.value).toBe('Test Value');
  });

  it('handles text changes correctly', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    // Simulate text change
    const textField = getByTestId('outlined-text-field');
    textField.props.onChangeText('New Value');
    
    // Verify callback was called with correct parameters
    expect(mockOnChangeValue).toHaveBeenCalledWith('testField', 'New Value');
  });

  it('displays error message when provided', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          error="This field is required"
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    expect(getByTestId('outlined-text-field').props.error).toBe('This field is required');
  });

  it('renders with secure text entry when isSecure is true', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Password" 
          name="password"
          baseColor="#999999"
          isSecure={true}
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    expect(getByTestId('outlined-text-field').props.secureTextEntry).toBe(true);
  });

  it('renders right accessory when provided', () => {
    const { Text } = require('react-native');
    const RightAccessory = () => <Text>Right</Text>;
    
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          rightAccessory={RightAccessory}
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    expect(getByTestId('right-accessory')).toBeTruthy();
  });

  it('renders left accessory when provided', () => {
    const { Text } = require('react-native');
    const LeftAccessory = () => <Text>Left</Text>;
    
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          leftAccessory={LeftAccessory}
          onChangeValue={mockOnChangeValue}
        />
      </ThemeProvider>
    );

    expect(getByTestId('left-accessory')).toBeTruthy();
  });

  it('calls onSubmitEditing when submit is triggered', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTextField 
          label="Test Field" 
          name="testField"
          baseColor="#999999"
          onChangeValue={mockOnChangeValue}
          onSubmitEditing={mockOnSubmitEditing}
        />
      </ThemeProvider>
    );

    // Simulate submit
    getByTestId('outlined-text-field').props.onSubmitEditing();
    
    // Verify callback was called
    expect(mockOnSubmitEditing).toHaveBeenCalledTimes(1);
  });
});
