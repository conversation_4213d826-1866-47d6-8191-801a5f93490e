import { useTheme } from "@/theme";
import { View, Text, Image } from "react-native";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { ImageVariant } from "..";
const Icon = _Icon as React.ElementType
type Props = {
    label: string;
    icon: any;
    height: number;
    notifCount?: number;
}
function SideMenuItem(props: Props) {
    const {
		colors,
		variant,
		changeTheme,
		layout,
		gutters,
		fonts,
		components,
		backgrounds,
        borders
	} = useTheme();
    return (
        <View testID="side-menu-item-container" style={[layout.row, layout.flex_1, layout.justifyBetween, gutters.marginHorizontal_16, layout.itemsCenter, {height: props.height}]}>
            <View style={[layout.row, layout.itemsCenter]}>
                <Image testID="side-menu-item-icon" source={props.icon} style={{width: 20, height: 20}}/>
                <Text style={[gutters.marginLeft_12]}>{props.label}</Text>
                {
                    !!props.notifCount && (
                        <View testID="side-menu-item-badge" style={[backgrounds.red500, gutters.marginLeft_8, gutters.padding_8, borders.rounded_16, layout.itemsCenter, layout.justifyCenter]}>
                            <Text style={[fonts.size_12, fonts.white, layout.absolute]}>{props.notifCount}</Text>
                        </View>
                    )
                }
            </View>
            <Icon name='angle-right' size={20} color={colors.gray400}/>
        </View>
    )
}
export default SideMenuItem