import React from 'react';
import { render } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import SideMenuItem from '../SideMenuItem';

// Mock FontAwesome icon
jest.mock('react-native-vector-icons/FontAwesome', () => {
  const { Text } = require('react-native');
  return function MockIcon(props) {
    return <Text testID={`${props.name}-icon`}>{props.name}</Text>;
  };
});

// Mock useTheme
jest.mock('@/theme', () => ({
  ...jest.requireActual('@/theme'),
  useTheme: jest.fn().mockReturnValue({
    colors: {
      gray400: '#bdbdbd'
    },
    layout: {
      row: { flexDirection: 'row' },
      flex_1: { flex: 1 },
      justifyBetween: { justifyContent: 'space-between' },
      itemsCenter: { alignItems: 'center' },
      absolute: { position: 'absolute' },
      justifyCenter: { justifyContent: 'center' }
    },
    gutters: {
      marginHorizontal_16: { marginHorizontal: 16 },
      marginLeft_12: { marginLeft: 12 },
      marginLeft_8: { marginLeft: 8 },
      padding_8: { padding: 8 }
    },
    fonts: {
      size_12: { fontSize: 12 },
      white: { color: '#ffffff' }
    },
    backgrounds: {
      red500: { backgroundColor: '#f44336' }
    },
    borders: {
      rounded_16: { borderRadius: 16 }
    }
  })
}));

describe('SideMenuItem Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  // Mock props
  const mockProps = {
    label: 'Test Menu Item',
    icon: { uri: 'https://example.com/icon.png' },
    height: 48
  };

  beforeEach(() => {
    storage = new MMKV();
  });

  it('renders correctly with required props', () => {
    const { getByText, getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SideMenuItem 
          label={mockProps.label} 
          icon={mockProps.icon} 
          height={mockProps.height} 
        />
      </ThemeProvider>
    );

    expect(getByText('Test Menu Item')).toBeTruthy();
    
    const icon = getByTestId('side-menu-item-icon');
    expect(icon.props.source).toBe(mockProps.icon);
    
    expect(getByTestId('angle-right-icon')).toBeTruthy();
  });

  it('renders notification count when provided', () => {
    const { getByText, getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SideMenuItem 
          label={mockProps.label} 
          icon={mockProps.icon} 
          height={mockProps.height} 
          notifCount={5}
        />
      </ThemeProvider>
    );

    // Check if the notification count is rendered
    expect(getByText('5')).toBeTruthy();
    
    // Check if the notification badge is rendered
    const badge = getByTestId('side-menu-item-badge');
    expect(badge).toBeTruthy();
  });

  it('does not render notification count when not provided', () => {
    const { queryByTestId } = render(
      <ThemeProvider storage={storage}>
        <SideMenuItem 
          label={mockProps.label} 
          icon={mockProps.icon} 
          height={mockProps.height}
        />
      </ThemeProvider>
    );

    // Check that the notification badge is not rendered
    expect(queryByTestId('side-menu-item-badge')).toBeNull();
  });

  it('applies correct height to the container', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SideMenuItem 
          label={mockProps.label} 
          icon={mockProps.icon} 
          height={mockProps.height}
        />
      </ThemeProvider>
    );

    const container = getByTestId('side-menu-item-container');
    
    // Check that the container has the correct height
    expect(container.props.style).toContainEqual({ height: 48 });
  });

  it('applies correct styles to the icon', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SideMenuItem 
          label={mockProps.label} 
          icon={mockProps.icon} 
          height={mockProps.height}
        />
      </ThemeProvider>
    );

    const icon = getByTestId('side-menu-item-icon');
    
    // Check icon styles
    expect(icon.props.style).toEqual({ width: 20, height: 20 });
  });

  it('applies correct styles to the notification badge', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SideMenuItem 
          label={mockProps.label} 
          icon={mockProps.icon} 
          height={mockProps.height}
          notifCount={5}
        />
      </ThemeProvider>
    );

    const badge = getByTestId('side-menu-item-badge');
    
    // Check badge styles
    expect(badge.props.style).toContainEqual({ backgroundColor: '#f44336' });
    expect(badge.props.style).toContainEqual({ marginLeft: 8 });
    expect(badge.props.style).toContainEqual({ padding: 8 });
    expect(badge.props.style).toContainEqual({ borderRadius: 16 });
    expect(badge.props.style).toContainEqual({ alignItems: 'center' });
    expect(badge.props.style).toContainEqual({ justifyContent: 'center' });
  });

  it('applies correct styles to the notification count', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <SideMenuItem 
          label={mockProps.label} 
          icon={mockProps.icon} 
          height={mockProps.height}
          notifCount={5}
        />
      </ThemeProvider>
    );

    const count = getByText('5');
    
    // Check count styles
    expect(count.props.style).toContainEqual({ fontSize: 12 });
    expect(count.props.style).toContainEqual({ color: '#ffffff' });
    expect(count.props.style).toContainEqual({ position: 'absolute' });
  });
});