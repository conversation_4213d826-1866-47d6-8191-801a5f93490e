import React, { useEffect, useState } from 'react';
import { Dimensions, Pressable, StyleSheet, View, Keyboard, Platform, EmitterSubscription } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/theme';

const { height } = Dimensions.get('window');

type Props = {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
};

function AnimatedViewBottomSheet({ visible, onClose, children }: Props) {
  const [isMounted, setIsMounted] = useState(visible);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  
  const translateY = useSharedValue(height);
  const backdropOpacity = useSharedValue(0);
  const keyboardOffset = useSharedValue(0);

  const { layout, backgrounds } = useTheme();
  const { bottom } = useSafeAreaInsets();

  useEffect(() => {
    const keyboardWillShow = (event: any) => {
      const keyboardHeight = event.endCoordinates.height;
      setKeyboardHeight(keyboardHeight);
      keyboardOffset.value = withTiming(-keyboardHeight, {
        duration: event.duration || 250,
        easing: Easing.out(Easing.quad),
      });
    };

    const keyboardWillHide = (event: any) => {
      setKeyboardHeight(0);
      keyboardOffset.value = withTiming(0, {
        duration: event.duration || 250,
        easing: Easing.out(Easing.quad),
      });
    };

    const keyboardDidShow = (event: any) => {
      if (Platform.OS === 'android') {
        const keyboardHeight = event.endCoordinates.height;
        setKeyboardHeight(keyboardHeight);
        keyboardOffset.value = withTiming(-keyboardHeight, {
          duration: 250,
          easing: Easing.out(Easing.quad),
        });
      }
    };

    const keyboardDidHide = () => {
      if (Platform.OS === 'android') {
        setKeyboardHeight(0);
        keyboardOffset.value = withTiming(0, {
          duration: 250,
          easing: Easing.out(Easing.quad),
        });
      }
    };

    // Use appropriate events based on platform
    const showEvent = Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow';
    const hideEvent = Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide';

    const showSubscription = Keyboard.addListener(showEvent, keyboardWillShow);
    const hideSubscription = Keyboard.addListener(hideEvent, keyboardWillHide);

    // For Android, also listen to the did events as backup
    let didShowSubscription: EmitterSubscription | undefined, didHideSubscription: EmitterSubscription | undefined;;
    if (Platform.OS === 'android') {
      didShowSubscription = Keyboard.addListener('keyboardDidShow', keyboardDidShow);
      didHideSubscription = Keyboard.addListener('keyboardDidHide', keyboardDidHide);
    }

    return () => {
      showSubscription?.remove();
      hideSubscription?.remove();
      didShowSubscription?.remove();
      didHideSubscription?.remove();
    };
  }, []);

  useEffect(() => {
    if (visible) {
      setIsMounted(true);
      translateY.value = withTiming(0, {
        duration: 300,
        easing: Easing.out(Easing.exp),
      });
      backdropOpacity.value = withTiming(0.5, { duration: 300 });
    } else {
      translateY.value = withTiming(height, { duration: 300 });
      backdropOpacity.value = withTiming(0, { duration: 300 }, () => {
        runOnJS(setIsMounted)(false);
        runOnJS(onClose)();
      });
    }
  }, [visible]);

  const modalStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: translateY.value },
      { translateY: keyboardOffset.value }
    ],
  }));

  const backdropStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
  }));

  function onBackdropPress() {
    // Dismiss keyboard first if it's open
    Keyboard.dismiss();
    
    translateY.value = withTiming(height, { duration: 300 });
    backdropOpacity.value = withTiming(0, { duration: 300 }, () => {
      runOnJS(setIsMounted)(false);
      runOnJS(onClose)();
    });
  }

  if (!isMounted) return null;

  return (
    <View style={[layout.absolute, { top: 0, left: 0, right: 0, bottom: 0, zIndex: 1000 }]}>
      <Pressable style={StyleSheet.absoluteFill} onPress={onBackdropPress}>
        <Animated.View
          style={[
            layout.absolute,
            backgrounds.black,
            backdropStyle,
            { top: 0, bottom: 0, left: 0, right: 0, zIndex: 5 },
          ]}
        />
      </Pressable>
      <Animated.View
        style={[
          layout.absolute,
          modalStyle,
          backgrounds.white,
          {
            zIndex: 10,
            bottom: 0,
            left: 0,
            right: 0,
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
            paddingBottom: bottom || 8,
            maxHeight: height - (keyboardHeight > 0 ? 50 : 0), // Leave some space when keyboard is open
          },
        ]}
      >
        {children}
      </Animated.View>
    </View>
  );
}

export default AnimatedViewBottomSheet;