import React from 'react';
import { render } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AKScreenBlocker from '../AKScreenBlocker';

describe('AKScreenBlocker Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  beforeEach(() => {
    storage = new MMKV();
  });

  it('renders correctly when visible', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKScreenBlocker isVisible={true} testID="screen-blocker" />
      </ThemeProvider>
    );

    const blocker = getByTestId('screen-blocker');
    expect(blocker).toBeTruthy();
    
    // Check if the blocker has the correct style
    expect(blocker.props.style).toEqual({
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    });
  });

  it('does not render when not visible', () => {
    const { queryByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKScreenBlocker isVisible={false} testID="screen-blocker" />
      </ThemeProvider>
    );

    // Should not find the blocker in the DOM
    const blocker = queryByTestId('screen-blocker');
    expect(blocker).toBeNull();
  });

  it('toggles visibility correctly', () => {
    const { rerender, queryByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKScreenBlocker isVisible={true} testID="screen-blocker" />
      </ThemeProvider>
    );

    // Initially visible
    expect(queryByTestId('screen-blocker')).toBeTruthy();

    // Update props to hide
    rerender(
      <ThemeProvider storage={storage}>
        <AKScreenBlocker isVisible={false} testID="screen-blocker" />
      </ThemeProvider>
    );

    // Should now be hidden
    expect(queryByTestId('screen-blocker')).toBeNull();

    // Update props to show again
    rerender(
      <ThemeProvider storage={storage}>
        <AKScreenBlocker isVisible={true} testID="screen-blocker" />
      </ThemeProvider>
    );

    // Should be visible again
    expect(queryByTestId('screen-blocker')).toBeTruthy();
  });
});