import React from 'react';
import { render } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import ImageVariant from './ImageVariant';

// Mock theme hook
const mockChangeTheme = jest.fn();
let mockVariant = 'default';

jest.mock('@/theme', () => ({
  ...jest.requireActual('@/theme'),
  useTheme: jest.fn(() => ({
	variant: mockVariant,
	changeTheme: mockChangeTheme
  }))
}));

describe('ImageVariant Component', () => {
  let storage: MMKV;
  
  // Mock image sources
  const mockDefaultSource = { uri: 'https://example.com/default.png' };
  const mockDarkSource = { uri: 'https://example.com/dark.png' };
  const mockLightSource = { uri: 'https://example.com/light.png' };
  
  beforeEach(() => {
	storage = new MMKV();
	mockVariant = 'default';
	jest.clearAllMocks();
  });

  it('renders with default source when variant is default', () => {
	mockVariant = 'default';
	
	const { getByTestId } = render(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  sourceDark={mockDarkSource}
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);

	const image = getByTestId('variant-image');
	expect(image.props.source).toBe(mockDefaultSource);
  });

  it('renders with dark source when variant is dark', () => {
	mockVariant = 'dark';
	
	const { getByTestId } = render(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  sourceDark={mockDarkSource}
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);

	const image = getByTestId('variant-image');
	expect(image.props.source).toBe(mockDarkSource);
  });

  it('renders with light source when variant is light', () => {
	mockVariant = 'light';
	
	const { getByTestId } = render(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  sourceDark={mockDarkSource}
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);

	const image = getByTestId('variant-image');
	expect(image.props.source).toBe(mockLightSource);
  });

  it('falls back to default source when variant source is not provided', () => {
	mockVariant = 'dark';
	
	const { getByTestId } = render(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  // No sourceDark provided
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);

	const image = getByTestId('variant-image');
	expect(image.props.source).toBe(mockDefaultSource);
  });

	// it('falls back to default source when accessing variant source throws an error', () => {
	// mockVariant = 'dark';

	// // Create a proxy for the *entire props object*
	// const propsProxy = new Proxy({}, {
	// 	get: (target, prop) => {
	// 	// This ensures it throws when 'sourceDark' is accessed
	// 	if (prop === 'sourceDark') {
	// 		throw new Error('Test error from proxy');
	// 	}

	// 	// Pass through to any other expected prop
	// 	if (prop === 'source') return mockDefaultSource;
	// 	if (prop === 'sourceLight') return mockLightSource;

	// 	return undefined;
	// 	}
	// });

	// const { getByTestId } = render(
	// 	<ThemeProvider storage={storage}>
	// 	<ImageVariant {...(propsProxy as any)} />
	// 	</ThemeProvider>
	// );

	// const image = getByTestId('variant-image');
	// expect(image.props.source).toBe(mockDefaultSource);
	// });


  it('passes additional props to the Image component', () => {
	const { getByTestId } = render(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  resizeMode="cover"
		  style={{ width: 100, height: 100 }}
		/>
	  </ThemeProvider>
	);

	const image = getByTestId('variant-image');
	expect(image.props.resizeMode).toBe('cover');
	expect(image.props.style).toEqual({ width: 100, height: 100 });
  });

  it('updates source when variant changes', () => {
	mockVariant = 'default';
	
	const { getByTestId, rerender } = render(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  sourceDark={mockDarkSource}
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);

	let image = getByTestId('variant-image');
	expect(image.props.source).toBe(mockDefaultSource);
	
	// Change variant to dark
	mockVariant = 'dark';
	
	rerender(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  sourceDark={mockDarkSource}
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);
	
	image = getByTestId('variant-image');
	expect(image.props.source).toBe(mockDarkSource);
  });

  it('updates source when defaultSource changes', () => {
	const newDefaultSource = { uri: 'https://example.com/new-default.png' };
	
	const { getByTestId, rerender } = render(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={mockDefaultSource}
		  sourceDark={mockDarkSource}
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);

	let image = getByTestId('variant-image');
	expect(image.props.source).toBe(mockDefaultSource);
	
	// Change default source
	rerender(
	  <ThemeProvider storage={storage}>
		<ImageVariant 
		  source={newDefaultSource}
		  sourceDark={mockDarkSource}
		  sourceLight={mockLightSource}
		/>
	  </ThemeProvider>
	);
	
	image = getByTestId('variant-image');
	expect(image.props.source).toBe(newDefaultSource);
  });
});



// import { render, screen } from '@testing-library/react-native';
// import { MMKV } from 'react-native-mmkv';

// import { ThemeProvider } from '@/theme';
// import sourceLight from '@/theme/assets/images/tom_light.png';
// import sourceDark from '@/theme/assets/images/tom_dark.png';

// import { isImageSourcePropType } from '@/types/guards/image';

// import ImageVariant from './ImageVariant';

// describe('ImageVariant component should render correctly', () => {
// 	let storage: MMKV;

// 	beforeAll(() => {
// 		storage = new MMKV();
// 	});

// 	test('with only default image and dark variant. Should return default source', () => {
// 		if (!isImageSourcePropType(sourceLight)) {
// 			throw new Error('Image source is not valid');
// 		}

// 		const component = (
// 			<ThemeProvider storage={storage}>
// 				<ImageVariant source={sourceLight} />
// 			</ThemeProvider>
// 		);

// 		render(component);

// 		const wrapper = screen.getByTestId('variant-image');

// 		expect(wrapper.props.source).toBe(sourceLight);
// 	});

// 	test('with default image dark image and dark variant. Should return dark source', () => {
// 		storage.set('theme', 'dark');
// 		if (!isImageSourcePropType(sourceDark)) {
// 			throw new Error('Image source is not valid');
// 		}

// 		const component = (
// 			<ThemeProvider storage={storage}>
// 				<ImageVariant source={sourceDark} sourceDark={sourceDark} />
// 			</ThemeProvider>
// 		);

// 		render(component);

// 		const wrapper = screen.getByTestId('variant-image');

// 		expect(wrapper.props.source).toBe(sourceDark);
// 	});
// });
