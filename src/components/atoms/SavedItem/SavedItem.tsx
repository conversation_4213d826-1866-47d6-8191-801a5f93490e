import { View, TouchableOpacity } from 'react-native';
import { moduleSchema } from '@/types/schemas/module';
import { useTheme } from '@/theme';
import FastImage from 'react-native-fast-image';
import ImageVariant from '../ImageVariant/ImageVariant';
import { colorTokens } from '@/theme/colorTokens';
import AKFastImage from '../AKFastImage/AKFastImage';

type Props = {
	item?: (typeof moduleSchema)['_output'];
	onItemPressed: (item: any) => void;
	icon?: any;
};
function SavedItem(props: Props) {
	const { layout, gutters, borders, backgrounds, colors } = useTheme();
	const c = colorTokens();

	const { item, onItemPressed, icon } = props;
	return (
		<TouchableOpacity
			testID="saved-item-container"
			style={[{ flex: 1 / 4 }, gutters.marginVertical_12]}
			onPress={() => onItemPressed(item)}
		>
			<View
				style={[
					borders.rounded_16,
					{ aspectRatio: 1, overflow: 'hidden' },
					backgrounds.green50,
				]}
			>
				<AKFastImage
					uri={item?.thumbnail}
					style={[layout.flex_1, borders.rounded_4, backgrounds.green50]}
				/>
				<ImageVariant
					testID="saved-item-icon"
					source={icon}
					style={[
						{
							tintColor: c.content.primary.default,
							position: 'absolute',
							bottom: 8,
							left: 8,
						},
					]}
				/>
			</View>
		</TouchableOpacity>
	);
}

export default SavedItem;
