import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import SavedItem from '../SavedItem';

// Mock FastImage
jest.mock('react-native-fast-image', () => {
  const { Image } = require('react-native');
  return Image;
});

// Mock ImageVariant
jest.mock('../../ImageVariant/ImageVariant', () => {
  const { Image } = require('react-native');
  return Image;
});

// Mock colorTokens
jest.mock('@/theme/colorTokens', () => ({
  colorTokens: jest.fn().mockReturnValue({
    content: {
      primary: {
        default: '#1E88E5'
      }
    }
  })
}));

// Mock useTheme
jest.mock('@/theme', () => ({
  ...jest.requireActual('@/theme'),
  useTheme: jest.fn().mockReturnValue({
    layout: {
      flex_1: { flex: 1 }
    },
    gutters: {
      marginVertical_12: { marginVertical: 12 }
    },
    borders: {
      rounded_16: { borderRadius: 16 },
      rounded_4: { borderRadius: 4 }
    },
    backgrounds: {
      green50: { backgroundColor: '#E8F5E9' }
    },
    colors: {}
  })
}));

describe('SavedItem Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  // Mock item data
  const mockItem = {
    id: 1,
    title: 'Test Module',
    thumbnail: 'https://example.com/image.jpg',
    bg_color: '#f5f5f5',
    logo_color: '#ff0000',
    slug: 'test-module',
    is_active: true
  };
  
  // Mock onItemPressed function
  const mockOnItemPressed = jest.fn();
  
  // Mock icon
  const mockIcon = { uri: 'https://example.com/icon.png' };

  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly with required props', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SavedItem 
          item={mockItem} 
          onItemPressed={mockOnItemPressed}
          icon={mockIcon}
          testID="saved-item"
        />
      </ThemeProvider>
    );

    // Check if the FastImage component is rendered with the correct source
    const fastImage = getByTestId('thumbnail-image');
    expect(fastImage.props.source).toEqual({ uri: 'https://example.com/image.jpg' });
    
    // Check if the icon is rendered with the correct source
    const icon = getByTestId('saved-item-icon');
    expect(icon.props.source).toBe(mockIcon);
  });

  it('calls onItemPressed when pressed', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SavedItem 
          item={mockItem} 
          onItemPressed={mockOnItemPressed}
          icon={mockIcon}
          testID="saved-item"
        />
      </ThemeProvider>
    );

    // Press the item
    fireEvent.press(getByTestId('saved-item-container'));
    
    // Verify callback was called with the correct item
    expect(mockOnItemPressed).toHaveBeenCalledWith(mockItem);
  });

  it('applies correct styles to the container', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SavedItem 
          item={mockItem} 
          onItemPressed={mockOnItemPressed}
          icon={mockIcon}
          testID="saved-item"
        />
      </ThemeProvider>
    );

    const touchable = getByTestId('saved-item-container');
    
    // Check touchable styles
    console.log(touchable.props.style);
    expect(touchable.props.style).toMatchObject({ flex: 1/4, marginVertical: 12, opacity: 1 });
  });

  it('applies correct styles to the image', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SavedItem 
          item={mockItem} 
          onItemPressed={mockOnItemPressed}
          icon={mockIcon}
          testID="saved-item"
        />
      </ThemeProvider>
    );

    const image = getByTestId('thumbnail-image');
    
    // Check image styles
    expect(image.props.style).toContainEqual({ flex: 1 });
    expect(image.props.style).toContainEqual({ borderRadius: 4 });
    expect(image.props.style).toContainEqual({ backgroundColor: '#E8F5E9' });
  });

  it('applies correct styles to the icon', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SavedItem 
          item={mockItem} 
          onItemPressed={mockOnItemPressed}
          icon={mockIcon}
          testID="saved-item"
        />
      </ThemeProvider>
    );

    const icon = getByTestId('saved-item-icon');
    
    // Check icon styles

    expect(icon.props.style).toEqual(expect.arrayContaining([
      { tintColor: '#1E88E5', position: 'absolute', bottom: 8, left: 8 }
    ]));
  });

  it('handles undefined item gracefully', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SavedItem 
          onItemPressed={mockOnItemPressed}
          icon={mockIcon}
          testID="saved-item"
        />
      </ThemeProvider>
    );

    // Check that the component still renders
    expect(getByTestId('saved-item-container')).toBeTruthy();
    
    // Check that the image source is undefined
    const fastImage = getByTestId('thumbnail-image');
    expect(fastImage.props.source).toEqual({ uri: undefined });
  });

  it('uses default placeholder image', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <SavedItem 
          item={mockItem} 
          onItemPressed={mockOnItemPressed}
          icon={mockIcon}
          testID="saved-item"
        />
      </ThemeProvider>
    );

    const image = getByTestId('thumbnail-image');
    
    // Check that the default source is set
    expect(image.props.defaultSource).toBeDefined();
  });
});