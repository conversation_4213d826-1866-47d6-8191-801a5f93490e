import { useTheme } from "@/theme";
import { View, TouchableOpacity, Text } from "react-native";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "../AKIcon/AKIcon";
import TickIcon from "@/theme/assets/images/TickIcon.png";

type Props = {
    id: number;
    text: string;
    isChecked: boolean;
    onPressOption: (option: number) => void;
}
function CheckBox(props: Props) {

    const { id, text, isChecked, onPressOption } = props;

    const {
        layout,
        gutters,
        fonts,
    } = useTheme();

    const c = colorTokens();

    function onPress(): void {
        onPressOption(id);
    }

    let checkboxStyles;
    if (isChecked) {
        checkboxStyles = {
            backgroundColor: c.fill.bold.primary.rest,
        };
    } else {
        checkboxStyles = {
            backgroundColor: 'white',
            borderWidth: 1,
            borderColor: c.stoke.default.default,
        };
    }

    return (
        <TouchableOpacity onPress={onPress} style={[layout.itemsCenter, layout.row]}>
            <View style={[layout.itemsCenter, layout.justifyCenter, { borderRadius: 2, width: 16, height: 16, ...checkboxStyles }]}>
                <AKIcon source={TickIcon} size={12} />
            </View>
            <Text style={[layout.flex_1, fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, gutters.marginLeft_8, { fontWeight: '500', color: c.content.default.default }]}>{text}</Text>
        </TouchableOpacity>
    )
}

export default CheckBox;
