import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AccountItem from '../AccountItem';

// Mock the dependencies
jest.mock('react-native-vector-icons/FontAwesome', () => 'Icon');
jest.mock('@/theme/colorTokens', () => ({
  colorTokens: jest.fn().mockReturnValue({
    content: {
      default: {
        default: '#333333'
      }
    }
  })
}));

describe('AccountItem Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  // Mock item props
  const mockItem = {
    icon: { uri: 'https://example.com/icon.png' },
    label: 'Test Item',
    section: 0,
    index: 0,
    onPressAction: jest.fn()
  };
  
  // Mock onItemPressed function
  const mockOnItemPressed = jest.fn();

  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly with required props', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AccountItem item={mockItem} onItemPressed={mockOnItemPressed} />
      </ThemeProvider>
    );

    // Check if the label is rendered
    expect(getByText('Test Item')).toBeTruthy();
  });

  it('calls onItemPressed with the item when pressed', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AccountItem item={mockItem} onItemPressed={mockOnItemPressed} />
      </ThemeProvider>
    );

    // Simulate press on the item
    fireEvent.press(getByText('Test Item').parent.parent);

    // Check if onItemPressed was called with the correct item
    expect(mockOnItemPressed).toHaveBeenCalledWith(mockItem);
    expect(mockOnItemPressed).toHaveBeenCalledTimes(1);
  });

  it('renders the icon with correct dimensions', () => {
    const { UNSAFE_getAllByType } = render(
      <ThemeProvider storage={storage}>
        <AccountItem item={mockItem} onItemPressed={mockOnItemPressed} />
      </ThemeProvider>
    );

    // Find the Image component
    const images = UNSAFE_getAllByType('Image');
    const itemIcon = images[0];
    
    // Check if the image has the correct style properties
    expect(itemIcon.props.style).toEqual({
      width: 20,
      height: 20
    });
    
    // Check if the source is correct
    expect(itemIcon.props.source).toBe(mockItem.icon);
  });

  it('renders the chevron icon', () => {
    const { getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AccountItem item={mockItem} onItemPressed={mockOnItemPressed} />
      </ThemeProvider>
    );

    // Find the chevron icon by test ID
    const chevronIcon = getByTestId('account-item-chevron');
    expect(chevronIcon).toBeTruthy();
  });

  it('applies correct styling to the item label', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AccountItem item={mockItem} onItemPressed={mockOnItemPressed} />
      </ThemeProvider>
    );

    const label = getByText('Test Item');
    
    // Check if the label has the correct style properties
    expect(label.props.style).toEqual(
      expect.arrayContaining([
        expect.anything(), // fonts.fontSizes.body.sm
        expect.anything(), // fonts.lineHeight.body.sm
        expect.anything(), // fonts.body
        expect.objectContaining({ marginLeft: 12 }), // gutters.marginLeft_12
        expect.objectContaining({ 
          fontWeight: '500',
          color: expect.any(String) 
        })
      ])
    );
  });
});
