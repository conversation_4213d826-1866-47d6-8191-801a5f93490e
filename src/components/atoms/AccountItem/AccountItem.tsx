import { useTheme } from "@/theme";
import { View, Text, Image } from "react-native";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { ImageVariant } from "..";
import { ProfileItem } from "@/components/molecules/AccountSection/AccountSection";
import { TouchableOpacity } from "react-native-gesture-handler";
import AKIcon from "../AKIcon/AKIcon";
import ChevronRightIcon from "@/theme/assets/images/ChevronRightIcon.png";
import { colorTokens } from "@/theme/colorTokens";

type Props = {
    onItemPressed: (item: ProfileItem) => void
    item: ProfileItem
}
function AccountItem(props: Props) {

    const {
		colors,
		layout,
		gutters,
		fonts,
	} = useTheme();

    const c = colorTokens();

    return (
        <TouchableOpacity onPress={() => props.onItemPressed(props.item)} style={[layout.row, layout.justifyBetween, gutters.paddingHorizontal_16, gutters.paddingVertical_16, layout.itemsCenter]}>
            <View style={[layout.row, layout.itemsCenter, layout.flex_1]}>
                <Image source={props.item.icon} tintColor={colors.black} style={{width: 20, height: 20}}/>
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, gutters.marginLeft_12, {color: c.content.default.default}]}>{props.item.label}</Text>
                <View style={[layout.flex_1, { alignItems: 'flex-end' }]}>
                    <AKIcon testID="account-item-chevron" source={ChevronRightIcon} size={20} tintColor={colors.black}/>
                </View>
            </View>
        </TouchableOpacity>
    )
}
export default AccountItem