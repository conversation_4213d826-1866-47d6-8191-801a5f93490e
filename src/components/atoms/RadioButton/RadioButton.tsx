import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { View, Text, TouchableOpacity } from "react-native";

type Props = {
    label: string;
    isSelected: boolean;
    onPress: () => void;
    info: string;
}

function RadioButton(props: Props) {

    const { label, isSelected, onPress, info } = props;

    const { layout, fonts, gutters } = useTheme();
    const c = colorTokens();

    return (
        <TouchableOpacity onPress={onPress} style={[layout.row]}>
            <View style={[layout.itemsCenter, layout.justifyCenter, gutters.marginTop_4, { height: 16, width: 16, borderWidth: isSelected ? 2 : 1, borderRadius: 16, borderColor: isSelected ? c.fill.bold.primary.rest : c.stoke.default.default, backgroundColor: c.custom.white, marginRight: 10 }]}>
                <View style={[{ height: 8, width: 8, borderRadius: 8, backgroundColor: isSelected ? c.fill.bold.primary.rest : c.custom.white }]} />
            </View>
            <View style={[layout.flex_1]}>
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: c.content.default.emphasis }]}>{label}</Text>
                {
                    info && (
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Regular, { color: c.content.default.subdued }]}>{info}</Text>
                    )
                }
            </View>
        </TouchableOpacity>
    )
}

export default RadioButton;