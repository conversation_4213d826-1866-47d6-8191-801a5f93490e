import { TextInput, View, Text, Platform, TouchableOpacity, TextInputProps } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "../AKIcon/AKIcon";
import { useState } from "react";
import TextInputShowIcon from "@/theme/assets/images/TextInputShowIcon.png";
import TextInputHideIcon from "@/theme/assets/images/TextInputHideIcon.png";
import ExcmalimationCircleIcon from "@/theme/assets/images/ExcmalimationCircleIcon.png";

interface Props extends TextInputProps {
    label?: string;
    rightIcon?: any;
    onPressInput?: () => void;
    value?: string;
    isTextArea?: boolean;
    onChangeValue?: (label: string, text: string) => void;
    name: string;
    isPassword?: boolean;
    rules?: any[];
    info?: any;
    isReadOnly?: boolean;
}

function AKTextInput(props: Props) {

    const { label, rightIcon, onPressInput, value, isTextArea, onChangeValue, name, isPassword, rules, info, isReadOnly, ...rest } = props;

    const { layout, gutters, fonts, colors } = useTheme();
    const c = colorTokens();

    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);

    function onChangeText(text: string): void {
        if (onChangeValue) {
            onChangeValue(name, text);
        }
    }

    const ParentView: React.ElementType = !!onPressInput ? TouchableOpacity : View;

    return (
        <View>
            {
                label && (
                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, gutters.marginBottom_4, { color: isReadOnly ? c.content.default.disabled : c.content.default.emphasis }]}>{label}</Text>
                )
            }
            <ParentView {...(onPressInput ? { onPress: onPressInput } : {})} style={[layout.row, layout.itemsCenter, {borderWidth: 1, borderColor: c.stoke.default.default, borderRadius: 8, paddingVertical: Platform.OS === 'ios' ? 10 : 6, paddingHorizontal: 12, gap: 12 }]}>
                {
                    !onPressInput ? (
                        <TextInput
                            multiline={isTextArea}
                            editable={!isReadOnly}
                            value={value}
                            onFocus={() => setIsFocused(true)}
                            onBlur={() => setIsFocused(false)}
                            secureTextEntry={isPassword && !showPassword}
                            onChangeText={(text: string) => onChangeText(text)}
                            style={[fonts.fontSizes.body.lg, fonts.Regular, { flex: 1, color: isReadOnly ? c.content.default.disabled : c.content.default.default, padding: 0}]}
                            {...rest}
                        />
                    ) : (
                        <Text style={[fonts.fontSizes.body.lg, fonts.Regular, { flex: 1, color: c.content.default.default}]}>{props.value}</Text>
                    )
                }
                {
                    rightIcon && (
                        <AKIcon source={rightIcon} tintColor={c.content.default.subdued} size={20} styles={[gutters.marginLeft_12]} />
                    )
                }
                {
                    isPassword && (
                        <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                            <AKIcon source={showPassword ? TextInputHideIcon : TextInputShowIcon } tintColor={c.content.default.default} size={20} styles={[gutters.marginLeft_12]} />
                        </TouchableOpacity>
                    )
                }
            </ParentView>
            {
                (isFocused || value) && rules && (
                    <View style={[]}>
                        <View style={[layout.row, layout.itemsCenter, gutters.marginTop_4, { gap: 4 }]}>
                            <AKIcon source={ExcmalimationCircleIcon} tintColor={c.content.default.default} size={16} />
                            <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: c.content.default.subdued }]}>Password must</Text>
                        </View>
                        <View style={[{ marginLeft: 20 }]}>
                            {
                                rules.map((rule: any) => (
                                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: rule.value ? c.content.success.default : c.fill.bold.primary.rest }]}>{'\u2022'}  {rule.name}</Text>
                                ))
                            }
                        </View>
                    </View>
                )
            }
            {
                info?.message && (
                    <View style={[layout.row, layout.itemsCenter, gutters.marginTop_4, { gap: 4 }]}>
                        <AKIcon source={ExcmalimationCircleIcon} tintColor={c.content.default.default} size={16} />
                        <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: info.isValid ? c.content.success.default : c.fill.bold.primary.rest }]}>{info.message}</Text>
                    </View>
                )
            }
            {
                isReadOnly && (
                    <View style={[layout.row, layout.itemsCenter, gutters.marginTop_4, { gap: 4 }]}>
                        <AKIcon source={ExcmalimationCircleIcon} tintColor={c.content.default.disabled} size={16} />
                        <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: c.content.default.disabled }]}>{`${label} can not be updated`}</Text>
                    </View>
                )
            }
        </View>
    )
}

export default AKTextInput;