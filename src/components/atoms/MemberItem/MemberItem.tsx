import { useTheme } from "@/theme";
import { View, TouchableOpacity, Text } from "react-native";
import _Icon from 'react-native-vector-icons/FontAwesome';
import MoreIcon from "@/theme/assets/images/SocialConnect/MoreIcon.png";
import AKIcon from "../AKIcon/AKIcon";
import { colorTokens } from "@/theme/colorTokens";
import FastImage from "react-native-fast-image";
import { useNavigation } from "@react-navigation/native";
import { z } from "zod"
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";
import { basicUserSchema } from "@/types/schemas/user";
import DefaultprofileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png"
import AKFastImage from "../AKFastImage/AKFastImage";

type Props = {
    onPressMore?: (item: z.infer<typeof socialConnectUserSchema>) => void
    callBack?: () => void
    item: z.infer<typeof basicUserSchema>
    extraInfoText?: string;
    rightView?: JSX.Element;
    isFromBlocked?: boolean
}

function MemberItem(props: Props) {

    const { extraInfoText, item, onPressMore, rightView } = props;
    const { first_name: firstName, last_name: lastName, id } = item || {};

    const navigation = useNavigation();

    const {
        layout,
        gutters,
        fonts,
    } = useTheme();

    const c = colorTokens();

    function onPress(): void {
        if (onPressMore) {
            onPressMore(item);
        }
    }

    function onPressItem(): void {
        if (props.isFromBlocked == null || props.isFromBlocked == false) {
            if (props.callBack != null) {
                props.callBack()
            }
            navigation.push('UserProfile', { userId: id })
        }
    }

    const userName = `${firstName} ${lastName}`;

    return (
        <TouchableOpacity onPress={onPressItem} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_4, { gap: 12 }]}>
            <AKFastImage
                uri={item?.profile_photo}
                placeholder={DefaultprofileIcon}
                resizeMode={FastImage.resizeMode.contain}
                style={[{width: 40, height: 40, borderRadius: 50,  backgroundColor: 'black'}]}
            />
            <View style={[layout.flex_1, layout.justifyCenter, { gap: 4 }]}>
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: c.content.default.default }]}>{userName}</Text>
                {
                    extraInfoText && (
                        <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: c.content.default.subdued }]}>{extraInfoText}</Text>
                    )
                }
            </View>
            {
                !!onPressMore && (
                    <AKIcon source={MoreIcon} size={20} tintColor="black" onPress={onPress} />
                )
            }
            {
                !!rightView && rightView
            }
        </TouchableOpacity>
    )
}

export default MemberItem;
