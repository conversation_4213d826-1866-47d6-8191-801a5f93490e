import { useTheme } from "@/theme";
import { View, TouchableOpacity, TextInput, Text } from "react-native";
import _Icon from 'react-native-vector-icons/FontAwesome';
import CrossIcon from "@/theme/assets/images/SocialConnect/CrossIcon.png";
import MagnifyingGlassIcon from "@/theme/assets/images/SocialConnect/MagnifyingGlassIcon.png";
import AKIcon from "../AKIcon/AKIcon";
import { colorTokens } from "@/theme/colorTokens";

type Props = {
    value?: string;
    setValue?: (text: string) => void;
    placeholderText: string;
    onPressBar?: () => void
}
function SearchBar(props: Props) {

    const { onPressBar, value, setValue, placeholderText } = props;

    const {
		layout,
		gutters,
        borders
	} = useTheme();

    const c = colorTokens();

    function onPressCross(): void {
        setValue("");
    }

    const ParentView: React.ElementType = !!onPressBar ? TouchableOpacity : View;

    return (
        <ParentView {...(onPressBar ? { onPress: onPressBar } : {})} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_8, gutters.paddingHorizontal_12, borders.rounded_50, { backgroundColor: c.background.default.neutrals.secondary, gap: 8 }]}>
            <AKIcon source={MagnifyingGlassIcon} tintColor={!!onPressBar ? c.content.default.subdued : undefined} size={20} />
            <View style={[layout.flex_1, layout.justifyCenter, { height: 24 }]}>
                {
                    !onPressBar ? (
                        <TextInput
                            value={value}
                            onChangeText={e => setValue(e)}
                            style={[layout.flex_1]}
                            placeholder={placeholderText}
                        />
                    ) : (
                        <Text style={[{ color: c.content.default.subdued }]}>{placeholderText}</Text>
                    )
                }
            </View>
            {
                value && (
                    <AKIcon source={CrossIcon} size={20} onPress={onPressCross} />
                )
            }
        </ParentView>
    )
}

export default SearchBar;
