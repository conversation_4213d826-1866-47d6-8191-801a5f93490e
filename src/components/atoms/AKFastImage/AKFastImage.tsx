import React, { useEffect, useState } from 'react';
import { View, Image, ImageSourcePropType, StyleProp, ImageStyle } from 'react-native';
import FastImage, { FastImageProps } from 'react-native-fast-image';

type AKFastImageProps = {
    uri?: string | null;
    style?: StyleProp<ImageStyle>;
    placeholder?: ImageSourcePropType;
    resizeMode?: FastImageProps['resizeMode'];
};

function AKFastImage({
    uri,
    style,
    placeholder = require('@/theme/assets/images/ProfilePlaceholder.png'),
    resizeMode = FastImage.resizeMode.cover,
}: AKFastImageProps) {

    const [isError, setIsError] = useState(false);

    useEffect(() => {
        if (uri == null) {
            setIsError(true);
            return;
        }
        setIsError(false);
    }, [uri]);

    return (
        <View style={[style, { overflow: 'hidden' }]}>
            {isError ? (
                <Image
                    source={placeholder}
                    style={{ height: '100%', width: '100%' }}
                    resizeMode={resizeMode}
                />
            ) : (
                <FastImage
                    source={{ uri }}
                    style={{ height: '100%', width: '100%' }}
                    resizeMode={resizeMode}
                    onLoadStart={() => {
                        setIsError(false);
                    }}
                    onError={() => {
                        setIsError(true);
                    }}
                />
            )}
        </View>
    );
}

export default AKFastImage;
