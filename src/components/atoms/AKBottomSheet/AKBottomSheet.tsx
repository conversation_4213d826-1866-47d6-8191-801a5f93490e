import { View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useSafeAreaInsets } from "react-native-safe-area-context";

type Props = {
    isModalVisible: any;
    setIsModalVisible: (isVisible: boolean) => void;
    children: React.ReactNode;
    modalProps?: any;
}

function AKBottomSheet(props: Props) {

    const { isModalVisible, setIsModalVisible, modalProps = {}, children } = props;

    const c = colorTokens();
    const { bottom } = useSafeAreaInsets();

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
    } = useTheme();

    function closeSheet() {
        setIsModalVisible(false);
    }

    return (
        <Modal
            propagateSwipe={true}
            isVisible={isModalVisible}
            animationIn="slideInUp"
            style={[layout.justifyEnd, gutters.margin_0]}
            swipeDirection={['down']}
            onBackdropPress={closeSheet}
            useNativeDriverForBackdrop={true}
            avoidKeyboard={true}
            {...modalProps}
        >
            <View style={[backgrounds.white, gutters.paddingTop_8, { borderTopRightRadius: 12, borderTopLeftRadius: 12, paddingBottom: bottom || 8, overflow: 'hidden', width: '100%' }]}>
                {children}
            </View>
        </Modal>
    )
}

export default AKBottomSheet;
