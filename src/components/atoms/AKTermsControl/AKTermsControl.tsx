import { useTheme } from "@/theme"
import { colorTokens } from "@/theme/colorTokens"
import { useTranslation } from "react-i18next"
import { Text, TouchableOpacity, View } from "react-native"

type Props = {
    isSelected: boolean
    radioButtonPressed: () => void
    termsButtonPressed: () => void
}
function AKTermsControl(props: Props){
    const {
		layout,
		gutters,
        borders,
	} = useTheme();
    
    const { t } = useTranslation(['common']);

    const c = colorTokens();

    return (
        <View style={[layout.row, gutters.marginLeft_16, gutters.marginTop_12]}>
            <TouchableOpacity testID="radio-button" onPress={props.radioButtonPressed}>
                <View style={[borders.black, {width: 18, height: 18, borderWidth: 1}]}>
                    { props.isSelected &&
                        <View testID="selected-indicator" style={[{margin: 2}, layout.flex_1, { backgroundColor: c.content.primary.default }]} />
                    }
                </View>
            </TouchableOpacity>
            
            <Text style={[gutters.marginLeft_12]}>{t("common:IAccept")}</Text>
            <TouchableOpacity onPress={props.termsButtonPressed}>
                <Text style={[{textDecorationLine: 'underline'}]}>{t("common:Terms")}</Text>
            </TouchableOpacity>
        </View>
    )
}
export default AKTermsControl