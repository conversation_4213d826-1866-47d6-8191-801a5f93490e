import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import AKTermsControl from '../AKTermsControl';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations = {
        'common:IAccept': 'I Accept',
        'common:Terms': 'Terms and Conditions'
      };
      return translations[key] || key;
    }
  })
}));

describe('AKTermsControl Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  const mockRadioButtonPressed = jest.fn();
  const mockTermsButtonPressed = jest.fn();
  
  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders correctly when not selected', () => {
    const { getByText, queryByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTermsControl 
          isSelected={false} 
          radioButtonPressed={mockRadioButtonPressed} 
          termsButtonPressed={mockTermsButtonPressed} 
        />
      </ThemeProvider>
    );

    // Check if text elements are rendered
    expect(getByText('I Accept')).toBeTruthy();
    expect(getByText('Terms and Conditions')).toBeTruthy();
    
    // Check that the selected indicator is not visible
    const selectedIndicator = queryByTestId('selected-indicator');
    expect(selectedIndicator).toBeFalsy();
  });

  it('renders correctly when selected', () => {
    const { queryByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTermsControl 
          isSelected={true} 
          radioButtonPressed={mockRadioButtonPressed} 
          termsButtonPressed={mockTermsButtonPressed} 
        />
      </ThemeProvider>
    );

    // Check that the selected indicator is visible
    const selectedIndicator = queryByTestId('selected-indicator');
    expect(selectedIndicator).toBeTruthy();
  });

  it('calls radioButtonPressed when radio button is pressed', () => {
    const { getByText, getByTestId } = render(
      <ThemeProvider storage={storage}>
        <AKTermsControl 
          isSelected={false} 
          radioButtonPressed={mockRadioButtonPressed} 
          termsButtonPressed={mockTermsButtonPressed} 
        />
      </ThemeProvider>
    );

    // Find the radio button and press it
    const radioButtonContainer = getByTestId('radio-button');
    fireEvent.press(radioButtonContainer);
    
    // Verify callback was called
    expect(mockRadioButtonPressed).toHaveBeenCalledTimes(1);
  });

  it('calls termsButtonPressed when terms text is pressed', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKTermsControl 
          isSelected={false} 
          radioButtonPressed={mockRadioButtonPressed} 
          termsButtonPressed={mockTermsButtonPressed} 
        />
      </ThemeProvider>
    );

    // Find the terms text and press it
    const termsText = getByText('Terms and Conditions');
    fireEvent.press(termsText);
    
    // Verify callback was called
    expect(mockTermsButtonPressed).toHaveBeenCalledTimes(1);
  });

  it('has underlined style for terms text', () => {
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <AKTermsControl 
          isSelected={false} 
          radioButtonPressed={mockRadioButtonPressed} 
          termsButtonPressed={mockTermsButtonPressed} 
        />
      </ThemeProvider>
    );

    const termsText = getByText('Terms and Conditions');
    
    // Check if the terms text has underline style
    expect(termsText.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ textDecorationLine: 'underline' })
      ])
    );
  });
});