import React, { useState } from 'react';
import { Text, TouchableOpacity, Linking, Alert } from 'react-native';
import { colorTokens } from "@/theme/colorTokens";
import { useTheme } from "@/theme";

type Props = {
    text: string;
    textColor?: string;
};

function ExpandableText({ text, textColor }: Props) {
    const {
        gutters,
        fonts,
    } = useTheme();

    const c = colorTokens();

    const [isExpanded, setIsExpanded] = useState(false);
    const limit = 150;

    function toggleExpanded(): void {
        setIsExpanded(prev => !prev);
    }

    // Regular expressions for URLs and emails
    const urlRegex = /(https?:\/\/[^\s]+|www\.[^\s]+)/gi;
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi;

    // Function to handle link press
    const handleLinkPress = async (url: string) => {
        try {
            // Add https:// if it's a www link
            const fullUrl = url.startsWith('www.') ? `https://${url}` : url;
            
            const supported = await Linking.canOpenURL(fullUrl);
            if (supported) {
                await Linking.openURL(fullUrl);
            } else {
                Alert.alert('Error', 'Cannot open this URL');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to open URL');
        }
    };

    // Function to handle email press
    const handleEmailPress = async (email: string) => {
        try {
            const emailUrl = `mailto:${email}`;
            const supported = await Linking.canOpenURL(emailUrl);
            if (supported) {
                await Linking.openURL(emailUrl);
            } else {
                Alert.alert('Error', 'Cannot open email client');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to open email client');
        }
    };

    // Function to parse text and create clickable elements
    const parseText = (inputText: string) => {
        // Combine URL and email regex
        const combinedRegex = /(https?:\/\/[^\s]+|www\.[^\s]+|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi;
        
        const parts = inputText.split(combinedRegex);
        
        return parts.map((part, index) => {
            if (urlRegex.test(part)) {
                return (
                    <Text 
                        key={index} 
                        style={{ color: c.content.info.default, textDecorationLine: 'underline' }}
                        onPress={() => handleLinkPress(part)}
                    >
                        {part}
                    </Text>
                );
            } else if (emailRegex.test(part)) {
                return (
                    <Text 
                        key={index} 
                        style={{ color: c.content.info.default, textDecorationLine: 'underline' }}
                        onPress={() => handleEmailPress(part)}
                    >
                        {part}
                    </Text>
                );
            } else {
                return part;
            }
        });
    };

    const shouldTruncate = text.length > limit;
    const displayedText = isExpanded || !shouldTruncate ? text : text.slice(0, limit) + '...';

    return (
        <Text testID="expandable-text" style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, {color: textColor || c.content.default.default}]}>
            {parseText(displayedText)}
            {shouldTruncate && !isExpanded && (
                    <Text onPress={toggleExpanded} suppressHighlighting style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, {color: c.content.primary.default}]}> more</Text>
            )}
        </Text>
    );
}

export default ExpandableText;