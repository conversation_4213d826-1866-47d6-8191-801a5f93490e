import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { MMKV } from 'react-native-mmkv';
import { ThemeProvider } from '@/theme';
import ExpandableText from '../ExpandableText';

// Mock colorTokens
jest.mock('@/theme/colorTokens', () => ({
  colorTokens: jest.fn().mockReturnValue({
    content: {
      default: {
        default: '#333333'
      },
      primary: {
        default: '#1E88E5'
      }
    }
  })
}));

// Mock useTheme
jest.mock('@/theme', () => ({
  ...jest.requireActual('@/theme'),
  useTheme: jest.fn().mockReturnValue({
    gutters: {},
    fonts: {
      fontSizes: {
        body: {
          xs: { fontSize: 12 }
        },
        utility: {
          sm: { fontSize: 10 }
        }
      },
      lineHeight: {
        body: {
          xs: { lineHeight: 16 }
        },
        utility: {
          sm: { lineHeight: 14 }
        }
      }
    }
  })
}));

describe('ExpandableText Component', () => {
  // Setup storage for ThemeProvider
  let storage: MMKV;
  
  beforeEach(() => {
    storage = new MMKV();
    jest.clearAllMocks();
  });

  it('renders short text without truncation', () => {
    const shortText = 'This is a short text that should not be truncated.';
    
    const { getByText, queryByText } = render(
      <ThemeProvider storage={storage}>
        <ExpandableText text={shortText} />
      </ThemeProvider>
    );

    // Check if the full text is rendered
    expect(getByText(shortText)).toBeTruthy();
    
    // Check that "more" button is not rendered
    expect(queryByText('more')).toBeNull();
  });

  it('truncates long text and shows "more" button', () => {
    const longText = 'This is a very long text that should be truncated. '.repeat(10);
    
    const { getByText, getByTestId } = render(
      <ThemeProvider storage={storage}>
        <ExpandableText text={longText} />
      </ThemeProvider>
    );

    // Check that the text is truncated (ends with "...")

    const textComponent = getByTestId('expandable-text');
    expect(textComponent.props.children[0].endsWith('...')).toBe(true);
    expect(textComponent.props.children[0].length).toBe(153);
    expect(getByText('more')).toBeTruthy();
  });

  it('expands text when "more" button is pressed', () => {
    const longText = 'This is a very long text that should be truncated. '.repeat(10);
    
    const { getByText, queryByText, getByTestId } = render(
      <ThemeProvider storage={storage}>
        <ExpandableText text={longText} />
      </ThemeProvider>
    );

    const textComponent = getByTestId('expandable-text');
    expect(textComponent.props.children[0].endsWith('...')).toBe(true);
    expect(textComponent.props.children[0].length).toBe(153);
    
    // Press the "more" button
    fireEvent.press(getByText('more'));
    
    // Check that the full text is now displayed
    expect(getByText(longText)).toBeTruthy();
    
    // Check that "more" button is no longer rendered
    expect(queryByText('more')).toBeNull();
  });

  it('applies correct text styles', () => {
    const shortText = 'This is a short text.';
    
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <ExpandableText text={shortText} />
      </ThemeProvider>
    );

    const textElement = getByText(shortText);
    
    // Check that the text has the correct font size and line height
    expect(textElement.props.style).toEqual(
      expect.arrayContaining([
        { fontSize: 12 },
        { lineHeight: 16 },
        { color: '#333333' }
      ])
    );
  });

  it('applies correct styles to "more" button', () => {
    const longText = 'This is a very long text that should be truncated. '.repeat(10);
    
    const { getByText } = render(
      <ThemeProvider storage={storage}>
        <ExpandableText text={longText} />
      </ThemeProvider>
    );

    const moreButton = getByText('more');
    
    // Check that the "more" button has the correct font size, line height, and color
    expect(moreButton.props.style).toEqual(
      expect.arrayContaining([
        { fontSize: 10 },
        { lineHeight: 14 },
        { color: '#1E88E5' }
      ])
    );
  });

  it('truncates text at the correct limit', () => {
    const longText = 'A'.repeat(200);
    const limit = 150;
    
    const { getByText, getByTestId } = render(
      <ThemeProvider storage={storage}>
        <ExpandableText text={longText} />
      </ThemeProvider>
    );

    const truncatedText = 'A'.repeat(limit) + '...';

    const textComponent = getByTestId('expandable-text');
    expect(textComponent.props.children[0]).toBe(truncatedText);    
  });
});
