import { TouchableOpacity, View, FlatList, Text } from "react-native"
import { useState } from "react"
import Icon from 'react-native-vector-icons/MaterialIcons'
import { useTheme } from "@/theme"
import { useTranslation } from "react-i18next"

type Props = {
    onOptionSelection: (item: any, name: string) => void
    options?: any
    name: string
}
export const SingleSelectionPopup = (props: Props): JSX.Element => {
    const { t } = useTranslation(['profile']);
    const [list, setList] = useState(props.options)
    const {
		layout,
		gutters,
		fonts,
		backgrounds,
        colors
	} = useTheme();
    const renderItem = (item: any) => {
        return (
            <TouchableOpacity onPress={() => itemPressed(item)}>
                <View style={[layout.row, layout.justifyBetween, layout.itemsCenter, {height: 35}]}>
                    <Text>{item.value}</Text>
                    {item.isSelected &&
                        <Icon name="done" size={25} color={colors.green50} />
                    }
                </View>
            </TouchableOpacity>
        )
    }
    const itemPressed = (item: any) => {
        let newList = list.map((currentItem: any) => {
            if (currentItem.name == item.name){
                currentItem.isSelected = !currentItem.isSelected
                return currentItem
            } else {
                currentItem.isSelected = false
            }
            return currentItem
        })
        setList(newList)
        props.onOptionSelection(item, props.name)
    }
    

    return (
        <View style={[layout.flex_1, gutters.margin_16]}>
            <View style={[layout.justifyCenter, layout.itemsCenter, gutters.marginHorizontal_0]}>
                <Text style={fonts.size_24}>{t("profile:SelectOption")}</Text>
            </View>
            <FlatList 
                data={list}
                keyExtractor={
                    (item, index) => index.toString()
                }
                renderItem={({item}) => renderItem(item) }
                ItemSeparatorComponent={() => (<View style={[backgrounds.gray400, {height: 1}]}/>)}

            />
        </View>
    )
}
