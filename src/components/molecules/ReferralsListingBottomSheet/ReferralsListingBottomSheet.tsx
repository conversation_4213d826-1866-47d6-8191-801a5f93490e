import { ActivityIndicator, FlatList, Text, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import MemberItem from "@/components/atoms/MemberItem/MemberItem";
import { useSafeAreaInsets } from "react-native-safe-area-context";

type Props = {
    isVisible: boolean;
    setIsVisible: (bool: boolean) => void;
    loadNext: () => void;
    flattenData: any;
    isFetchingNextPage: boolean;
}

function ReferralsListingBottomSheet(props: Props) {

    const { isVisible, setIsVisible, loadNext, flattenData, isFetchingNextPage } = props;

    const { bottom } = useSafeAreaInsets();

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
    } = useTheme();

    const c = colorTokens();

    function closeSheet(): void {
        setIsVisible(false);
    }

    function getRightView(userName: string) {
        return (
            <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Regular, {color: c.content.default.subdued}]}>{userName}</Text>
        )
    }

    return (
        <Modal
            propagateSwipe={true}
            isVisible={isVisible}
            animationIn="slideInUp"
            style={[layout.justifyEnd, gutters.margin_0]}
            onBackdropPress={closeSheet}
            avoidKeyboard={true}
            useNativeDriverForBackdrop={true}
        >
            <View style={[backgrounds.white, gutters.paddingTop_8, {borderTopRightRadius: 12, borderTopLeftRadius: 12, overflow: 'hidden', width: '100%', height: 500}]}>
                <View style={[backgrounds.black, gutters.marginTop_12, gutters.marginBottom_12, { borderRadius: 24, height: 5, width: 48, alignSelf: 'center' }]} />
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, gutters.marginBottom_8, { alignSelf: 'center', fontWeight: 600, color: c.content.default.emphasis}]}>Referrals</Text>
                <View style={[layout.flex_1, gutters.paddingHorizontal_16, { borderTopWidth: 1, borderTopColor: c.stoke.default.subdued, paddingBottom: bottom}]}>
                    <FlatList
                        showsVerticalScrollIndicator={false} 
                        contentContainerStyle={{gap: 8, paddingTop: 16}}
                        data={flattenData}
                        keyExtractor={
                            (_, index)=> index.toString()
                        }
                        renderItem={({item}) => (
                            <MemberItem item={item} rightView={getRightView(item.user_name)} callBack={closeSheet} />
                        )}
                        onEndReached={loadNext}
                        removeClippedSubviews={true}
                        ListFooterComponent={
                            <View style={[layout.row,layout.justifyCenter, layout.itemsCenter]}>
                                {isFetchingNextPage && <ActivityIndicator />}
                            </View>
                        }
                    />
                </View>
            </View>
        </Modal>
    )
}

export default ReferralsListingBottomSheet;
