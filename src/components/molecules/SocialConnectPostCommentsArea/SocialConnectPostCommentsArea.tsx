import { useState } from "react";
import { ActivityIndicator, Dimensions, Text, TextInput, TouchableOpacity, View } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import FastImage from "react-native-fast-image";
import ArrowRightIcon from "@/theme/assets/images/SocialConnect/ArrowRightIcon.png";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import SocialConnectPostComment from "../SocialConnectPostComment/SocialConnectPostComment";
import { ElementRef, useRef, useEffect } from "react";
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import NoCommentsPlaceholder from "@/theme/assets/images/SocialConnect/NoCommentsPlaceholder.png";
import { z } from "zod";
import CrossIcon from "@/theme/assets/images/SocialConnect/CrossIcon.png";
import DefaultProfileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png";
import { EMOJIS } from "@/utils/constants";
import EmptyDataView from "../EmptyDataView/EmptyDataView";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { timeSince } from "@/utils";
import { useCommentActions, usePostComments } from "@/hooks/usePostCommentsHooks";
import { getProfile } from "@/services/users";
import { useQuery } from "@tanstack/react-query";
import SelectedCommentOverlay from "../SelectedCommentOverlay/SelectedCommentOverlay";
import CommentInputContainer from "../CommentInputContainer/CommentInputContainer";
import ReportPostBottomSheet from "../ReportPostBottomSheet/ReportPostBottomSheet";
import basicProfile from "@/services/users/basicProfile";

type Props = {
    item: z.infer<typeof socialConnectPostSchema>;
    isPost?: boolean;
    displayPopoverBackdrop?: boolean;
    setIsBlurViewVisible?: (visible: boolean) => void; 
};

function SocialConnectPostCommentsArea(props: Props) {
    const {
        layout,
        gutters,
        backgrounds,
        fonts,
        colors,
        borders,
    } = useTheme();

    const c = colorTokens();

    const inputRef = useRef<ElementRef<typeof TextInput>>();
    const viewRef = useRef(null);
    const [boxYPosition, setBoxYPosition] = useState(0);

    const { data, isLoading } = usePostComments(props.item!.id);
    
    const { data: profileData } = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile
    });

    const {
        onSendPress,
        onEditComment,
        onDeleteComment,
        onPressCross,
        onPressComment,
        onReportComment,
        reportingContent,
        setReportingContent,
        isSending,
        commentText,
        setCommentText,
        replyingComment,
        editingComment,
        selectedComment,
        commentCoordinates,
        setReplyingComment
    } = useCommentActions(
        props.item!.id,
        props.isPost,
        props.setIsBlurViewVisible
    );

    useEffect(() => {
        setTimeout(() => {
            viewRef.current?.measureInWindow((_, y) => {
                setBoxYPosition(y);
            });
        }, 1000);
    }, []);

    function renderItem({item}: any) {
        return (
            <View onStartShouldSetResponder={() => true}>
                <SocialConnectPostComment
                    comment={item}
                    setReplyingComment={setReplyingComment}
                    onDeleteComment={onDeleteComment}
                    onEditComment={onEditComment}
                    onPressComment={onPressComment}
                    onReportComment={onReportComment}
                    displayPopoverBackdrop={props?.displayPopoverBackdrop ?? false}
                />
            </View>
        );
    }

    const handleEditComment = (item) => {
        onEditComment(item);
        inputRef.current?.focus();
    };

    const isTextFieldBarVisible = !!replyingComment || !!editingComment;
    let barText = '';
    if (isTextFieldBarVisible) {
        barText = replyingComment 
            ? `Replying to ${replyingComment?.user.first_name} ${replyingComment?.user.last_name}` 
            : 'Editing...';
    }

    return (
        <View ref={viewRef} style={[layout.flex_1, backgrounds.white]}>
            <SelectedCommentOverlay 
                selectedComment={selectedComment}
                commentCoordinates={commentCoordinates}
                boxYPosition={boxYPosition}
            />

            <View style={[layout.flex_1, gutters.paddingHorizontal_16]}>
                {isLoading && (
                    <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
                        <ActivityIndicator />
                    </View>
                )}
                {data?.comments?.length > 0 && 
                    <KeyboardAwareFlatList 
                        showsVerticalScrollIndicator={false} 
                        contentContainerStyle={{flexGrow: 1 }} 
                        data={data?.comments}
                        keyExtractor={(item) => item.id.toString()}
                        renderItem={renderItem}
                        nestedScrollEnabled={true}
                        extraScrollHeight={-300}
                        extraHeight={0}
                        // style={[{maxHeight: Dimensions.get('window').height*0.6}]}
                    />
                }
                {!isLoading && data?.comments?.length == 0 && (
                    <EmptyDataView
                        heading="No comments"
                        desc="Be the first to start the conversation"
                        image={NoCommentsPlaceholder}
                    />
                )}
            </View>
            <CommentInputContainer
                inputRef={inputRef}
                commentText={commentText}
                setCommentText={setCommentText}
                onSendPress={onSendPress}
                onPressCross={onPressCross}
                isSending={isSending}
                replyingComment={replyingComment}
                editingComment={editingComment}
                profilePhotoUrl={profileData?.user?.profile_photo ?? ''}
            />
            <ReportPostBottomSheet
                isModalVisible={!!reportingContent}
                setReportingContent={setReportingContent}
                reportingContent={reportingContent}
            />
        </View>
    );
}

export default SocialConnectPostCommentsArea;