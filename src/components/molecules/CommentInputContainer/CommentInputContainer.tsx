// CommentInputContainer.tsx
import { View } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import EmojiBar from "../EmojiBar/EmojiBar";
import CommentInputBar from "../CommentInputBar/CommentInputBar";
import { MutableRefObject } from "react";
import { TextInput } from "react-native";
import { Comment } from "@/types/models/Comment";
import layout from "@/theme/layout";

interface CommentInputContainerProps {
  inputRef: MutableRefObject<TextInput | undefined>;
  commentText: string;
  setCommentText: (text: string) => void;
  onSendPress: () => void;
  onPressCross: () => void;
  isSending: boolean;
  replyingComment: Comment | null;
  editingComment: Comment | null;
  profilePhotoUrl?: string;
}

function CommentInputContainer({ 
  inputRef,
  commentText, 
  setCommentText, 
  onSendPress, 
  onPressCross, 
  isSending, 
  replyingComment, 
  editingComment, 
  profilePhotoUrl 
}: CommentInputContainerProps) {
  const { gutters } = useTheme();
  const c = colorTokens();

  const handleEmojiPress = (emoji: string) => {
    setCommentText(prev => prev + emoji);
  };

  return (
    <View style={[gutters.paddingHorizontal_16 ,{ borderTopWidth: 2, borderTopColor: c.stoke.default.subdued }]}>
      <EmojiBar onEmojiPress={handleEmojiPress} />
      <CommentInputBar
        ref={inputRef}
        commentText={commentText}
        setCommentText={setCommentText}
        onSendPress={onSendPress}
        onPressCross={onPressCross}
        isSending={isSending}
        replyingComment={replyingComment}
        editingComment={editingComment}
        profilePhotoUrl={profilePhotoUrl}
      />
    </View>
  );
}

export default CommentInputContainer;