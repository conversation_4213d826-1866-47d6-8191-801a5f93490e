import { Alert, Text, TouchableOpacity, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import CheckBox from "@/components/atoms/CheckBox/CheckBox";
import { useRef, useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import reportPost from "@/services/socialConnect/reportPost";
import Spinner from "react-native-loading-spinner-overlay";
import { useQueryCacheUpdate } from "@/screens/SideTabs/Home/SocialConnect/hooks/useQueryCacheUpdate";
import getReportReasons from "@/services/socialConnect/getReportReasons";
import reportComment from "@/services/home/<USER>";
import reportReply from "@/services/home/<USER>";
import { useNavigation } from "@react-navigation/native";
import { useCustomToast } from "@/hooks/useCustomToast";
import AKBottomSheet from "@/components/atoms/AKBottomSheet/AKBottomSheet";

export type ReportingContent = {
    userID?: number;
    postID: number;
    commentID?: number;
    replyID?: number;
} | null;

type Props = {
    isModalVisible: boolean;
    setReportingContent: (reportingContent: ReportingContent) => void;
    reportingContent: ReportingContent;
    canGoBack?: boolean;
    moduleInfo?: {
        moduleName: string;
        itemId: number;
    }
}

function ReportPostBottomSheet(props: Props) {

    const { isModalVisible, setReportingContent, reportingContent, canGoBack, moduleInfo } = props;

    const { userID, postID, commentID, replyID } = reportingContent || {};

    const navigation = useNavigation();
    const toastRef = useRef();

    const showToast = useCustomToast();

    const [selectedReasons, setSelectedReasons] = useState<Record<string, boolean>>({});
    const [isReporting, setIsReporting] = useState(false);

    const { updatePostsCache, updateCommentsCache, updateRepliesCache, updateCommentsCacheForOtherModules } = useQueryCacheUpdate(postID);

    const { data: reportReasons } = useQuery({
        queryKey: ['report-reasons'],
        queryFn: getReportReasons,
        staleTime: Infinity,
        refetchOnMount: false,
        refetchOnReconnect: false,
    });

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
        borders,
    } = useTheme();

    const c = colorTokens();

    const reportMutation = useMutation(
        {
            mutationFn: (data: any) => {
                if(replyID) {
                    return reportReply(replyID, data);
                }
                if(commentID) {
                    return reportComment(commentID, data);
                }
                return reportPost(postID, data);
            },
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );

    function onSuccessMutation(): void {
        let toastText = ' Reported Successfully';
        setSelectedReasons({});
        setIsReporting(false);
        closeSheet();
        if (moduleInfo) {
            updateCommentsCacheForOtherModules(moduleInfo.moduleName, moduleInfo.itemId, reportingContent);
        } else if (replyID) {
            toastText = 'Reply' + toastText;
            updateRepliesCache(commentID, replyID);
        } else if (commentID) {
            toastText = 'Comment' + toastText;
            updateCommentsCache(commentID);
        } else {
            toastText = 'Post' + toastText;
            updatePostsCache(userID);
            if (canGoBack) {
                navigation.goBack();
            }
        }
        showToast(toastText);
    }

    function onErrorMutation(error: any): void {
        setIsReporting(false);
        Alert.alert("Error!", error.message);
    }

    function closeSheet(): void {
        setReportingContent(null);
        setSelectedReasons({});
    }

    function onPressCancel(): void {
        setSelectedReasons({});
        closeSheet();
    }

    function onPressReport(): void {
        let reasons = Object.keys(selectedReasons).map(Number).join(',');
        const data = {
            report_reason_ids: reasons
        };
        setIsReporting(true);
        reportMutation.mutate(data);
    }

    function onPressOption(option: number): void {
        let updatedReasons = selectedReasons;
        if (!!updatedReasons[option]) {
            delete updatedReasons[option];
        } else {
            updatedReasons[option] = true;
        }
        setSelectedReasons({
            ...updatedReasons
        });
    }

    const type = commentID ? 'Comment' : 'Post';

    return (
        <AKBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setReportingContent}>
            <Spinner
                visible={isReporting}
            />
            <View style={[backgrounds.black, gutters.marginTop_12, gutters.marginBottom_12, { borderRadius: 24, height: 5, width: 48, alignSelf: 'center' }]} />
            <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, gutters.marginBottom_8, { alignSelf: 'center', color: c.content.default.emphasis}]}>Report</Text>
            <View style={[gutters.paddingHorizontal_16, { borderTopWidth: 1, borderTopColor: c.stoke.default.subdued }]}>
                <Text style={[fonts.fontSizes.headings.H5, fonts.lineHeight.headings.H5, fonts.Bold, gutters.marginTop_12, gutters.marginBottom_8, {color: c.content.default.emphasis}]}>Report a {type}</Text>
                <View style={{ gap: 20 }}>
                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, { color: c.content.default.default }]}>
                        You’re about to report this {type.toLowerCase()}. It will also be sent to the admin for review. If the admin finds it inappropriate, the {type.toLowerCase()} may be removed for all users.
                    </Text>
                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, gutters.marginBottom_16, { color: c.content.default.default }]}>
                        Select reason(s) for reporting this {type.toLowerCase()}.
                    </Text>
                </View>
                <View style={[gutters.marginBottom_16, { gap: 12 }]}>
                    {
                        reportReasons?.report_reasons.map(reason => (
                            <CheckBox id={reason.id} key={reason.id} text={reason.title} onPressOption={onPressOption} isChecked={!!selectedReasons[reason.id]} />
                        ))
                    }
                </View>
            </View>
            <View style={[layout.row, gutters.paddingTop_12, gutters.paddingHorizontal_16, { gap: 8, borderTopWidth: 1, borderTopColor: c.stoke.default.subdued }]}>
                <TouchableOpacity onPress={onPressCancel} style={[layout.flex_1, borders.rounded_8, layout.itemsCenter, layout.justifyCenter, { height: 48 }]}>
                    <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, { color: c.content.default.default }]}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={onPressReport} disabled={Object.keys(selectedReasons).length == 0} style={[layout.flex_1, borders.rounded_8, layout.itemsCenter, layout.justifyCenter, { height: 48, backgroundColor: 'black' }]}>
                    <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, { color: c.content.onBold.default.default }]}>Report</Text>
                </TouchableOpacity>
            </View>
        </AKBottomSheet>
    )
}

export default ReportPostBottomSheet;
