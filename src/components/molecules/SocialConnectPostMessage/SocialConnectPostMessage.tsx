import { useTheme } from "@/theme"
import { Alert, ImageSourcePropType, Linking, Text, TouchableOpacity, View } from "react-native"
import { z } from "zod"
import { useTranslation } from "react-i18next";
import { Dispatch, SetStateAction, useState, useRef, useEffect } from "react";
import FastImage from "react-native-fast-image"
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import CommentIcon from "@/theme/assets/images/SocialConnect/CommentIcon.png";
import ReportIcon from "@/theme/assets/images/ReportIcon.png";
import ExcmalimationCircleIcon from "@/theme/assets/images/ExcmalimationCircleIcon.png";
import EyeCutIcon from "@/theme/assets/images/EyeCutIcon.png";
import DefaultProfileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png";
import EditIcon from "@/theme/assets/images/SocialConnect/EditIcon.png";
import BinIcon from "@/theme/assets/images/SocialConnect/BinIcon.png";
import { timeSince } from "@/utils";
import Popover from 'react-native-popover-view';
import { useQuery } from "@tanstack/react-query";
import { getProfile } from "@/services/users";
import { Comment } from "@/types/models/comment";
import { commentSchema, replySchema } from "@/types/schemas/postDetail";

import Animated, { 
    useSharedValue, 
    useAnimatedStyle, 
    withTiming, 
    withSequence, 
    runOnJS
  } from 'react-native-reanimated';
import basicProfile from "@/services/users/basicProfile";
import { AKFastImage } from "@/components/atoms";

type Props = {
    commentId: number;
    message: z.infer<typeof commentSchema> | z.infer<typeof replySchema>;
    setReplyingComment: Dispatch<SetStateAction<Comment | null>>;
    onDeleteComment: (item: Comment) => void;
    onEditComment: (item: Comment) => void;
    isReply: boolean;
    onPressComment: (data?: any) => any;
    displayPopoverBackdrop: boolean;
    key: number
}

type PopoverMenuOption = {
    text: string;
    icon: ImageSourcePropType;
    onPress: () => void;
    textColor?: string;
};

function SocialConnectPostMessage({ commentId, message, setReplyingComment, isReply, onDeleteComment, onEditComment, onReportComment, onPressComment, displayPopoverBackdrop, key }: Props) {
    const { t } = useTranslation(['home']);

    const {
        layout,
        fonts,
        colors,
        gutters,
        borders,
    } = useTheme();

    const c = colorTokens();

    const { data } = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile
    });

    const { comment: commentText, reply, user, updated_at: updatedAt } = message;
    const userName = `${user.first_name} ${user.last_name}`;

    const touchable = useRef();
    const [showPopover, setShowPopover] = useState(false);
    const [isReported, setIsReported] = useState(message.reported_comment_exists || message.reported_reply_exists);

    const viewRef = useRef<View>(null);

    const avatarSize = isReply ? 24 : 32;
    let viewStyle = {};
    let displayText = commentText;
// Regular expressions for URLs and emails
    const urlRegex = /(https?:\/\/[^\s]+|www\.[^\s]+)/gi;
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi;

    useEffect(() => {
        setIsReported(message.reported_comment_exists || message.reported_reply_exists);
    }, [message.reported_comment_exists || message.reported_reply_exists]);

    function onPressReply(): void {
        setReplyingComment(message);
    }

    function onLongPress(): void {
        viewRef.current?.measureInWindow((_, y) => {
            if (!!popoverMenuOptions.length) {
                onPressComment({
                    comment: {
                        profilePhoto: user?.profile_photo,
                        userName,
                        updatedAt,
                        displayText,
                        avatarSize,
                    },
                    yPos: y,
                    xPos: isReply ? 44 : 0,
                });
                setShowPopover(true);
            }
        });
    }

    function onClosePopover() {
        setShowPopover(false);
        onPressComment({
            comment: null,
            yPos: 0,
            xPos: 0,
        });
    }

    const popoverMenuOptions: PopoverMenuOption[] = [
        {
            text: 'Reply',
            icon: CommentIcon,
            onPress: () => {
                onClosePopover();
                onPressReply();
            }
        },
        {
            text: 'Edit',
            icon: EditIcon,
            onPress: () => {
                onClosePopover();
                onEditComment({
                    id: commentId,
                    replyId: isReply ? message.id : null,
                    comment: isReply ? reply : commentText,
                });
            }
        },
        {
            text: 'Delete',
            textColor: c.content.error.default,
            icon: BinIcon,
            onPress: () => {
                onClosePopover();
                onDeleteComment({
                    id: commentId,
                    replyId: isReply ? message.id : null
                });
            }
        },
    ]

    if (isReply) {
        viewStyle = { marginLeft: 44, marginTop: 10 };
        displayText = reply;
        popoverMenuOptions.shift();
    }

    if (data?.user?.id != message?.user?.id) {
        popoverMenuOptions.pop();
        popoverMenuOptions.pop();
        popoverMenuOptions.push({
            text: 'Report Comment',
            textColor: c.content.error.default,
            icon: ReportIcon,
            onPress: () => {
                onReportComment(commentId, isReply ? message.id : null);
                onClosePopover();
            }
        });
    }

    const scale = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ scale: scale.value }],
    }));

    const handleLongPress = () => {
        scale.value = withSequence(
            withTiming(0.70, { duration: 300 }, (finished) => {
                if (finished) {
                    runOnJS(onLongPress)();
                }
            }),
            withTiming(1, { duration: 150 })
        );
    };

    function onPressEyeCutIcon() {
        setIsReported(prev => !prev);
    }

    const handleLinkPress = async (url: string) => {
        try {
            // Add https:// if it's a www link
            const fullUrl = url.startsWith('www.') ? `https://${url}` : url;
            
            const supported = await Linking.canOpenURL(fullUrl);
            if (supported) {
                await Linking.openURL(fullUrl);
            } else {
                Alert.alert('Error', 'Cannot open this URL');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to open URL');
        }
    };
    
        // Function to handle email press
    const handleEmailPress = async (email: string) => {
        try {
            const emailUrl = `mailto:${email}`;
            const supported = await Linking.canOpenURL(emailUrl);
            if (supported) {
                await Linking.openURL(emailUrl);
            } else {
                Alert.alert('Error', 'Cannot open email client');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to open email client');
        }
    };
    
        // Function to parse text and create clickable elements
    const parseText = (inputText: string) => {
        // Combine URL and email regex
        const combinedRegex = /(https?:\/\/[^\s]+|www\.[^\s]+|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi;
        
        const parts = inputText.split(combinedRegex);
        
        return parts.map((part, index) => {
            if (urlRegex.test(part)) {
                return (
                    <Text 
                        key={index} 
                        style={{ color: c.content.info.default, textDecorationLine: 'underline' }}
                        onPress={() => handleLinkPress(part)}
                    >
                        {part}
                    </Text>
                );
            } else if (emailRegex.test(part)) {
                return (
                    <Text 
                        key={index} 
                        style={{ color: c.content.info.default, textDecorationLine: 'underline' }}
                        onPress={() => handleEmailPress(part)}
                    >
                        {part}
                    </Text>
                );
            } else {
                return part;
            }
        });
    };

    return (
        <View key={key} ref={viewRef} style={[{...viewStyle}]} onLayout={() => {}}>
            <View style={[layout.row]}>
                <AKFastImage
                    uri={user?.profile_photo}
                    resizeMode={FastImage.resizeMode.contain}
                    placeholder={DefaultProfileIcon}
                    style={[{width: avatarSize, height: avatarSize, borderRadius: 50,  backgroundColor: colors.black}]}
                />
                {
                    isReported ? (
                        <View style={[layout.row, layout.flex_1, layout.itemsCenter]}>
                            <View style={[layout.row, layout.flex_1, gutters.marginLeft_8, gutters.paddingHorizontal_12, gutters.paddingVertical_8, borders.rounded_12, { gap: 2, backgroundColor: c.background.default.neutrals.secondary }]}>
                                <AKIcon source={ExcmalimationCircleIcon} size={16} styles={[gutters.marginRight_4]} />
                                <View style={[layout.flex_1]}>
                                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, {color: c.content.default.default}]}>
                                        This comment has been reported and is under review
                                    </Text>
                                </View>
                            </View>
                            <AKIcon source={EyeCutIcon} size={20} styles={[gutters.marginLeft_16]} onPress={onPressEyeCutIcon} />
                        </View>
                    ) : (
                        <Animated.View style={[animatedStyle, { maxWidth: 300 }]}>
                            <TouchableOpacity
                                delayLongPress={200}
                                ref={touchable}
                                style={[gutters.marginLeft_8, gutters.paddingHorizontal_12, gutters.paddingVertical_8, borders.rounded_12, { gap: 2, backgroundColor: c.background.default.neutrals.secondary }]}
                                onLongPress={handleLongPress}
                            >
                                <View style={[layout.row, { gap: 4 }]}>
                                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.SemiBold, {color: c.content.default.emphasis}]}>{userName}</Text>
                                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, {color: c.content.default.subdued}]}>{timeSince(new Date(updatedAt))}</Text>
                                </View>
                                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, {color: c.content.default.default}]}>{parseText(displayText)}</Text>
                            </TouchableOpacity>
                        </Animated.View>
                    )
                }
                <Popover
                    from={touchable}
                    isVisible={showPopover}
                    onRequestClose={onClosePopover}
                    backgroundStyle={{ backgroundColor: 'transparent' }}
                    // {...(!displayPopoverBackdrop && { backgroundStyle: { backgroundColor: 'transparent' } })}
                    arrowSize={{
                        width: 0,
                        height: 0
                    }}
                    offset={10}
                    popoverStyle={{borderRadius: 12, width: 190, marginLeft: 50 }}
                >
                    <View style={[gutters.paddingHorizontal_12, gutters.paddingVertical_4, { gap: 2, borderRadius: 12, borderWidth: 1, borderColor: c.stoke.default.default }]}>
                        {
                            popoverMenuOptions.map((option, index) => (
                                <TouchableOpacity key={index} onPress={option.onPress} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_8]}>
                                    <AKIcon source={option.icon} styles={[gutters.marginRight_12]} size={20} onPress={() => console.log(123)} />
                                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: option.textColor }]}>{option.text}</Text>
                                </TouchableOpacity>
                            ))
                        }
                    </View>
                </Popover>
            </View>
            {
                !isReply && !isReported && (
                    <TouchableOpacity style={[gutters.paddingTop_6, { marginHorizontal: 44 }]} onPress={onPressReply}>
                        <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, {color: c.content.default.default}]}>Reply</Text>
                    </TouchableOpacity>
                )
            }
        </View>
    )
}

export default SocialConnectPostMessage;
