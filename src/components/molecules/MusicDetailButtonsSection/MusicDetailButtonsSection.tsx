import { ImageVariant } from "@/components/atoms"
import { TouchableOpacity, View } from "react-native"
import RoundButtonBG from "@/theme/assets/images/Home/RoundButtonBG.png"
import HeartFilled from "@/theme/assets/images/Home/HeartFilled.png"
import MessageIcon from "@/theme/assets/images/Home/MessageIcon.png"
import SaveIcon from "@/theme/assets/images/Home/SaveIcon.png"
import { useTheme } from "@/theme"
type Props = { 
    isLikeSelected?: boolean
    isSaveSelected?: boolean
    onLikeButton?: () => void
    onCommentButton?: () => void
    onSaveButton?: () => void
}
function MusicDetailButtonsSection(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    return (
        <View style={[layout.justifyCenter, layout.itemsCenter, gutters.marginHorizontal_0]}>
            <View style={[layout.row,]}>
                <TouchableOpacity onPress={props.onLikeButton}>
                    <View style={[layout.justifyCenter, layout.itemsCenter,{height: 60, width: 60,}]}>
                        <ImageVariant 
                            source={RoundButtonBG}
                            style={[ { resizeMode: 'cover', position: 'absolute'}]}
                        />
                        <ImageVariant
                            source={HeartFilled}
                            style={ [ {tintColor: props.isLikeSelected ? colors.green50 :  colors.black, position: 'absolute',height: 15, width: 15,}]}
                        />
                    </View>
                </TouchableOpacity>
                
                {/* <TouchableOpacity>
                    <View style={[layout.justifyCenter, layout.itemsCenter,{height: 60, width: 60,}]}>
                        <ImageVariant 
                            source={RoundButtonBG}
                            style={[ { resizeMode: 'cover', position: 'absolute'}]}
                        />
                        <ImageVariant
                            source={MessageIcon}
                            style={ [ {tintColor:  colors.black, position: 'absolute',height: 15, width: 15,}]}
                        />
                    </View>
                </TouchableOpacity> */}
                
                <TouchableOpacity onPress={props.onSaveButton}>
                    <View style={[layout.justifyCenter, layout.itemsCenter,{height: 60, width: 60,}]}>
                        <ImageVariant 
                            source={RoundButtonBG}
                            style={[ { resizeMode: 'stretch', position: 'absolute'}]}
                        />
                        <ImageVariant
                            source={SaveIcon}
                            style={ [ {tintColor: props.isSaveSelected ? colors.green50 : colors.black, position: 'absolute',height: 15, width: 15,}]}
                        />
                    </View>
                </TouchableOpacity>
                
            </View>
            
        </View>
    )
}

export default MusicDetailButtonsSection