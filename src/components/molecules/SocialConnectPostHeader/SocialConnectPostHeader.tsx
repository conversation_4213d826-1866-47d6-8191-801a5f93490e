import { useTheme } from "@/theme"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { Text, TouchableOpacity, View } from "react-native"
import { z } from "zod"
import FastImage from "react-native-fast-image"
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import MoreIcon from "@/theme/assets/images/SocialConnect/MoreIcon.png";
import { timeSince } from "@/utils";
import { useNavigation } from "@react-navigation/native";
import DefaultprofileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png"
import { AKFastImage } from "@/components/atoms"

type Props = {
    item: z.infer<typeof socialConnectPostSchema>;
    onPressPostSettings: (item: z.infer<typeof socialConnectPostSchema>) => void;
}

function SocialConnectPostHeader(props: Props) {
    const {
        layout,
        fonts,
        colors,
        gutters
    } = useTheme();

    const c = colorTokens();

    const navigation = useNavigation();

    const { updated_at: updatedAt, user } = props.item;
    const { first_name: firstName, last_name: lastName, profile_photo: profilePhoto } = user || {};

    const fullName = `${firstName} ${lastName}`;

    function onPressSettings() {
        props?.onPressPostSettings(props.item);
    }

    function onPressProfilePicture() {
        navigation.navigate('UserProfile', { userId: user?.id });
        // navigation.navigate('Profile', { userId: user?.id });
    }

    return (
        <View style={[layout.row, gutters.paddingHorizontal_16, gutters.marginTop_16, gutters.marginBottom_12, layout.itemsCenter]}>
            <TouchableOpacity onPress={onPressProfilePicture}>
                <AKFastImage
                    uri={profilePhoto}
                    placeholder={DefaultprofileIcon}
                    resizeMode={FastImage.resizeMode.contain}
                    style={[{width: 32, height: 32, borderRadius: 50,  backgroundColor: colors.black}]}
                />
            </TouchableOpacity>
            <View style={[gutters.marginLeft_8, { gap: 2 }]}>
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.bold, {color: c.content.default.default}]}>{fullName}</Text>
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, {color: c.content.default.subdued}]}>{timeSince(new Date(updatedAt))}</Text>
            </View>
            <View style={[layout.flex_1, layout.itemsEnd]}>
                <AKIcon source={MoreIcon} size={20} onPress={onPressSettings} />
            </View>
        </View>
    )

}

export default SocialConnectPostHeader;
