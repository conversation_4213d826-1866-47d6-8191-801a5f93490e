import { Text, View } from "react-native";
import { moduleSchema } from "@/types/schemas/module";
import { useTheme } from "@/theme";
import FastImage from "react-native-fast-image";
import { colorTokens } from "@/theme/colorTokens";
type Props = {
    item?: typeof moduleSchema['_output']
}
function AkinaHubCell(props: Props) {
    const {
        fonts,
		layout,
		gutters,
        borders,
	} = useTheme();
    
    const tokenColors = colorTokens()
    return (
        <View style={[gutters.marginLeft_16, {height: 88, minWidth: 124, borderRadius: 12 , overflow: 'hidden'}]}>
            <FastImage style={[layout.absolute, borders.rounded_4 ,{height: 88, width: '100%', backgroundColor: 'transparent',} ]} source={{uri: props.item?.mob_image ?? ''}}/>
            <View style={[layout.flex_1, layout.justifyEnd, gutters.padding_8]}>
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs ,fonts.SemiBold, {color: tokenColors.content.onBold.default.emphasis}]}>{props.item?.title}</Text>
            </View>
        </View>
    )
}

export default AkinaHubCell