import { View, Text, TouchableOpacity } from "react-native";
import { useTheme } from "@/theme";
import { EMOJIS } from "@/utils/constants";

interface EmojiBarProps {
  onEmojiPress: (emoji: string) => void;
}

function EmojiBar({ onEmojiPress }: EmojiBarProps) {
  const { layout, gutters } = useTheme();

  return (
    <View style={[layout.row, layout.justifyBetween, gutters.marginVertical_16, { zIndex: 10, position: 'relative' }]}>
      {EMOJIS.map((emoji, index) => (
        <TouchableOpacity key={index} onPress={() => onEmojiPress(emoji)}>
          <Text>{emoji}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

export default EmojiBar;