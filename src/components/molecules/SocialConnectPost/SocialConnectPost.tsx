import { useTheme } from "@/theme"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { TouchableOpacity, useWindowDimensions, View } from "react-native"
import FastImage from "react-native-fast-image"
import { z } from "zod"
import SocialConnectPostHeader from "../SocialConnectPostHeader/SocialConnectPostHeader"
import SocialConnectPostFooter from "../SocialConnectFooter/SocialConnectFooter"
import { colorTokens } from "@/theme/colorTokens"
import Video, { VideoRef } from "react-native-video"
import ExpandableText from "@/components/atoms/ExpandableText/ExpandableText"
import { useIsFocused, useNavigation } from "@react-navigation/native"
import { forwardRef, useEffect, useImperativeHandle, useRef } from "react"

type Props = {
    item: z.infer<typeof socialConnectPostSchema>;
    isVisible: boolean
    onCommentButtonTapped: (item: z.infer<typeof socialConnectPostSchema>) => void;
    onLikeButton: (item: z.infer<typeof socialConnectPostSchema>) => void;
    onBookmarkButton: (item: z.infer<typeof socialConnectPostSchema>) => void;
    onPressPostSettings: (item: z.infer<typeof socialConnectPostSchema>) => void;
}

function SocialConnectPost(props: Props) {
    const {
		layout,
		gutters,
        fonts,
        backgrounds,
	} = useTheme();
    const isFocused = useIsFocused();

    useEffect(() => {
        if (isFocused == false) {
            pauseVideo()
        } else {
            playVideo()
        }
    }, [isFocused])
    function pauseVideo() {
        videoRef.current?.pause()
    }
    function playVideo() {
        videoRef.current?.resume()
    }
    const videoRef = useRef<VideoRef>(null)

    const c = colorTokens();
    const navigation = useNavigation();
    const { width } = useWindowDimensions();
    function onPressVideo() {
        navigation.navigate('VideoPreview', { item: props.item });
    }

    return(
        <View style={[backgrounds.white]} >
            <SocialConnectPostHeader item={props.item} onPressPostSettings={props?.onPressPostSettings}  />
            {
                props.item.text && (
                    <View style={[gutters.paddingHorizontal_16, gutters.marginBottom_12]}>
                        <ExpandableText text={props.item.text} />
                    </View>
                )
            }
            {
                props.item.type != 'text' && (
                    <View style={[{maxHeight: width + 52}]}>
                        {
                            props.item.type == 'post' ? (
                                <FastImage
                                    source={{uri: props.item.media_link ?? ''} }
                                    resizeMode={FastImage.resizeMode.contain}
                                    style={[{aspectRatio: 1,width: '100%' , backgroundColor: 'black'}]}
                                />
                            ) : (
                                <>
                                    <Video
                                        ref={videoRef}
                                        source={props.isVisible ? {uri: props.item.media_link ?? ''} : {} }
                                        controls={false}
                                        paused={!props.isVisible}
                                        hideShutterView={false}
                                        
                                        resizeMode="contain"
                                        allowsExternalPlayback={false}
                                        style={[ gutters.marginHorizontal_0, {height: '100%', width: '100%', overflow: 'hidden', backgroundColor: c.background.bold.neutral.default}]}
                                        repeat={true}
                                        volume={0}
                                        poster={props.item.video_thumbnail ?? ''}
                                    />
                                    <TouchableOpacity onPress={onPressVideo} style={[layout.absolute, { left: 0, right: 0, top: 0, bottom: 0 }]} />
                                </>
                            )
                        }
                        
                    </View>
                )
            }
            <SocialConnectPostFooter
                item={props.item}
                onCommentButtonTapped={props.onCommentButtonTapped}
                onLikeButton={props.onLikeButton}
                onBookmarkButton={props.onBookmarkButton}
            />
        </View>
    )
}

export default SocialConnectPost;
