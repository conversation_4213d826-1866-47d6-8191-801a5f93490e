import { AKFastImage } from "@/components/atoms";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { Comment } from "@/types/models/comment";
import { ElementRef, MutableRefObject, forwardRef, useImperativeHandle, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Text, TextInput, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import { Colors } from "react-native/Libraries/NewAppScreen";
type Props = { 
    image?: string
    onSend: (text: string, isEdit: boolean, comment?: Comment) => void
    inputFocus?: boolean
    commentRef?: any
    // commentText?: string

}
const AddCommentSection = forwardRef( function AddCommentSection(props: Props, ref: any) {

    const { t } = useTranslation(['home']);
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();

    const c = colorTokens();
    const [text, setText] = useState('')
    const [isCommentEdit, setIsCommentEdit] = useState(false)
    const [currentComment, setCurrentComment] = useState<Comment | null>(null)
    const inputRef = useRef<ElementRef<typeof TextInput>>()
    function onChange(text: string) {
        setText(text)
    }
    useImperativeHandle(ref, () => ({
        // each key is connected to `ref` as a method name
        // they can execute code directly, or call a local method
        editComment: (comment: Comment) => {editComment(comment)},
        makeCommentFirstResponsder: () => { makeCommentFirstResponsder() },
    }))
    function editComment(comment?: Comment) {
        if (comment != null) {
            setIsCommentEdit(true)
            setCurrentComment(comment)
            setText(comment.comment)
        } else {
            setIsCommentEdit(false)
            setCurrentComment(null)
            setText('')
        }
        
    }
    function makeCommentFirstResponsder(){
        inputRef?.current?.focus()
    }
    function onSendButtonTapped() {
        props.onSend(text, isCommentEdit, currentComment)
        setText('')
    }
    return(
        <View>
            <View style={[{borderWidth: 1}, backgrounds.gray50 , gutters.marginTop_16, gutters.marginHorizontal_32 ,borders.gray100, borders.w_1,]}>
                <TextInput placeholderTextColor={colors.gray100} ref={inputRef} autoFocus={props.inputFocus} multiline editable value={text} onChangeText={onChange} style={[gutters.padding_12, {height: 100}]} placeholder={t('home:AddAComment')}/>
            </View>
            <View style={[layout.row, gutters.marginTop_16 ,layout.justifyBetween, layout.itemsCenter ,gutters.marginHorizontal_32 ,{height: 50}]}>
                <AKFastImage
                    uri={props.image}
                    style={[ ,{width: 32, height: 32, borderRadius: 16}]}
                />
                <TouchableOpacity disabled={text == ''} onPress={() => onSendButtonTapped()}>
                    <View style={[layout.itemsCenter,layout.justifyCenter,{width: 100, height: 50, borderRadius: 10, backgroundColor: text == '' ? colors.gray200 : c.fill.bold.neutrals.rest}]}>
                        <Text style={[fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: c.content.onBold.default.default}]}>{isCommentEdit ? t('home:Update') : t('home:Send')}</Text>
                    </View>
                </TouchableOpacity>
                
            </View>

        </View>
    )
})
export default AddCommentSection
