import { useTheme } from "@/theme"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { StyleSheet, Text, TouchableOpacity, View } from "react-native"
import { z } from "zod"
import _Icon from 'react-native-vector-icons/FontAwesome';
import { useTranslation } from "react-i18next";
import LinearGradient from "react-native-linear-gradient";
import { useState } from "react";

type Props = {
    item: z.infer<typeof socialConnectPostSchema>
    onLikeButtonTapped?: (item:  z.infer<typeof socialConnectPostSchema>) => void
    onShareButtonTapped?: (item:  z.infer<typeof socialConnectPostSchema>) => void
    onCommentButtonTapped?: (item:  z.infer<typeof socialConnectPostSchema>) => void
    isLoading: boolean
}
function SocialConnectPostTopItem(props: Props) {
    const { t } = useTranslation(['home']);

    const [isLikeButtonPressed, setIsLikeButtonPressed] = useState(false);

    const Icon = _Icon as React.ElementType
    const {
		layout,
        fonts,
        colors,
	} = useTheme();

    function onLikeButtonTapped(): void {
        if (isLikeButtonPressed) {
            return;
        }
        if (props.onLikeButtonTapped != null) {
            props.onLikeButtonTapped(props.item)
            setIsLikeButtonPressed(true);
            setTimeout(() => setIsLikeButtonPressed(false), 1000);
        }
    }
    function onShareButtonTapped(): void {
        if (props.onShareButtonTapped != null) {
            props.onShareButtonTapped(props.item)
        }
    }
    function onCommentButtonTapped(): void {
        if (props.onCommentButtonTapped != null) {
            props.onCommentButtonTapped(props.item)
        }
    }

    const isDisabled = isLikeButtonPressed || props.isLoading;

    return (
        <View style={[layout.absolute ,{top: 0, left:0, right: 0}]}>
            <LinearGradient style={styles.cardsongDetails} locations={[0,1,1,0]} colors={['rgba(0,0,0,0.16)','rgba(0, 0, 0, 0.8)','transparent','transparent']} useAngle={true} angle={0}>
                <View style={[layout.row, layout.justifyBetween, layout.itemsEnd ,{height: 80}]}>
                    <TouchableOpacity onPress={onLikeButtonTapped} disabled={isDisabled}>
                        <View style={[layout.justifyCenter, layout.itemsCenter ,{height: 50, width: 100}]}>
                            <Text style={[fonts.gray100, fonts.size_12, fonts.Medium]}>{t('home:Likes')}</Text>
                            <Icon style={[]} name='heart' size={20} color={props.item?.is_liked ? colors.green50 : colors.gray100}/>
                            <Text style={[fonts.gray100, fonts.size_12, fonts.Medium]}>{props.item.likes_count ?? 0}</Text>
                        </View>
                    </TouchableOpacity>
                    
                    <TouchableOpacity onPress={onShareButtonTapped}>
                        <View style={[layout.justifyCenter, layout.itemsCenter,{height: 50, width: 100}]}>
                            <Text style={[fonts.gray100, fonts.size_12, fonts.Medium]}>{t('home:Share')}</Text>
                            <Icon style={[]} name='share' size={20} color={colors.gray100}/>
                        </View>
                    </TouchableOpacity >
                    <TouchableOpacity onPress={onCommentButtonTapped}>
                        <View style={[layout.justifyCenter, layout.itemsCenter,{height: 50, width: 100}]}>
                            <Text style={[fonts.gray100, fonts.size_12, fonts.Medium]}>{t('home:Comments')}</Text>
                            <Icon style={[]} name='comment' size={20} color={colors.gray100}/>
                            <Text style={[fonts.gray100, fonts.size_12, fonts.Medium]}>{props.item.comments_count ?? 0}</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </LinearGradient>
            
        </View>
    )

}
const styles = StyleSheet.create({
    cardsongDetails: {
        shadowColor: "transparent",
        shadowOffset: {
        width: 0,
        height: 0
        },
        width: "100%",
        alignItems: "center",
        padding: 16,
        backgroundColor: "transparent"
        }

})
export default SocialConnectPostTopItem