import { AKFastImage } from "@/components/atoms";
import { useTheme } from "@/theme";
import { podcastSchema } from "@/types/schemas/podcastListing";
import { videoSchema } from "@/types/schemas/videoListing";
import { Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { z } from "zod";
type Props = {
    item: z.infer<typeof videoSchema>
    onPress?: (item:  z.infer<typeof videoSchema>) => void
}
function RectVideoCell(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    function onItemPressed() {
        if (props.onPress != null) {
            props.onPress(props.item)
        }
    }
    return (
        <TouchableOpacity style={[layout.flex_1]} onPress={onItemPressed}>
            <View style={[layout.flex_1]}>
                <AKFastImage
                    uri={props.item.thumbnail}
                    style={[layout.flex_1]}
                />
                <View style={[layout.row, gutters.marginBottom_0, layout.flex_1 ,layout.absolute, , {bottom: 10} ]}>
                    <Text style={[layout.flex_1, gutters.marginHorizontal_12, fonts.gray50, fonts.size_24, fonts.Medium]}>{props.item.title}</Text>
                </View>
            </View>
        </TouchableOpacity>
    )
}
export default RectVideoCell