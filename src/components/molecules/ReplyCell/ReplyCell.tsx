import { MessageRow } from "@/components/atoms";
import { useTheme } from "@/theme";
import { Comment } from "@/types/models/comment";
import { Reply } from "@/types/models/reply";
import { View } from "react-native";

export type ReplyItem = {
    reply: Reply
    commentId: number
    userId?: number
    onDeleteComment?: (comment: Comment) => void
    onEditComment: (comment: Comment) => void
    onReportComment?: (postId: number, commentID: number, replyID?: number, userID?: number) => void
}

function ReplyCell(props: ReplyItem) {
    const {
		gutters,
	} = useTheme();

    return (
        <View style={[gutters.marginLeft_32, gutters.marginTop_12]}>
            <MessageRow
                comment={{
                    ...props.reply,
                    id: props.commentId,
                    comment: props.reply.reply,
                    replyId: props.reply.id,
                }}
                userId={props.userId}
                onDeleteComment={props.onDeleteComment}
                onEditComment={props.onEditComment}
                onReportComment={props.onReportComment}
            />
        </View>
    )
}
export default ReplyCell