import { useTheme } from "@/theme";
import { musicSchema } from "@/types/schemas/musicListing";
import { podcastSchema } from "@/types/schemas/podcastListing";
import { TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { z } from "zod";
import PodcastThumb from '@/theme/assets/images/Home/PodcastThumb.png'
import { AKFastImage } from "@/components/atoms";

type Props = {
    item: z.infer<typeof podcastSchema>
    onPress?: (item:  z.infer<typeof podcastSchema>) => void

}
function SquarePodcastCell(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    function onItemPressed() {
        if (props.onPress != null) {
            props.onPress(props.item)
        }
    }
    return (
        <TouchableOpacity style={[layout.flex_1]} onPress={onItemPressed}>
            <View style={[layout.flex_1]}>
                <AKFastImage
                    uri={props.item.thumbnail}
                    style={[layout.flex_1]}
                    placeholder={require('@/theme/assets/images/Home/PodcastThumb.png')}
                />
            </View>
        </TouchableOpacity>
    )
}
export default SquarePodcastCell