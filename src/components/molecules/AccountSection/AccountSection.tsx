import { useTheme } from "@/theme";
import { View } from "react-native";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { AccountItem } from "@/components/atoms";
import { colorTokens } from "@/theme/colorTokens";

export type ProfileItem = {
    label: string,
    icon?: any,
    rightLabel?: string,
    section?: number,
    index?: number,
    onPressAction?: Function,
}
type Props = {    
    items: Array<ProfileItem>
    onItemPressed: (item: ProfileItem) => void
}
function AccountSection(props: Props) {
    const {
        borders
	} = useTheme();

    const c = colorTokens();

    return (

        <View style={[borders.rounded_16, { backgroundColor: c.background.default.neutrals.secondary }]}>
            <View style={[]}>
                {
                    props.items.map((item, index) => (
                        <View key={index}>
                            <AccountItem item={item} onItemPressed={props.onItemPressed}/>
                            {
                                index == props.items.length -1 ? null : (
                                    <View style={[{borderBottomWidth: 1, borderBottomColor: c.stoke.default.default}]} />
                                )
                            }
                        </View>
                    ))
                }
            </View>

        </View>
        
    )
}
export default AccountSection