import { Dimensions, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import { SocialConnectCommentListing } from "@/components/molecules";
import Modal from "react-native-modal";
import { useTheme } from "@/theme";

type Props = {
    isModalVisible: any,
    setIsModalVisible: (isVisible: boolean) => void
    selectedCommentItem: any
}

function SocialConnectCommentBottomSheet(props: Props) {

    const { isModalVisible, setIsModalVisible, selectedCommentItem } = props;

    const {
		layout,
		gutters,
		backgrounds,
	} = useTheme();

    return (
        <Modal
            propagateSwipe={true}
            isVisible={isModalVisible}
            animationIn="slideInUp"
            style={[layout.justifyEnd, gutters.margin_0]}
            swipeDirection={['up', 'left', 'right', 'down']}
            onBackdropPress={() => {
                setIsModalVisible(false)
            }}
            avoidKeyboard={true}
        >
            <View style={[backgrounds.gray100 ,{borderTopRightRadius: 16, borderTopLeftRadius: 16,overflow: 'hidden',height: Dimensions.get('window').height*0.6, width: '100%'}]}>
                <View style={[layout.flex_1, ]}>
                    <SocialConnectCommentListing item={selectedCommentItem} />
                </View>
            </View>
            
        </Modal>
    )
}

export default SocialConnectCommentBottomSheet;
