import { Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import PodcastThumb from '@/theme/assets/images/Home/PodcastThumb.png'
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import CrossIcon from "@/theme/assets/images/CrossIcon.png";

function GroupsListingCard(props) {

    const { layout, gutters, fonts } = useTheme();
    const c = colorTokens();

    const { item } = props;

    return (
        <View style={[{ flex: 1, padding: 10, borderRadius: 8, borderWidth: 1, borderColor: c.stoke.default.default, gap: 8 }]}>
            <View>
                <FastImage
                    source={PodcastThumb}
                    style={[{width: '100%', height: 100, borderRadius: 6}]}
                />
                <TouchableOpacity style={[layout.absolute, gutters.padding_4, { zIndex: 10, borderWidth: 1, borderColor: c.stoke.default.default, borderRadius: 4, top: 6, right: 6 }]}>
                    <AKIcon source={CrossIcon} size={16} tintColor={c.content.onBold.default.default} />
                </TouchableOpacity>
            </View>
            <View style={{ flex: 1, gap: 4 }}>
                <Text style={[fonts.fontSizes.headings.H6, fonts.lineHeight.headings.H6, fonts.Bold, { color: c.content.default.emphasis }]}>{item.name}</Text>
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Regular, { color: c.content.default.subdued }]}>3 members . public members</Text>
            </View>
            <TouchableOpacity style={[layout.itemsCenter, layout.justifyCenter, {backgroundColor: c.fill.bold.neutrals.rest, padding: 10, borderRadius: 10}]} >
                <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, { color: c.content.onBold.default.default }]}>Join</Text>
            </TouchableOpacity>
        </View>
    )
}

export default GroupsListingCard;
