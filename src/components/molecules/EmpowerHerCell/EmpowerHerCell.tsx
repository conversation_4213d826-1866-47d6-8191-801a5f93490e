import { AKFastImage, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { empowerHerListingSchema } from "@/types/schemas/empowerHerListings";
import { empowerHerPostSchema } from "@/types/schemas/empowerHerPostsListing";
import { Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import { z } from "zod";
import AkinaLogo from "@/theme/assets/images/AkinaLogo.png"
type Props = {
    onItemPressed: (item: z.infer<typeof empowerHerListingSchema> | z.infer<typeof empowerHerPostSchema>) => void
    item: z.infer<typeof empowerHerListingSchema> | z.infer<typeof empowerHerPostSchema>
}
function EmpowerHerCell(props: Props) {
    const {
		layout,
		gutters,
        fonts,
        borders,
        colors,
        backgrounds
	} = useTheme();
    return (
        <TouchableOpacity onPress={() => props.onItemPressed(props.item)}>
       
            <View style={[ gutters.marginHorizontal_16 , gutters.marginTop_24,{height: 164 }]}>
                <View style={[ layout.flex_1]}>
                    <AKFastImage
                        uri={props.item.thumbnail}
                        style={[layout.flex_1, borders.rounded_16]}
                    />
                    <View style={[layout.absolute,backgrounds.black, borders.rounded_16,{height: 164, left:0, right: 0, opacity:0.3 }]}></View>

                    <View style={[layout.absolute, layout.top0, layout.left0]}>
                        <ImageVariant
                            source={AkinaLogo}
                            style={ [ gutters.marginLeft_16, gutters.marginTop_12, {tintColor:  colors.yellow, resizeMode: 'contain'  , width: 24, height: 40}]}
                        />

                    </View>
                </View>
                <View style={[layout.absolute, layout.justifyEnd, gutters.marginHorizontal_16,{height: 164}]}>
                    <Text style={[fonts.gray50]}>{props.item.title}</Text>
                    <Text style={[fonts.gray50 ,gutters.marginBottom_16]}>{(typeof props.item === typeof empowerHerListingSchema) ? props.item.description : props.item.subtitle }</Text>
                </View>
            </View>
        </TouchableOpacity>
    )
}
export default EmpowerHerCell