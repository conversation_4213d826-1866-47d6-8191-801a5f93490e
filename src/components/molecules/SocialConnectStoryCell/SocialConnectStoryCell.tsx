import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from "react-native"
import Video, { VideoRef } from "react-native-video"
import { ElementRef, MutableRefObject, forwardRef, useImperativeHandle, useRef, useState } from "react";

import { z } from "zod"
import { useTheme } from "@/theme"
import LinearGradient from "react-native-linear-gradient";

type Props = {
    item: z.infer<typeof socialConnectPostSchema>
    isFocused: boolean
    onProfileTapped?: (item:  z.infer<typeof socialConnectPostSchema>) => void

}

const SocialConnectStoryCell = forwardRef( function SocialConnectStoryCell(props: Props, ref: any) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    const [isPreloading, setIsPreloading] = useState(false)
    useImperativeHandle(ref, () => ({
        // each key is connected to `ref` as a method name
        // they can execute code directly, or call a local method
        pauseVideo: () => { pauseVideo() },
        playVideo: () => { playVideo() },

    }))
    function pauseVideo() {
        videoRef.current?.pause()
    }
    function playVideo() {
        videoRef.current?.resume()
    }
    const videoRef = useRef<VideoRef>(null)

    return(
        <View style={[layout.itemsCenter, layout.justifyCenter,{width: '100%', height: '100%'}]}>
            {isPreloading &&
                <ActivityIndicator
                    animating
                    color={"gray"}
                    size="large"
                    style={{ flex: 1, position:"absolute", top:"50%", left:"45%" }}
                />
            }
            <Video
                ref={videoRef}
                source={props.isFocused ? {uri: props.item.media_link ?? ''} : {} }
                controls={false}
                paused={!props.isFocused}
                style={[ gutters.marginHorizontal_0,{height: '100%', width: '100%', overflow: 'hidden'}]}
                hideShutterView={false}
                allowsExternalPlayback={false}
                repeat={true}
                onLoadStart={() => setIsPreloading(true)}
                onReadyForDisplay={() => setIsPreloading(false)}
                disableFocus={true}
                onPlaybackStateChanged={(e) => {
                
                }}  
            />
            <View style={[layout.absolute ,{bottom: 100, left:0, right: 0}]}>
                <LinearGradient style={styles.cardsongDetails} locations={[0,1,1,0]} colors={['rgba(0,0,0,0.16)','rgba(0, 0, 0, 0.8)','transparent','transparent']} useAngle={true} angle={0}>
                <TouchableOpacity onPress={() => props.onProfileTapped !=null ? props.onProfileTapped(props.item) : () => {}}>
                        <Text style={[gutters.marginLeft_12,fonts.gray100, fonts.bold ]}>{`@${props.item?.user?.first_name} ${props.item?.user?.last_name}`}</Text>
                </TouchableOpacity>
                    <Text style={[fonts.gray100 ,gutters.marginLeft_12,gutters.marginTop_4]}>{props.item.text ?? ''}</Text>
                </LinearGradient>  
            </View>
        </View>
    )
})
const styles = StyleSheet.create({
    cardsongDetails: {
        shadowColor: "transparent",
        shadowOffset: {
        width: 0,
        height: 0
        },
        width: "100%",
        padding: 16,
        backgroundColor: "transparent"
    }

})
export default SocialConnectStoryCell