import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useTheme } from "@/theme";
import { z } from "zod";
import { aiQuestion } from "@/types/schemas/aiQuestion";
import { QuestionCard } from '../QuestionCard/QuestionCard';
import { colorTokens } from "@/theme/colorTokens";

type EmptyChatViewProps = {
    userName: string;
    questions?: z.infer<typeof aiQuestion>[];
    keyboardHeight: number;
    onQuestionPress: (question: z.infer<typeof aiQuestion>) => void;
};

export const EmptyChatView: React.FC<EmptyChatViewProps> = ({ 
    userName, 
    questions, 
    keyboardHeight, 
    onQuestionPress 
}) => {
    const { layout, fonts, gutters } = useTheme();
    const c = colorTokens();

    return (
        <View style={[
            layout.absolute, 
            { marginBottom: 0, top: 0, bottom: 134 + keyboardHeight }
        ]}>
            <View style={[
                layout.flex_1, 
                { flexGrow: 1 }, 
                layout.justifyCenter, 
                layout.itemsCenter, 
                gutters.marginHorizontal_32
            ]}>
                <Text style={[
                    fonts.fontSizes.headings.H3, 
                    fonts.lineHeight.headings.H3, 
                    fonts.Bold,
                    { textAlign: 'center', color: c.content.default.default }
                ]}>
                    {`Hello, ${userName} - how can I help you?`}
                </Text>
            </View>
            
            <View style={[gutters.marginBottom_4]}>
                <ScrollView
                    horizontal
                    contentContainerStyle={[
                        { minHeight: 88 }, 
                        gutters.paddingLeft_8, 
                        gutters.paddingRight_8
                    ]}
                    showsHorizontalScrollIndicator={false}
                    nestedScrollEnabled
                >
                    {questions?.map((item, index) => (
                        <QuestionCard 
                            key={index}
                            item={item} 
                            onPress={onQuestionPress}
                        />
                    ))}
                </ScrollView>
            </View>
        </View>
    );
};