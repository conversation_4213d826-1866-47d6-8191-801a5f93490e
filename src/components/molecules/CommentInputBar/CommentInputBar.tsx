// CommentInputBar.tsx
import { View, Text, TextInput, TouchableOpacity } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import FastImage from "react-native-fast-image";
import ArrowRightIcon from "@/theme/assets/images/SocialConnect/ArrowRightIcon.png";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import DefaultProfileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png";
import CrossIcon from "@/theme/assets/images/SocialConnect/CrossIcon.png";
import { forwardRef, Ref } from "react";
import { Comment } from "@/types/models/Comment";
import { AKFastImage } from "@/components/atoms";

interface CommentInputBarProps {
  commentText: string;
  setCommentText: (text: string) => void;
  onSendPress: () => void;
  onPressCross: () => void;
  isSending: boolean;
  replyingComment: Comment | null;
  editingComment: Comment | null;
  profilePhotoUrl?: string;
}

const CommentInputBar = forwardRef<TextInput, CommentInputBarProps>(({ 
  commentText, 
  setCommentText, 
  onSendPress, 
  onPressCross, 
  isSending, 
  replyingComment, 
  editingComment, 
  profilePhotoUrl 
}, ref) => {
  const { layout, gutters, fonts } = useTheme();
  const c = colorTokens();

  const isTextFieldBarVisible = !!replyingComment || !!editingComment;
  let barText = '';
  
  if (isTextFieldBarVisible) {
    if (replyingComment) {
      barText = `Replying to ${replyingComment.user.first_name} ${replyingComment.user.last_name}`;
    } else {
      barText = 'Editing...';
    }
  }

  return (
    <View style={[layout.row, layout.itemsEnd, { gap: 8 }]}>
      <AKFastImage
        uri={profilePhotoUrl}
        placeholder={DefaultProfileIcon}
        resizeMode={FastImage.resizeMode.cover}
        style={[{ width: 36, height: 36, borderRadius: 18 }]}
      />
      <View style={[
        layout.flex_1, 
        { 
          height: isTextFieldBarVisible ? 76 : 36, 
          borderRadius: 8, 
          borderWidth: 1, 
          borderColor: c.stoke.default.default 
        }
      ]}>
        {isTextFieldBarVisible && (
          <View style={[
            layout.row, 
            layout.flex_1, 
            layout.itemsCenter, 
            layout.justifyBetween, 
            {
              backgroundColor: c.background.default.neutrals.secondary, 
              borderTopRightRadius: 8, 
              borderTopLeftRadius: 8, 
              height: 32, 
              paddingHorizontal: 10
            }
          ]}>
            <Text style={[
              fonts.fontSizes.body.xs, 
              fonts.lineHeight.body.xs, 
              fonts.body, 
              { color: c.content.default.subdued }
            ]}>
              {barText}
            </Text>
            <AKIcon source={CrossIcon} size={16} onPress={onPressCross} />
          </View>
        )}
        <View style={[layout.row, layout.flex_1, layout.itemsCenter, { height: 44, paddingHorizontal: 10, paddingVertical: 6 }]}>
          <TextInput
            ref={ref}
            style={[layout.flex_1, gutters.marginRight_8, fonts.body, { height: 44 }]}
            placeholder="Add a comment..."
            placeholderTextColor={c.content.default.subdued}
            value={commentText}
            onChangeText={(text) => setCommentText(text)}
          />
          <TouchableOpacity
            style={[
              layout.itemsCenter, 
              layout.justifyCenter, 
              { 
                borderRadius: 4, 
                height: 24, 
                width: 24, 
                backgroundColor: c.fill.default.primary.rest 
              }
            ]}
            onPress={onSendPress}
            disabled={isSending || commentText === ''}
          >
            <AKIcon tintColor={c.content.primary.default} source={ArrowRightIcon} size={16} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
});

export default CommentInputBar;