import Modal from "react-native-modal";
import { ActivityIndicator, Text, View } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";

function ScreenLoader(props: any) {

    const { loadingText } = props;

    const { layout, fonts, gutters } = useTheme();
    const c = colorTokens();

    return (
        <Modal
            isVisible={true}
            animationIn="fadeIn"
            style={{justifyContent: 'center', alignItems: 'center'}}
            backdropOpacity={0.4}
        >
            <View style={[layout.row, layout.itemsCenter, { backgroundColor: c.custom.white, borderRadius: 6, padding: 10, gap: 6 }]}>
                <ActivityIndicator color={c.background.medium.primary.default} />
                <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, {color: c.content.default.emphasis}]}>{loadingText}</Text>
            </View>
        </Modal>
    )
}

export default ScreenLoader;
