import { ImageVariant } from "@/components/atoms";
import { AddCommentSection, BlogButtonsContainer } from "@/components/molecules";
import { useTheme } from "@/theme";
import { VIDEO_TYPES } from "@/utils/constants";
import { checkVideoInCache, getYoutubeID } from "@/utils/utility";
import { useEffect, useRef, useState } from "react";
import { ActivityIndicator, Text, TouchableOpacity, useWindowDimensions, View } from "react-native"
import Video, { VideoRef } from "react-native-video";
import YoutubePlayer from "react-native-youtube-iframe";
import ReplayButton from "@/theme/assets/images/Replay.png";
import { useIsFocused } from "@react-navigation/native";
import { StringDecoder } from "string_decoder";

type Props = {
    onLikeButtonPressed: () => void
    onBookmarkButtonPressed: () => void
    onCommentButton: () => void
    onCommentSend: () => void
    commentRef: any
    profilePhoto: string | undefined
    title: string
    currentVideo: any
}

function VideoDetailListHeader(props: Props) {

    const {
        onBookmarkButtonPressed, onLikeButtonPressed, onCommentButton, onCommentSend,
        currentVideo, commentRef, profilePhoto, title,
    } = props;

    const videoRef = useRef<VideoRef>(null)

    const { width } = useWindowDimensions();

    const isFocused = useIsFocused();

    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isReplayVisible, setIsReplayVisible] = useState<boolean>(false);
    const [cachedVideoPath, setCachedVideoPath] = useState<string>('');

    const {
		layout,
		gutters,
        fonts,
        backgrounds,
	} = useTheme();

    useEffect(() => {
        if (!isFocused) {
          videoRef.current?.pause();
        }
    }, [isFocused]);

    async function configureLink(): Promise<void> {
        const link = await checkVideoInCache(currentVideo.video_link);
        setCachedVideoPath(link);
    };

    useEffect(() => {
        console.log(currentVideo)
        if (currentVideo.video_type == VIDEO_TYPES.SELF_HOSTED && !cachedVideoPath) {
            configureLink();
        }
    }, [currentVideo?.video_type]);

    function onReplay(): void {
        setIsReplayVisible(false);
        videoRef.current?.seek(0);
    }

    function onLoad(): void {
        setIsLoading(false);
    }

    function onEnd(): void {
        setIsReplayVisible(true);
    }

    function onPlaybackStateChanged(e: any): void {
        if (e.isPlaying && isReplayVisible) {
            setIsReplayVisible(false);
        }
    }

    function onFullscreenPlayerDidDismiss(): void {
        setTimeout(() => videoRef.current?.resume(), 500)
    }

    return (
        <View>
            <View style={[gutters.marginHorizontal_16, gutters.marginVertical_12 ,layout.justifyCenter, layout.itemsCenter]}>
                <Text style={[fonts.size_32, fonts.Medium]}>{title}</Text>
            </View>
            {currentVideo.video_type == VIDEO_TYPES.SELF_HOSTED && 
            <View style={{height: 200}}>
                
                <Video
                    ref={videoRef}
                    source={{uri: cachedVideoPath}}
                    controls={true}
                    style={[ gutters.marginHorizontal_0,{height: 200}]}
                    hideShutterView={false}
                    allowsExternalPlayback={false}
                    onLoad={onLoad}
                    onEnd={onEnd}
                    onPlaybackStateChanged={onPlaybackStateChanged}
                    onFullscreenPlayerDidDismiss={onFullscreenPlayerDidDismiss}
                />
                {
                    isReplayVisible && (
                        <View style={[layout.absolute, layout.itemsCenter, layout.justifyCenter, backgrounds.black ,{height: 200, left: 0, right: 0}]}>
                            <TouchableOpacity onPress={onReplay}>
                                <ImageVariant source={ReplayButton} />
                            </TouchableOpacity>
                        </View>
                    )
                }
                {
                    isLoading && (
                        <View style={[layout.absolute, layout.itemsCenter, layout.justifyCenter ,{height: 200, left: 0, right: 0}]}>
                            <ActivityIndicator animating={isLoading}/>
                        </View>
                    )
                }
            </View>
            
            }
            {currentVideo.video_type == VIDEO_TYPES.YOUTUBE && 
            <YoutubePlayer
                height={width* 9/16}
                videoId={getYoutubeID(currentVideo.video_link)}

            />
            }
            <BlogButtonsContainer 
                isLikeSelected={currentVideo.is_liked ?? false}
                isSaveSelected={currentVideo.is_bookmarked ?? false}
                onSaveButton={onBookmarkButtonPressed}
                onLikeButton={onLikeButtonPressed}
                onCommentButton={onCommentButton}
            />
            <View>
                <AddCommentSection ref={commentRef}  onSend={onCommentSend} image={profilePhoto}/>
            </View>
        </View>
    )
}
export default VideoDetailListHeader;
