import { Text, View, ImageSourcePropType } from "react-native"
import { useTheme } from "@/theme";
import { ImageVariant } from "@/components/atoms";
import { colorTokens } from "@/theme/colorTokens";

type Props = {
    heading?: string;
    desc?: string;
    image?: ImageSourcePropType;
    isFullScreen: boolean;
    actionButton?: () => JSX.Element;
}

function EmptyDataView({ heading, desc, image, isFullScreen = false, actionButton }: Props) {

    const {
        layout,
        fonts,
        gutters,
        backgrounds
    } = useTheme();

    const c = colorTokens();

    function getEmptyView() {
        return (
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter, gutters.paddingHorizontal_40, { gap: 8, backgroundColor: 'white' }]}>
                {
                    image && (
                        <ImageVariant source={image} style={{height: 132, width: 132, resizeMode: 'contain'}}/>
                    )
                }
                <View style={[layout.itemsCenter, { gap: 8 }]}>
                {
                    heading && (
                        <Text style={[fonts.fontSizes.headings.H5, fonts.lineHeight.headings.H5, fonts.Bold, {color: c.content.default.emphasis}]}>{heading}</Text>
                    )
                }
                {
                    desc && (
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, {color: c.content.default.subdued, textAlign: 'center'}]}>{desc}</Text>
                    )
                }
                {
                    actionButton && actionButton()
                }
                </View>
            </View>
        )
    }

    return isFullScreen ? (
        <View style={[layout.absolute, layout.flex_1, backgrounds.white, { top: 0, left: 0, right: 0, bottom: 0 }]}>
            {getEmptyView()}
        </View>
    ) : (
        getEmptyView()
    )
}

export default EmptyDataView;
