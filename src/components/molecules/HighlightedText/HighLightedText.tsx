import React from 'react';
import { Text, TextStyle } from 'react-native';

interface HighlightedTextProps {
  text: string;
  searchQuery: string;
  style?: TextStyle;
  numberOfLines?: number;
  highlightStyle?: TextStyle;
}
export function HighlightedText({ 
  text, 
  searchQuery, 
  style, 
  numberOfLines = 1,
  highlightStyle 
}: HighlightedTextProps) {
  if (!searchQuery.trim()) {
    return <Text numberOfLines={numberOfLines} style={style}>{text}</Text>;
  }

  const parts = text.split(new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi'));
  
  return (
    <Text style={style}>
      {parts.map((part, index) => 
        part.toLowerCase() === searchQuery.toLowerCase() ? (
          <Text 
            numberOfLines={numberOfLines} 
            key={index} 
            style={[style, highlightStyle, { fontWeight: '600' }]}
          >
            {part}
          </Text>
        ) : (
          <Text numberOfLines={numberOfLines} key={index}>{part}</Text>
        )
      )}
    </Text>
  );
}