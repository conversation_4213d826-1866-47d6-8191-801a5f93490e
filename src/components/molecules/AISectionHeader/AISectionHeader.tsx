import React from 'react';
import { View, Text } from 'react-native';
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { MessageSection } from '@/screens/SideTabs/Home/AkinaAI/ChatHome/ChatAI/Types/ChatTypes';

type AISectionHeaderProps = {
    section: MessageSection;
};

export function AISectionHeader(props: AISectionHeaderProps) {
    const { gutters, layout, fonts } = useTheme();
    const c = colorTokens();

    return (
        <View style={[gutters.paddingVertical_8, layout.itemsCenter]}>
            <View style={[
                gutters.paddingHorizontal_12, 
                gutters.paddingVertical_4, 
                // { backgroundColor: c.background.default.neutrals.secondary, borderRadius: 12 }
            ]}>
                <Text style={[
                    fonts.fontSizes.body.xs, 
                    fonts.lineHeight.body.xs, 
                    { fontWeight: '500', color: c.content.default.subdued }
                ]}>
                    {props.section.title}
                </Text>
            </View>
        </View>
    );
};