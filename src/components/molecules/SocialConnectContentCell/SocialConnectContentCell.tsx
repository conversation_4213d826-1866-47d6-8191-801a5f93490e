import { Dimensions, View } from "react-native"
import { useTheme } from "@/theme";
import { useHeaderHeight } from "@react-navigation/elements";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { AnimatingHeart, SocialConnectPostTopItem } from "@/components/molecules";
import { z } from "zod";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";

import { getSocialConnectCellComponent } from "../utility";

type Props = {
    cellRef: any,
    isFocused: boolean
    onLikeButtonTapped: (item: z.infer<typeof socialConnectPostSchema>) => void
    onCommentButtonTapped: (item: z.infer<typeof socialConnectPostSchema>) => void
    onShareButtonTapped: (item: z.infer<typeof socialConnectPostSchema>) => void
    item: z.infer<typeof socialConnectPostSchema>
    onProfileTapped: (item: z.infer<typeof socialConnectPostSchema>) => void
}

function SocialConnectContentCell(props: Props) {

    const { cellRef, isFocused, onLikeButtonTapped, onCommentButtonTapped, 
        onShareButtonTapped, item, onProfileTapped } = props;

    const {
		layout,
		backgrounds,
	} = useTheme();

    const headerHeight = useHeaderHeight();

    const CellComponent = getSocialConnectCellComponent(item?.type);

    return (
        <View style={[layout.flex_1, layout.justifyCenter, layout.itemsCenter, backgrounds.black ,{width: Dimensions.get('window').width, height: Dimensions.get('window').height - headerHeight }]}>
            <AnimatingHeart
                onDoubleTap={() => {
                    onLikeButtonTapped(item)
                }}
                isLiked={item?.is_liked}
            >
                <CellComponent
                    {...(item?.type == 'story' && { 
                        ref: cellRef,
                        isFocused,
                    })}
                    onProfileTapped={onProfileTapped}
                    item={item}
                />
            </AnimatingHeart>
            <SocialConnectPostTopItem
                item={item}
                onCommentButtonTapped={onCommentButtonTapped} 
                onLikeButtonTapped={onLikeButtonTapped}
                onShareButtonTapped={onShareButtonTapped}
            />
        </View>
    )

}

export default SocialConnectContentCell;
