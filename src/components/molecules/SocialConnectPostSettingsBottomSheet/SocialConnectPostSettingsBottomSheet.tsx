import { Alert, Text, TouchableOpacity, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import RemoveUserIcon from "@/theme/assets/images/SocialConnect/RemoveUserIcon.png";
import ReportIcon from "@/theme/assets/images/ReportIcon.png";
import EditIcon from "@/theme/assets/images/SocialConnect/EditIcon.png";
import BinIcon from "@/theme/assets/images/SocialConnect/BinIcon.png";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import followUnfollowUser from "@/services/socialConnect/users/followUnfollowUser";
import { useCustomToast } from "@/hooks/useCustomToast";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { z } from 'zod';
import deletePost from "@/services/socialConnect/deletePost";
import { useNavigation } from "@react-navigation/native";
import { useQueryCacheUpdate } from "@/screens/SideTabs/Home/SocialConnect/hooks/useQueryCacheUpdate";
import AKBottomSheet from "@/components/atoms/AKBottomSheet/AKBottomSheet";

type Props = {
    isModalVisible: any;
    setIsModalVisible: (isVisible: boolean) => void;
    item: z.infer<typeof socialConnectPostSchema>;
    isOwnPost?: boolean;
    shouldUpdate?: (userId?: number) => void;
    setActionStatus: () => {};
}

type RemoveMemberAlertOption = {
    text: string;
    onPress?: () => void | Promise<void>;
    style?: 'default' | 'cancel' | 'destructive';
};

type AddSocialPostParams = {
    postId: number;
    postText?: string;
    postType: 'photo' | 'video';
    isUpdating: boolean;
    photoLink?: string;
    videoLink?: string;
};

function SocialConnectPostSettingsBottomSheet(props: Props) {

    const { isModalVisible, setIsModalVisible, item, isOwnPost, setActionStatus, onPressReport } = props;
    const showToast = useCustomToast();
    const { updateProfileCache } = useQueryCacheUpdate();

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
    } = useTheme();

    const queryClient = useQueryClient();
    const navigation = useNavigation();

    const c = colorTokens();

    function closeSheet() {
        setIsModalVisible(false);
    }

    function callSetActionStatus(status: boolean) {
        if (!!setActionStatus) {
            setActionStatus(status);
        }
    }

    const unFollowMutation = useMutation(
        {
            mutationFn: (data: any) => followUnfollowUser(data),
            onSuccess: (response) => {
                closeSheet();
                showToast('Member Removed Successfully');
                if (props.shouldUpdate != null) {
                    props.shouldUpdate(props.item?.user?.id)
                }
                // setIsLoading(false)
            },
            onError: (error) => {
                // setIsLoading(false)
                closeSheet();
                Alert.alert("Error!", error.message)
            }
        }
    );

    const deletePostMutation = useMutation(
        {
            mutationFn: (data: any) => deletePost(data),
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );
// social-connect-posts-${route.params.userId}
    function onSuccessMutation(): void {
        queryClient.setQueryData([`get_user_profile${item?.user?.id}`], (cacheData: any) => {
            let posts = cacheData?.posts?.filter((post: any) => post.id !== item?.id );
            console.log(posts)
            return {
                ...cacheData,
                posts,
            };
        });
        queryClient.setQueriesData(
            {
                predicate: (query) => [`social-connect-user-posts-${item?.user?.id}`,'social-connect-posts', 'social-connect-posts-following'].includes(query?.queryKey[0] as string),
            },
            (cacheData: any) => {
            if (cacheData != null) {
                let pages = cacheData?.pages?.map((page: any) => {
                    let pageData = page?.data?.filter((post: any) => post.id !== item?.id);
                    return {
                        ...page,
                        data: pageData,
                    };
                });
                return {
                    ...cacheData,
                    pages: pages,
                };
            }
        });
        updateProfileCache(false);
        callSetActionStatus(false);
        if (navigation.canGoBack()) {
            navigation.goBack();
        }
        showToast("Post Deleted Successfully", true)
    }

    function onErrorMutation(error: any): void {
        callSetActionStatus(false);
        closeSheet();
        showToast("Oops! Couldn't Remove Post", true)
    }

    function onPressRemoveMember() {
        let options: RemoveMemberAlertOption[] = [
            {
                text: 'Cancel',
            },
            {
                text: 'Remove',
                onPress: async () => {
                    const apiData = {
                        user_id: item?.user?.id
                    };
                    console.log(apiData)
                    unFollowMutation.mutate(apiData);
                },
                style: 'destructive',
            },
        ]
        Alert.alert('Remove this member?', 'You won’t see their updates or shared activity anymore.', options)
    }

    function onPressReportPost() {
        closeSheet();
        onPressReport();
    }

    function onPressEdit() {
        closeSheet();
        const postTypeMapping: Record<'post' | 'story', 'photo' | 'video'> = {
            'post': 'photo',
            'story': 'video',
        };
        const params: AddSocialPostParams = {
            postId: item?.id,
            postText: item?.text,
            postType: postTypeMapping[item?.type as keyof typeof postTypeMapping],
            isUpdating: true,
            ...(item?.type === 'post' && { photoLink: item?.media_link }), 
            ...(item?.type === 'story' && { videoLink: item?.media_link }),
        }
        navigation.navigate('AddSocialPost', params);
    }

    function onPressDelete() {
        closeSheet();
        Alert.alert('Delete Post', 'This action can’t be undone. Your post will be permanently removed.', [
            {
              text: 'Cancel',
              onPress: () => {},
            },
            {
                text: 'Delete',
                onPress: () => {
                    callSetActionStatus(true);
                    deletePostMutation.mutate(item?.id)
                },
                style:'destructive'
            },
        ]);
    }

    const settings = isOwnPost
        ? [
            { id: 1, text: 'Edit', textColor: c.content.default.default, icon: EditIcon, onPress: onPressEdit },
            { id: 2, text: 'Delete', textColor: c.content.error.default, icon: BinIcon, onPress: onPressDelete },
        ]
        : [
            { id: 1, text: 'Report Post', textColor: c.content.error.default, icon: ReportIcon, onPress: onPressReportPost },
            // { id: 2, text: 'Remove Member', textColor: c.content.error.default, icon: RemoveUserIcon, onPress: onPressRemoveMember },
        ];

    return (
        <AKBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible}>
            {
                settings.map(option => (
                    <TouchableOpacity key={option.id} onPress={option.onPress} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_12, gutters.paddingHorizontal_16]}>
                        <AKIcon source={option.icon} styles={[gutters.marginRight_12]} />
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, {color: option.textColor}]}>{option.text}</Text>
                    </TouchableOpacity>
                ))
            }
        </AKBottomSheet>
    )
}

export default SocialConnectPostSettingsBottomSheet;
