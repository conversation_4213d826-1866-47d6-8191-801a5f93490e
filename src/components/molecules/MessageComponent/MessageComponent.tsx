import { AKFastImage } from "@/components/atoms";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { messageSchema } from "@/types/schemas/conversationSchema"
import { userSchema } from "@/types/schemas/user"
import moment from "moment";
import { Text, View } from "react-native";
import FastImage from "react-native-fast-image";
import Markdown from "react-native-markdown-display";
import ThumbsUpAI from "@/theme/assets/images/Home/ThumbsUpAI.png"
import ThumbsDownAI from "@/theme/assets/images/Home/ThumbsDownAI.png"
import CopyAI from "@/theme/assets/images/Home/CopyAI.png"

import { z } from "zod"

type Props = {
    item: z.infer<typeof messageSchema>,
    user: z.infer<typeof userSchema>,
    onThumbsUp: (item: z.infer<typeof messageSchema>,) => void
    onThumbsDown: (item: z.infer<typeof messageSchema>,) => void
    onCopy: (item: z.infer<typeof messageSchema>,) => void

}
function MessageComponent(props: Props) {
    const status = props.item.sender_type === 'ai'
    const {
		layout,
		gutters,
        fonts,
        borders,
        colors,
        backgrounds
	} = useTheme();

    const c = colorTokens();
    function onThumbsUp() {
        if (props.onThumbsUp != null) {
            props.onThumbsUp(props.item)
        }
    }
    function onThumbsDown() {
        if (props.onThumbsDown != null) {
            props.onThumbsDown(props.item)
        }
    }
    function onCopy() {
        if (props.onCopy != null) {
            props.onCopy(props.item)
        }
    }
    return (
        <View>
            <View
                style={
                    status
                        ? [layout.itemsStart, gutters.marginTop_12 ,gutters.marginBottom_16, gutters.marginLeft_16 ,{width: '100%'}]
                        : [layout.itemsStart, gutters.marginTop_12  ,gutters.marginBottom_16, { width: '100%' ,alignItems: "flex-end" }]
                }
            >
                <View style={[gutters.marginRight_16,{ flexDirection: "row",  }]}>
                    {/* <AKFastImage
                        uri={props.user.profile_photo}
                        placeholder={require('@/theme/assets/images/AkinaLogo.png')}
                        resizeMode={FastImage.resizeMode.contain}
                        style={[backgrounds.gray100, gutters.marginHorizontal_12 ,{height: 40, width: 40, borderRadius: 20}]}
                    /> */}
                    <View
                        style={
                            status
                                ? [gutters.padding_16,gutters.marginBottom_4, {maxWidth: '70%', borderRadius: 12, backgroundColor: c.background.default.neutrals.secondary, borderWidth: 1, borderColor: c.stoke.default.subdued, borderBottomLeftRadius: 0},layout.flex_1 ]
                                : [gutters.padding_16,gutters.marginBottom_4,{ maxWidth: '70%', borderRadius: 12, backgroundColor: c.background.default.neutrals.default, borderWidth: 1, borderColor: c.stoke.default.subdued, borderBottomRightRadius: 0}]
                        }
                    >
                        <View style={[layout.flex_1]}>
                            <Markdown style={{text: {color: c.content.default.default, }}}>
                                {`${props.item.content}`}
                            </Markdown>
                        </View>
                        
                    </View>
                </View>
                {status && 
                <View style={[layout.row, layout.justifyBetween ,{ width: '70%'}]}>
                    <View style={[layout.row]}>
                        <AKIcon tintColor={props.item.like_status == 1 ? c.content.info.default : c.content.default.default} onPress={onThumbsUp} source={ThumbsUpAI}/>
                        <AKIcon tintColor={props.item.like_status == 2 ? c.content.info.default : c.content.default.default} onPress={onThumbsDown} source={ThumbsDownAI}/>
                        <AKIcon onPress={onCopy} source={CopyAI}/>
                    </View>
                    {status &&  <Text style={[gutters.marginRight_24, fonts.fontSizes.body.xs,{ alignSelf: status ? 'center' : 'flex-end',color: c.content.default.subdued }]}>{ moment(props.item.created_at).format("hh:mm A") /*timeSince( new Date(props.item.created_at))*/}</Text>}
                </View>
                }
                {!status && <Text style={[gutters.marginRight_24, fonts.fontSizes.body.xs,{ alignSelf: status ? 'center' : 'flex-end',color: c.content.default.subdued }]}>{ moment(props.item.created_at).format("hh:mm A") /*timeSince( new Date(props.item.created_at))*/}</Text>}
            </View>
        </View>
    )

}
export default MessageComponent