import { useTheme } from "@/theme"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { StyleSheet, Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import { z } from "zod"
import LinearGradient from "react-native-linear-gradient"

type Props = {
    item: z.infer<typeof socialConnectPostSchema>
    onProfileTapped?: (item:  z.infer<typeof socialConnectPostSchema>) => void
}

function SocialConnectPostCell(props: Props) {
    const {
		layout,
		gutters,
        fonts,
	} = useTheme();
    return(
        <View style={[layout.itemsCenter, layout.justifyCenter, {width: '100%', height: '100%'}]}>
            <FastImage
                source={{uri: props.item.media_link ?? ''} }
                resizeMode={FastImage.resizeMode.contain}
                style={[gutters.marginHorizontal_0, gutters.marginVertical_0 ,{width: '100%', height: '100%'}]}
            />
            <View style={[layout.absolute ,{bottom: 100, left:0, right: 0}]}>
                <LinearGradient style={styles.cardsongDetails} locations={[0,1,1,0]} colors={['rgba(0,0,0,0.16)','rgba(0, 0, 0, 0.8)','transparent','transparent']} useAngle={true} angle={0}>
                    <TouchableOpacity onPress={() => props.onProfileTapped !=null ? props.onProfileTapped(props.item) : () => {}}>
                        <Text style={[gutters.marginLeft_12,fonts.gray100, fonts.bold ]}>{`@${props.item?.user?.first_name} ${props.item?.user?.last_name}`}</Text>
                    </TouchableOpacity>                    
                    <Text style={[fonts.gray100 ,gutters.marginLeft_12,gutters.marginTop_4]}>{props.item.text ?? ''}</Text>
                </LinearGradient>
                
            </View>
        </View>
    )

}
const styles = StyleSheet.create({
    cardsongDetails: {
        shadowColor: "transparent",
        shadowOffset: {
        width: 0,
        height: 0
        },
        width: "100%",
        // alignItems: "center",
        padding: 16,
        backgroundColor: "transparent"
        }

})
export default SocialConnectPostCell