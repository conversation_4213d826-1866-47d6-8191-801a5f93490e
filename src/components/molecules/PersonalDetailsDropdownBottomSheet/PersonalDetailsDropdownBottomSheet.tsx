import { Text, TouchableOpacity, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import TickIcon from "@/theme/assets/images/TickIcon.png";
import { colorTokens } from "@/theme/colorTokens";
import { DropdownOption } from "@/screens/SideTabs/Settings/constants";
import AKBottomSheet from "@/components/atoms/AKBottomSheet/AKBottomSheet";

type Props = {
    isModalVisible: any,
    setIsModalVisible: (isVisible: boolean) => void
    options?: DropdownOption[]
    onPressOption: (field: string, option: DropdownOption) => void
    selectedOption: any
}
function PeronsalDetailsDropdownBottomSheet(props: Props) {
    const { isModalVisible, setIsModalVisible, options, onPressOption, selectedOption } = props;
    const {
        layout,
        gutters,
        backgrounds,
        fonts,
    } = useTheme();

    const c = colorTokens();
    function closeSheet(): void {
        setIsModalVisible(false);
    }
    function onPress(option: DropdownOption): void {
        onPressOption(selectedOption.field, option);
        closeSheet();
    }

    return (
        <AKBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible}>
            {
                (options || []).map(option => (
                    <TouchableOpacity onPress={() => onPress(option)} style={[layout.row, layout.itemsCenter, layout.justifyBetween, gutters.paddingVertical_12, gutters.paddingHorizontal_16]}>
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, {color: c.content.default.default}]}>{option.value}</Text>
                        {
                            option.value == selectedOption.value && (
                                <AKIcon source={TickIcon} size={16} tintColor={c.content.default.default} styles={[gutters.marginLeft_8]} />
                            )
                        }
                    </TouchableOpacity>
                ))
            }
        </AKBottomSheet>
    )
}
export default PeronsalDetailsDropdownBottomSheet;