import { Dimensions, Text, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import SocialConnectPostCommentsArea from "../SocialConnectPostCommentsArea/SocialConnectPostCommentsArea";
import { z } from "zod";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { BlurView } from "@react-native-community/blur";
import { useState } from "react";
import AnimatedViewBottomSheet from "@/components/atoms/AnimatedViewBottomSheet/AnimatedViewBottomSheet";

type Props = {
    item: z.infer<typeof socialConnectPostSchema>;
    isModalVisible: boolean,
    setIsModalVisible: (isVisible: boolean) => void
}

function SocialConnectPostCommentsSheet(props: Props) {

    const { isModalVisible, setIsModalVisible, item } = props;

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
    } = useTheme();

    const c = colorTokens();

    const [isBlurViewVisible, setIsBlurViewVisible] = useState(false);

    return (
        <AnimatedViewBottomSheet visible={isModalVisible} onClose={setIsModalVisible}>
            <View style={[{borderTopRightRadius: 24, borderTopLeftRadius: 24, height: Dimensions.get('window').height*0.6}]}>
                <View style={[layout.flex_1]}>
                    <View>
                        {
                            isBlurViewVisible && (
                                <View style={[layout.absolute, layout.flex_1, { top: 0, left: 0, right: 0, bottom: 0, zIndex: 5 }]}>
                                    <BlurView blurType="light" blurAmount={7} style={[layout.flex_1, { borderTopRightRadius: 24, borderTopLeftRadius: 24 }]} />
                                </View>
                            )
                        }
                        <View style={[backgrounds.black, gutters.marginTop_12, { borderRadius: 24, height: 5, width: 48, alignSelf: 'center' }]} />
                        <View style={[gutters.paddingVertical_12, layout.itemsCenter, { borderBottomWidth: 2, borderBottomColor: c.stoke.default.subdued }]}>
                            <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, {color: c.content.default.default}]}>Comments</Text>
                        </View>
                    </View>
                    <SocialConnectPostCommentsArea item={item} setIsBlurViewVisible={setIsBlurViewVisible} />
                </View>
            </View>
        </AnimatedViewBottomSheet>
    )
}

export default SocialConnectPostCommentsSheet;
