import { useTheme } from "@/theme";
import { Text, View } from "react-native";
import FastImage from "react-native-fast-image";
import PodcastThumb from '@/theme/assets/images/Home/PodcastThumb.png'
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import MoreIcon from "@/theme/assets/images/SocialConnect/MoreIcon.png";
import UsersIcon from "@/theme/assets/images/UsersIcon.png";

function GroupsListingItem(props) {

    const { item } = props;
    const { name, members, type, newPosts } = item;

    const { layout, gutters, fonts } = useTheme();
    const c = colorTokens();

    function onPressMore() {
        console.log('More')
    }

    return (
        <View style={[layout.row, layout.itemsCenter, gutters.paddingVertical_8, { borderBottomWidth: 1, borderBottomColor: c.stoke.default.subdued }]}>
            <FastImage
                source={PodcastThumb}
                style={[{width: 60, height: 46, borderRadius: 6}]}
            />
            <View style={[layout.flex_1, gutters.marginLeft_12, { gap: 4 }]}>
                <Text style={[fonts.fontSizes.headings.H6, fonts.lineHeight.headings.H6, fonts.SemiBold, { color: c.content.default.emphasis }]}>{name}</Text>
                <View style={[layout.row, layout.itemsCenter, { gap: 6 }]}>
                    <AKIcon source={UsersIcon} size={16} />
                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Regular, { color: c.content.default.subdued }]}>{members} members</Text>
                    {
                        newPosts && (
                            <View style={[layout.row, layout.itemsCenter, { gap: 6 }]}>
                                <View style={{width: 4, height: 4, borderRadius: 4, backgroundColor: c.background.medium.primary.default}} />
                                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Regular, { color: c.content.default.subdued }]}>{newPosts} new posts</Text>
                            </View>
                        )
                    }
                </View>
            </View>
            <AKIcon source={MoreIcon} onPress={onPressMore} />
        </View>
    )
}

export default GroupsListingItem;
