import { AKFastImage } from "@/components/atoms"
import { useTheme } from "@/theme"
import { colorTokens } from "@/theme/colorTokens"
import { User } from "@/types/models"
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers"
import { userSchema } from "@/types/schemas/user"
import { useNavigation } from "@react-navigation/native"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import { z } from "zod"

type Props = {
    item: z.infer<typeof socialConnectUserSchema>
    currentUser?: User
    onPress?: (item:  z.infer<typeof socialConnectUserSchema>) => void

}
function UserCell(props: Props) {
    // const [isFollowed, setIsFollowed] = useState(props.item?.is_followed ?? false)
    const { t } = useTranslation(['sideTabs'])
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const navigation = useNavigation()

    const c = colorTokens();
    
    function onItemPressed() {
        if (props.onPress != null) {
            // setIsFollowed(!isFollowed)
            props.onPress(props.item)
        }
    }
    return (
        <View style={[layout.row, layout.itemsCenter, gutters.marginVertical_12, layout.justifyBetween, gutters.marginHorizontal_12]}>
            <View style={[layout.row, layout.itemsCenter, layout.flex_1]}>
                <AKFastImage
                    uri={props.item?.profile_photo}
                    style={{height: 100, width: 100, borderRadius: 50}}
                />
                <Text numberOfLines={0} style={[gutters.marginLeft_12, gutters.marginRight_12, layout.flex_1]}>{`${props.item.first_name} ${props.item.last_name}`}</Text>
            </View>
            { props.currentUser?.id != props.item.id &&
                <TouchableOpacity onPress={onItemPressed}>
                    <View style={[gutters.marginHorizontal_12,layout.justifyCenter, layout.itemsCenter ,borders.rounded_4 ,{width: 120, height: 30, backgroundColor: props.item.is_followed ? c.content.default.default : colors.gray200}]}>
                        <Text style={[fonts.white]}>{props.item.is_followed ? t('sideTabs:Following'): t('sideTabs:Follow')}</Text>
                    </View>
                </TouchableOpacity>
            } 
        </View>
    )
}
export default UserCell