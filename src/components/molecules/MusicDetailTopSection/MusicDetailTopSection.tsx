import { ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme"
import { StyleSheet, Text, View } from "react-native"
import FastImage from "react-native-fast-image"
import LinearGradient from "react-native-linear-gradient"
import RoundButtonBG from "@/theme/assets/images/Home/RoundButtonBG.png"
import HeartFilled from "@/theme/assets/images/Home/HeartFilled.png"
import MessageIcon from "@/theme/assets/images/Home/MessageIcon.png"
import SaveIcon from "@/theme/assets/images/Home/SaveIcon.png"
type Props = { 
    image: string
    title?: string
    subtitle?: string
    isLikeSelected?: boolean
    isSaveSelected?: boolean
    onLikeButton?: () => void
    onCommentButton?: () => void
    onSaveButton?: () => void
}

function MusicDetailTopSection(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    return (
        <View>
            <View style={[layout.itemsCenter, gutters.marginHorizontal_0, gutters.marginTop_24]}>
                <LinearGradient style={styles.cardsongDetails} locations={[0,1,1,1]} colors={['#c7cdd1','rgba(255, 255, 255, 0)','rgba(255, 255, 255, 0)','rgba(199, 205, 209, 0.16)']} useAngle={true} angle={180}>
                    <FastImage 
                        resizeMode="cover"
                        source={{uri: props.image}}
                        style={[ borders.rounded_16, backgrounds.green50, gutters.marginHorizontal_16 ,{height: 210, width: 210} ]} />
                    <Text numberOfLines={1} style={[gutters.marginTop_16, fonts.Medium, {fontSize: 20,}]}>{props.title}</Text>
                    <Text style={[gutters.marginTop_16, fonts.Medium, {fontSize: 20}]}>{props.subtitle}</Text>
                </LinearGradient>
            </View>
            
        </View>
    )

}
const styles = StyleSheet.create({
    cardsongDetails: {
        shadowColor: "rgba(231, 231, 234, 0.4)",
        shadowOffset: {
        width: 0,
        height: 0
        },
        shadowRadius: 16,
        elevation: 16,
        shadowOpacity: 1,
        borderRadius: 32,
        borderStyle: "solid",
        borderColor: "#84bdc2",
        borderWidth: 2,
        width: 242,
        height: 322,
        // width: "100%",
        alignItems: "center",
        padding: 16,
        backgroundColor: "transparent"
        }

})
export default MusicDetailTopSection