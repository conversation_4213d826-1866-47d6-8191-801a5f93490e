import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { Text, TouchableOpacity, View } from "react-native"
import { z } from "zod"
import { useTheme } from "@/theme"

type Props = {
    item: z.infer<typeof socialConnectPostSchema>
    onProfileTapped?: (item:  z.infer<typeof socialConnectPostSchema>) => void
}

function SocialConnectTextCell(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    return(
        <View style={[layout.itemsCenter, layout.justifyCenter,{width: '100%', height: '100%'}]}>
            <Text style={[fonts.gray100]}>{props.item.text  ?? ''}</Text>
                <View style={[layout.absolute ,{bottom: 100, left:0, right: 0,}]}>
                <TouchableOpacity onPress={() => props.onProfileTapped !=null ? props.onProfileTapped(props.item) : () => {}}>

                    <Text style={[gutters.marginLeft_12,fonts.gray100, layout.flex_1,fonts.bold ]}>{`@${props.item?.user?.first_name} ${props.item?.user?.last_name}`}</Text>
                </TouchableOpacity>

                </View>
            
        </View>
    )
}
export default SocialConnectTextCell