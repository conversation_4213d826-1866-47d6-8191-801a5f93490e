import { Comment } from "@/types/models/comment"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { Alert, TextInput, TouchableOpacity, View, Keyboard, Text, ActivityIndicator } from "react-native"
import { z } from "zod"
import CommentCell from "../CommentCell/CommentCell"
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import addPostComment from "@/services/socialConnect/addPostComment"
import editPostComment from "@/services/socialConnect/editPostComment"
import deletePostComment from "@/services/socialConnect/deletePostComment"
import { socialConnectPostCommentSchema } from "@/types/schemas/socialConnectPostComment"
import { useTheme } from "@/theme"
import _Icon from 'react-native-vector-icons/FontAwesome';
import { ElementRef, useRef, useState } from "react"
import addPostCommentReply from "@/services/socialConnect/addPostCommentReply"
import deletePostCommentReply from "@/services/socialConnect/deletePostCommentReply"
import editPostCommentReply from "@/services/socialConnect/editPostCommentReply"
import getPostComments from "@/services/socialConnect/getPostComments"
import Spinner from "react-native-loading-spinner-overlay"

type Props = {
    item?: z.infer<typeof socialConnectPostSchema> | null
    isPost?: boolean
}

type EditMutationData = {
    data: {
        reply?: string;
        comment?: string;
    };
    commentId?: number;
    replyId?: number;
};

type DeleteMutationData = {
    commentId: number;
    replyId?: number;
}

type AddMutationData = {
    comment: string;
}

type ReplyMutationData = {
    commentId: number;
    payload: {
        reply: string;
    }
}

function SocialConnectCommentListing(props: Props) {
    const Icon = _Icon as React.ElementType
    const [commentText, setCommentText] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [currentItem, setCurrentItem] = useState(props.item)
    const [isEdit, setIsEdit] = useState(false)
    const [currentComment, setCurrentComment] = useState<Comment | null>(null)
    const queryClient = useQueryClient()
    const inputRef = useRef<ElementRef<typeof TextInput>>()
    const commentListingResponse = useQuery({
            queryKey: [`social-connect-posts-${props.item?.id}`],
            queryFn: () =>  getPostComments(props.item!.id)
        });
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    const addCommentMutation = useMutation(
        {
            mutationFn: (data: AddMutationData) => addPostComment(currentItem?.id, data),
            onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
                // newsDetailResponse.refetch()
                setIsLoading(false)
                setCommentText('')
                updateCachedData(response)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        },
    );
    const addReplyMutation = useMutation(
        {
            mutationFn: (data: ReplyMutationData) => addPostCommentReply(currentItem?.id, data.commentId ,data.payload),
            onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
                setIsLoading(false)
                updateCachedData(response)
            },
            onError: (error: any) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        },
    );
    const editCommentMutation = useMutation(
        {
            mutationFn: (data: EditMutationData) => {
                if (!!data.replyId) {
                    return editPostCommentReply(currentItem?.id, data.commentId, data.replyId, data.data)
                } else {
                    return editPostComment(currentItem?.id, data.commentId, data.data);
                }
            },
            onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
                setIsLoading(false)
                updateCachedData(response)

            },
            onError: (error: any) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        },
    );
    const deleteCommentMutation = useMutation(
        {
            mutationFn: (data: DeleteMutationData) => {
                if (!!data.replyId) {
                    return deletePostCommentReply(currentItem?.id, data.commentId, data.replyId);
                } else {
                    return deletePostComment(currentItem?.id, data.commentId);
                }
            },
            onSuccess: (response: z.infer<typeof socialConnectPostCommentSchema>) => {
                setTimeout(() => {
                    setIsLoading(false)
                }, 500)
                updateCachedData(response)
            },
            onError: (error: any) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        },
    );
    function updateCachedData(response: z.infer<typeof socialConnectPostCommentSchema> ) {
        setCurrentItem(response.post)
        if (props.isPost) {
            queryClient.setQueryData([`social-connect-posts-${response.post.id}`], () => {
                return response.post;
            })
        } else {
            queryClient.setQueryData([`social-connect-posts-${props.item?.id}`], (cacheData: any) => {
                if (cacheData != null) {
                    return {
                        ...cacheData,
                        comments: response.post.comments,
                        comments_count: response.post.comments_count
                    }
                } else {
                    return {
                        ...response.post
                    }
                }
                
            })
        }
        queryClient.setQueryData([`social-connect-post-${response.post.id}`], () => {
            return response.post;
        })
        updateCachedDataOnListing(response)
    }
    function updateCachedDataOnListing(response: z.infer<typeof socialConnectPostCommentSchema> ) {
        queryClient.setQueriesData(
            {
                predicate: (query) => ['social-connect-posts', 'social-connect-posts-following'].includes(query?.queryKey[0]),
            }, 
            (cacheData: any) => {
                if (cacheData !=null) {
                    let pages = cacheData?.pages?.map((page: any) => {
                        let pageData = page?.data?.map((post: z.infer<typeof socialConnectPostSchema>) => {
                            if (post.id === response.post.id) {
                                return response.post;
                            } else {
                                return post;
                            }
                        });
                        return {
                            ...page,
                            data: pageData,
                        };
                    });
                    return {
                        ...cacheData,
                        pages: pages,
                    };
                }
            
        });
    }
    function onCommentReply(commentId: number, reply: string) {
        let data: ReplyMutationData = {
            commentId,
            payload: {
                reply
            }
        }
        setIsLoading(true)
        addReplyMutation.mutate(data)
    }
    function onEditComment(item: Comment) {
        setIsEdit(true)
        setCommentText(item.comment)
        setCurrentComment(item)
        inputRef.current?.focus()
        
    }
    function onDeleteComment(item: Comment) {
        setIsLoading(true)
        let data: DeleteMutationData = {
            commentId: item.id,
            replyId: item.replyId,
        }
        deleteCommentMutation.mutate(data)
    }
    function onCommentSend(text?: string, isEditing?: boolean, comment?: Comment, replyRef?: any) {
        const updatedText = text ?? commentText;
        const updatedIsEdit = isEditing ?? isEdit;
        const updatedComment = comment ?? currentComment;
        if (updatedIsEdit == true) {
            const bodyKey = !!updatedComment?.replyId ? 'reply' : 'comment';
            let data: EditMutationData = {
                data: {
                    [bodyKey]: updatedText
                },
                commentId: updatedComment?.id,
                replyId: updatedComment?.replyId,
            }
            setIsLoading(true)
            setCommentText('')
            Keyboard.dismiss()
            editCommentMutation.mutate(data)
            replyRef?.current?.editComment(null);
        } else {
            let data: AddMutationData = {
                comment: updatedText
            }
            setIsLoading(true)
            addCommentMutation.mutate(data)
        }
        
        
    }
    function renderItem({item}: any) {
        let comment: Comment = item
        return (
            <View onStartShouldSetResponder={() => true}>
                <CommentCell 
                    comment={comment} 
                    onCommentReply={onCommentReply}
                    onEditComment={onEditComment}
                    onDeleteComment={onDeleteComment}
                    onCommentSend={onCommentSend}
                />
            </View>
            
        )
    }
    return (
        <View  style={[layout.flex_1]}>
            <Spinner
                visible={isLoading || commentListingResponse.isFetching}
            />
            <View style={[layout.justifyCenter, layout.itemsCenter, gutters.marginHorizontal_0, {height: 40}]}>
                <Text style={[fonts.bold]}>Comments</Text>
            </View>
            <View style={[layout.flex_1, layout.justifyCenter]}>
                {commentListingResponse?.data?.comments?.length > 0 && 
                    <KeyboardAwareFlatList 
                        showsVerticalScrollIndicator={false} 
                        contentContainerStyle={{ flexGrow: 1 }} 
                        data={commentListingResponse.data?.comments}
                        keyExtractor={
                            (_, index)=> index.toString()
                        }
                        renderItem={renderItem}
                        // keyboardShouldPersistTaps={'handled'}
                        
                    />
                }
                {currentItem?.comments?.length == 0 && 
                    <View style={[layout.justifyCenter, layout.itemsCenter, gutters.marginHorizontal_0]}>
                        <Text>No comments added</Text>
                    </View>
                }
                
                

            </View>
            
            <View style={[backgrounds.gray50,layout.row,gutters.marginBottom_0, gutters.marginHorizontal_0,layout.justifyBetween ,layout.itemsCenter ,gutters.marginTop_12,{height: 80}]}>
                <TextInput 
                    ref={inputRef}
                    placeholder="Add Comment..."
                    style={[gutters.marginHorizontal_12, layout.flex_1]}
                    placeholderTextColor={colors.gray100}
                    value={commentText}
                    onChangeText={(text) => setCommentText(text)}
                />
                <TouchableOpacity onPress={() => onCommentSend()} disabled={commentText == ''}>
                    <View>
                        <Icon style={[gutters.marginRight_16]} name='send' size={25} color={commentText == '' ? colors.gray200 : colors.green50}/>
                    </View>
                </TouchableOpacity>
                

            </View>
        </View>
    )

}
export default SocialConnectCommentListing