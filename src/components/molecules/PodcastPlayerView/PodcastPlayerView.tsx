import { AKFastImage, ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme";
import { Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import PlayIcon from "@/theme/assets/images/Home/PlayIcon.png"
import CrossIcon from "@/theme/assets/images/CrossIcon.png"
import { podcastSchema } from "@/types/schemas/podcastListing";
import { z } from "zod";
import { State } from "react-native-track-player";
import Pause from "@/theme/assets/images/Home/Pause.png"
type Props = {
    item?: z.infer<typeof podcastSchema> | null
    state?: State
    onPlayButton: (item?: z.infer<typeof podcastSchema> | null) => void
    onCrossButton: () => void
}
function PodcastPlayerView(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    return (
        <View style={[layout.row, layout.itemsCenter, gutters.marginLeft_16 ,layout.justifyBetween , layout.flex_1]}>
            <View style={[layout.row, layout.itemsCenter]}>
                <AKFastImage
                    uri={props.item?.thumbnail}
                    style={[{width: 42, height: 42}]}
                />
                <Text numberOfLines={2} style={[gutters.marginLeft_12, fonts.size_16, fonts.Medium, {maxWidth: 200}]}>{props.item?.title}</Text>
            </View>
            <View style={[layout.row, layout.itemsCenter]}>
                <TouchableOpacity onPress={() => props.onPlayButton(props.item)}>
                    <View style={[layout.justifyCenter ,{width: 50, height: 50}]}>
                        {props.state == State.Playing  && 
                        <ImageVariant
                            source={Pause}
                            style={ [ {height: 25, width: 25,}]}
                        />
                        }
                        {props.state != State.Playing  && 
                        <ImageVariant
                            source={PlayIcon}
                            style={ [ {height: 25, width: 25,}]}
                        />
                        }
                    </View>
                </TouchableOpacity>
                
                <TouchableOpacity onPress={props.onCrossButton}>
                    <View style={[layout.justifyCenter, {width: 50, height: 50}]}>
                        <ImageVariant
                            source={CrossIcon}
                            style={ [ {height: 25, width: 25,}]}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        </View>
    )

}
export default PodcastPlayerView