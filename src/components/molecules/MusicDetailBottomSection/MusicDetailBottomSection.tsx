import { ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme";
import Slider from "@react-native-community/slider"
import { Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import Next from "@/theme/assets/images/Home/Next.png"
import Previous from "@/theme/assets/images/Home/Previous.png"
import Pause from "@/theme/assets/images/Home/Pause.png"
import PlayIcon from "@/theme/assets/images/Home/PlayIcon.png"

import AkinaLogo from "@/theme/assets/images/AkinaLogo.png"
import { duration } from "moment";
import TrackPlayer, { State, usePlaybackState, Event ,useProgress, useTrackPlayerEvents } from "react-native-track-player";
import { secondToHHMMSS } from "@/utils/utility";
import { useEffect, useState } from "react";
import { z } from "zod";
import { musicSchema } from "@/types/schemas/musicListing";
type Props = {
    url?: string
    image?: string
    onPlay?: () => void
    onNext?: () => void
    onPrevious?: () => void
    onPositionChange?: (position: number) => void
    title?: string,
    subtitle?: string,
    hasNext: boolean,
    hasPrevious: boolean
    isPodcast?: boolean 
}
const events = [
    Event.PlaybackState,
    Event.PlaybackError,
];
function MusicDetailBottomSection(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    const playerState = usePlaybackState();
    const { position, duration } = useProgress()
    const [totalTime, setTotalTime]= useState(secondToHHMMSS(duration))
    const [timeElapsed, setTimeElapsed] = useState(secondToHHMMSS(position))
    useEffect(() => {
        setupPlayer()
    },[])
    useEffect(() => {
        setupPlayer()
    }, [props.url])
    useEffect(() => {
        setTotalTime(secondToHHMMSS(duration))
    },[ duration])
    useEffect(() => {
        setTimeElapsed(secondToHHMMSS(position))
    },[position])
    async function setupPlayer() {
        console.log("Setup Called")
        try {
            await TrackPlayer.setupPlayer()

        } catch (e) {

        }
        let track = {
            url: props.url,
            title: props.title,
            ...(!!props.image && {artwork: props.image})
        }
        await TrackPlayer.load(track)
    }
    useTrackPlayerEvents(events, (event) => {
        console.log("Track event")
        if (event.type === Event.PlaybackError) {
            console.warn('An error occured while playing the current track.');
        }
        if (event.type === Event.PlaybackState) {
            if (event.state == State.Ended) {
                TrackPlayer.seekTo(0.0)
            }
        }
    });
    function onPlayPauseButton() {
        if (playerState.state == State.Playing) {
            TrackPlayer.pause()
        } else {
            TrackPlayer.play()
        }
    }
    function onPositionChange(duration: number) {
        console.log("Position::::", duration)
        TrackPlayer.seekTo(duration)
    }
    return (
        <View>
            <View style={[layout.row, layout.justifyBetween,gutters.marginHorizontal_80]}>
                <Text style={[fonts.green50]}>{timeElapsed}</Text>
                <Text style={[fonts.green50]}>{totalTime}</Text>
            </View>
            <View>
                <Slider
                    style={[gutters.marginHorizontal_16]}

                    minimumValue={0}
                    maximumValue={1}
                    minimumTrackTintColor={'#007AFF'}
                    maximumTrackTintColor={colors.green50}
                    value={position/duration}
                    step={0.01}
                    onSlidingComplete={(value) => {onPositionChange(value* duration)}}
                />
            </View>
            <View style={[layout.row, gutters.marginHorizontal_16]}>
                <View style={[ {borderRadius: 10, overflow: 'hidden', width: 99, height: 61 }]}>
                    <View style={[layout.justifyEnd,{borderBottomLeftRadius: 70, overflow: 'hidden', height: 110, width: 99 ,marginTop: -49}]}>
                        <FastImage 
                            resizeMode="cover" 
                            source={{uri: props.image}}
                            style={[gutters.marginLeft_0  ,backgrounds.green50, gutters.marginHorizontal_16 ,{height: 61, width: 99} ]} />
                    </View>
                    <ImageVariant
                        source={AkinaLogo}
                        style={ [ {tintColor:  colors.black,height: 24, left: 5 ,width: 14, position: 'absolute', bottom:0}]}
                    />
                </View>
                <View style={[layout.flex_1, gutters.marginLeft_4 ,layout.justifyCenter]}>
                    <Text numberOfLines={3} style={[fonts.green50, fonts.Medium, {fontSize: 16}]}>{props.title}</Text>
                    <Text style={[fonts.Medium, {fontSize: 7}]}>{props.subtitle}</Text>
                </View>
                <View style={[layout.row,  ,layout.itemsCenter, layout.justifyAround, {height: 61}]}>
                    {props.isPodcast == null && 
                    <TouchableOpacity onPress={props.onPrevious} disabled={!props.hasPrevious}>
                        <ImageVariant
                            source={Previous}
                            style={ [ {tintColor: props.hasPrevious ? colors.black : colors.gray400,height: 40, width: 40,}]}
                        />
                    </TouchableOpacity>
                    }
                    <TouchableOpacity onPress={onPlayPauseButton} >
                        {playerState.state == State.Playing  && 
                        <ImageVariant
                            source={Pause}
                            style={ [ {height: 40, width: 40,}]}
                        />
                        }
                        {playerState.state != State.Playing  && 
                        <ImageVariant
                            source={PlayIcon}
                            style={ [ {height: 40, width: 40,}]}
                        />
                        }
                        
                    </TouchableOpacity>

                    {props.isPodcast == null &&
                    <TouchableOpacity onPress={props.onNext} disabled={!props.hasNext}>
                        <ImageVariant
                            source={Next}
                            style={ [ {tintColor:  props.hasNext ? colors.black : colors.gray400 ,height: 40, width: 40,}]}
                        />
                    </TouchableOpacity>
                    }

                    
                </View>
            </View>
        </View>
    )

}
export default MusicDetailBottomSection