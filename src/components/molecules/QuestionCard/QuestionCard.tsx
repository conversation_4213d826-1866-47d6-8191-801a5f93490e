import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import { ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AIChatIcon from '@/theme/assets/images/Home/AIChatIcon.png';
import { z } from "zod";
import { aiQuestion } from "@/types/schemas/aiQuestion";
import { CHAT_CONFIG } from '@/screens/SideTabs/Home/AkinaAI/ChatHome/ChatAI/Constants/ChatConstants';

type QuestionCardProps = {
    item: z.infer<typeof aiQuestion>;
    onPress: (question: z.infer<typeof aiQuestion>) => void;
};

export const QuestionCard: React.FC<QuestionCardProps> = ({ item, onPress }) => {
    const { gutters, fonts } = useTheme();
    const c = colorTokens();

    return (
        <TouchableOpacity onPress={() => onPress(item)}>
            <View style={[
                gutters.marginHorizontal_4, 
                {
                    width: CHAT_CONFIG.QUESTION_CARD_WIDTH, 
                    backgroundColor: c.background.default.neutrals.secondary, 
                    borderRadius: 16 
                }
            ]}>
                <ImageVariant 
                    source={AIChatIcon} 
                    style={[
                        gutters.marginTop_4, 
                        gutters.marginHorizontal_12,
                        { height: 20, width: 20, resizeMode: 'contain' }
                    ]}
                />
                <Text style={[
                    fonts.fontSizes.body.xs, 
                    gutters.marginHorizontal_16, 
                    fonts.lineHeight.body.xs,
                    gutters.marginTop_4, 
                    gutters.marginBottom_16, 
                    fonts.Medium, { color: c.content.default.default }
                ]}>
                    {item.message}
                </Text>
            </View>
        </TouchableOpacity>
    );
};
