import { useTheme } from "@/theme"
import { Text, TouchableOpacity, View } from "react-native"
import { z } from "zod"
import { useTranslation } from "react-i18next";
import { Dispatch, SetStateAction, useState } from "react";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import ChevronDown from "@/theme/assets/images/SocialConnect/ChevronDown.png";
import ChevronUp from "@/theme/assets/images/SocialConnect/ChevronUp.png";
import SocialConnectPostMessage from "../SocialConnectPostMessage/SocialConnectPostMessage";
import { commentSchema } from "@/types/schemas/postDetail";
import { Comment } from "@/types/models/comment";

type Props = {
    comment: z.infer<typeof commentSchema>;
    displayPopoverBackdrop: boolean;
    onPressComment: (data?: any) => void;
    setReplyingComment: Dispatch<SetStateAction<Comment | null>>;
    onDeleteComment: (item: Comment) => void;
    onEditComment: (item: Comment) => void;
}

function SocialConnectPostComment({ comment, setReplyingComment, onDeleteComment, onEditComment, onPressComment, onReportComment, displayPopoverBackdrop }: Props) {
    const { t } = useTranslation(['home']);

    const [isRepliesVisible, setIsRepliesVisible] = useState(false);

    const {
        layout,
        fonts,
        gutters,
    } = useTheme();

    const c = colorTokens();

    const totalReplies = comment.replies?.length || 0;

    function onPressViewMore() {
        setIsRepliesVisible(prev => !prev);
    }

    return (
        <View style={[gutters.marginTop_12]}>
            <SocialConnectPostMessage
                key={comment.id}
                message={comment}
                commentId={comment.id}
                setReplyingComment={setReplyingComment}
                onDeleteComment={onDeleteComment}
                onEditComment={onEditComment}
                onPressComment={onPressComment}
                onReportComment={onReportComment}
                displayPopoverBackdrop={displayPopoverBackdrop}
                isReply={false}
            />
            {
                !!totalReplies && !isRepliesVisible && (
                    <TouchableOpacity onPress={onPressViewMore} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_6, { marginLeft: 44 + 12}]}>
                        <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, {color: c.content.default.default}]}>{`View ${totalReplies} more repl${totalReplies == 1 ? 'y' : 'ies'}`}</Text>
                        <AKIcon source={ChevronDown} styles={[gutters.marginLeft_4]} size={16} />
                    </TouchableOpacity>
                )
            }
            {
                isRepliesVisible && (
                    <View>
                        {
                            comment?.replies?.map(reply => (
                                <SocialConnectPostMessage
                                    key={reply.id}
                                    commentId={comment.id}
                                    message={reply} 
                                    isReply
                                    onDeleteComment={onDeleteComment}
                                    onEditComment={onEditComment}
                                    onPressComment={onPressComment}
                                    onReportComment={onReportComment}
                                    displayPopoverBackdrop={displayPopoverBackdrop}
                                />
                            ))
                        }
                    </View>
                )
            }
            {
                !!totalReplies && isRepliesVisible && (
                    <TouchableOpacity onPress={onPressViewMore} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_6, { marginLeft: 44 + 12}]}>
                        <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, {color: c.content.default.default}]}>Hide Replies</Text>
                        <AKIcon source={ChevronUp} styles={[gutters.marginLeft_4]} size={16} />
                    </TouchableOpacity>
                )
            }
        </View>
    )

}

export default SocialConnectPostComment;
