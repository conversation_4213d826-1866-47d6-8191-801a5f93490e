import { useCallback, useRef, useState } from "react";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { View } from "react-native"
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import SocialConnectPost from "@/components/molecules/SocialConnectPost/SocialConnectPost";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import SocialConnectPostCommentsSheet from "@/components/molecules/SocialConnectPostCommentsSheet/SocialConnectPostCommentsSheet";
import SocialConnectPostSettingsBottomSheet from "@/components/molecules/SocialConnectPostSettingsBottomSheet/SocialConnectPostSettingsBottomSheet";
import { usePremiumMemberPopup } from "@/hooks/usePremiumMemberPopup";
import { getProfile } from "@/services/users";
import { UserTypes } from "@/utils/constants";
import { useLikeBookmarkToggleMutation } from "@/screens/SideTabs/Home/SocialConnect/hooks/useLikeBookmarkToggleMutation";
import Animated, { useAnimatedScrollHandler } from "react-native-reanimated";
import basicProfile from "@/services/users/basicProfile";
import ReportPostBottomSheet from "../ReportPostBottomSheet/ReportPostBottomSheet";

export type HomeScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'BlogListing',
    'EmpowerHerListing'
>;

type Props = {
    ListHeaderComponent: () => JSX.Element;
    onScroll: ReturnType<typeof useAnimatedScrollHandler>;
    data: z.infer<typeof socialConnectPostSchema>[];
    ListEmptyComponent: () => JSX.Element;
    ListFooterComponent: () => JSX.Element | null;
    loadNext: () => void;
    refetchProfile?: () => void
}

function SocialConnectFeed({ ListHeaderComponent, onScroll, data, ListEmptyComponent, ListFooterComponent, loadNext, refetchProfile }: Props) {
    const queryClient = useQueryClient();

    const {
        layout,
    } = useTheme();
    
    const { t } = useTranslation(['home']);
    const tokenColors = colorTokens()

    const { onLikeButtonTapped, MUTATION_TYPE } = useLikeBookmarkToggleMutation();

    const showPremiumMemberPopup = usePremiumMemberPopup();

    const [visiblePostId, setVisiblePostId] = useState(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isSettingSheetVisible, setIsSettingSheetVisible] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [selectedPost, setSelectedPost] = useState(null);
    const [selectedCommentItem, setSelectedCommentItem] = useState<z.infer<typeof socialConnectPostSchema> | null>(null)
    const [reportingContent, setReportingContent] = useState(null);

    const profileResponse = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile
    });

    function ItemSeparatorComponent() {
        return (
            <View style={[{ height: 2, backgroundColor: tokenColors.stoke.default.subdued }]} />
        )
    }
    
    const viewabilityConfig = {
        itemVisiblePercentThreshold: 80,
    };
    
    const onViewableItemsChanged = useCallback(({ viewableItems }) => {
        if (viewableItems.length > 0) {
            setVisiblePostId(viewableItems[0].item.id);
        } else {
            setVisiblePostId(null);
        }
    }, []);

    const viewabilityConfigCallbackPairs = useRef([
        { viewabilityConfig, onViewableItemsChanged },
    ]);

    function renderItem({ item }: any) {
        return (
            <SocialConnectPost
                item={item}
                isVisible={visiblePostId === item.id}
                onCommentButtonTapped={onCommentButtonTapped}
                onLikeButton={onLikeButton}
                onBookmarkButton={onBookmarkButton}
                onPressPostSettings={onPressPostSettings}
            />
        )
    }

    function onLikeButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        if (profileResponse.data?.user.user_type == UserTypes.FREE) {
            showPremiumMemberPopup()
        } else {
            onLikeButtonTapped(tappedItem)
        }
    }

    function onBookmarkButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        if (profileResponse.data?.user.user_type == UserTypes.FREE) {
            showPremiumMemberPopup()
        } else {
            onLikeButtonTapped(tappedItem, MUTATION_TYPE.BOOKMARK)
        }
    }

    function onCommentButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema> ) {
        setSelectedCommentItem(tappedItem);
        setIsModalVisible(true);
    }

    function onPressPostSettings(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        setSelectedPost(tappedItem);
        setIsSettingSheetVisible(true);
    }
    
    function removeUsersData(userId?: number) {
        if (refetchProfile != null) {
            refetchProfile()
        }
        queryClient.setQueriesData(
            {
                predicate: (query) => ['social-connect-posts-following', 'social-connect-posts', `social-connect-user-posts-${userId}`].includes(query?.queryKey[0] as string),
            }, 
            (cacheData: any) => {
                if (cacheData !=null) {
                    let pages = cacheData?.pages?.map((page: any) => {
                        let pageData = page.data.filter((post: z.infer<typeof socialConnectPostSchema>) => post?.user?.id != userId )
                        
                        if (pageData != null) {
                            return {
                                ...page,
                                data: pageData,
                            };
                        }
                        
                    });
                    let newPages = pages.filter((page) => page.data.length !=0)
                    return {
                        ...cacheData,
                        pages: newPages,
                    };
                }
            
        });
    }

    function onPressReport() {
        setTimeout(() => {
            setReportingContent({
                postID: selectedPost?.id,
                userID: selectedPost?.user?.id,
            });
        }, 750)
    }

    return (
        <View style={[layout.flex_1]}>
            <Animated.FlatList
                showsVerticalScrollIndicator={false}
                data={data}
                contentContainerStyle={[{ flexGrow: 1 }]} 
                keyExtractor={(_, index) => index.toString()}
                renderItem={renderItem}
                ListHeaderComponent={ListHeaderComponent()}
                ListEmptyComponent={ListEmptyComponent}
                ListFooterComponent={ListFooterComponent}
                ItemSeparatorComponent={ItemSeparatorComponent}
                onScroll={onScroll}
                scrollEventThrottle={16}
                viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
                bounces={false}
                onEndReached={loadNext}
                onEndReachedThreshold={0.4}
            />
            <SocialConnectPostCommentsSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                item={selectedCommentItem}
            />
            <SocialConnectPostSettingsBottomSheet
                isModalVisible={isSettingSheetVisible}
                setIsModalVisible={setIsSettingSheetVisible}
                shouldUpdate={(userId) => removeUsersData(userId)}
                item={selectedPost}
                setActionStatus={setIsDeleting}
                isOwnPost={profileResponse?.data?.user?.id === selectedPost?.user?.id}
                onPressReport={onPressReport}
            />
            <ReportPostBottomSheet
                isModalVisible={!!reportingContent}
                setReportingContent={setReportingContent}
                reportingContent={reportingContent}
            />
        </View>
    )
}

export default SocialConnectFeed