import { useTheme } from "@/theme"
import { Alert, Dimensions, Text, TouchableOpacity, View, findNodeHandle, UIManager, Platform, ImageSourcePropType, Image, } from "react-native"
import FastImage from "react-native-fast-image"
import _Icon from 'react-native-vector-icons/AntDesign';
import AKIcon from "@/components/atoms/AKIcon/AKIcon"
import UsersIcon from "@/theme/assets/images/UsersIcon.png";
import NewsIcon from "@/theme/assets/images/NewsIcon.png";
import UserIcon from "@/theme/assets/images/SocialConnect/UserIcon.png";
import CircleCutIcon from "@/theme/assets/images/SocialConnect/CircleCutIcon.png";
import { colorTokens } from "@/theme/colorTokens"
import ProfileActionButton from "@/components/atoms/ProfileActionButton/ProfileActionButton"
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import EditIcon from "@/theme/assets/images/SocialConnect/EditIcon.png";
import { useNavigation } from "@react-navigation/native";
import { launchCamera, launchImageLibrary, Asset } from "react-native-image-picker";
import { useActionSheet } from "@expo/react-native-action-sheet";
import { useTranslation } from "react-i18next";
import updateProfile from "@/services/users/updateProfile";
import followUnfollowUser from "@/services/socialConnect/users/followUnfollowUser";
import Spinner from "react-native-loading-spinner-overlay";
import BlockBottomSheet from "../BlockBottomSheet/BlockBottomSheet";
import SocialConnectUserPictureUpdatorBottomSheet from "../SocialConnectUserPictureUpdatorBottomSheet/SocialConnectUserPictureUpdatorBottomSheet";
import { z } from "zod";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { AKFastImage } from "@/components/atoms";

type Props = {
    profileInfo: any
    postsCount: number;
    onMeasured: (yPos: number) => void;
    isOwnProfile: boolean;
    profileRefetch: () => {};
    userId: number;
}

type ActionButtonItem = {
    text: string;
    onPress: () => void;
    icon?: ImageSourcePropType;
};

export const EditButtonType = {
    Profile: 'Profile',
    Cover: 'Cover'
}
type RemoveMemberAlertOption = {
    text: string;
    onPress?: () => void | Promise<void>;
    style?: 'default' | 'cancel' | 'destructive';
};

function SocialConnectProfileHeader(props: Props) {

    const { height } = Dimensions.get("screen");
    const c = colorTokens();

    const { t } = useTranslation(['account']);
    const queryClient = useQueryClient();

    const navigation = useNavigation();
    const { showActionSheetWithOptions } = useActionSheet();
    const [isBlockModalVisible, setIsBlockModalVisible] = useState(false)
    const [isModalVisible, setIsModalVisible] = useState(false)
    const [pictureType, setPictureType] = useState('')

    const { profileInfo, postsCount, onMeasured, isOwnProfile, profileRefetch, userId } = props;
    const { id, first_name: firstName, last_name: lastName, followers_count: followersCount, 
        is_followed: isFollowed, profile_photo: profilePhoto,cover_photo: coverPhoto , profile } = profileInfo || {};
    
    const {
        layout,
        gutters,
        fonts,
        borders
    } = useTheme();

    const trackedViewRef = useRef(null);
    const [isLoading, setIsLoading] = useState(false)

    useEffect(() => {
        setTimeout(() => {
        const handle = findNodeHandle(trackedViewRef.current);
        if (handle) {
            UIManager.measure(handle, (x, y, width, height, pageX, pageY) => {
                console.log(x, y, width, height, pageX, pageY)
                onMeasured(pageY);
            });
        }
        }, 500);
    }, []);

    function onEditPress(type: string) {
        setPictureType(type)
        setIsModalVisible(true);
    }

    function getEditButton(isDisabled: boolean, type: string ) {
        return (
            <TouchableOpacity disabled={isDisabled} onPress={() => onEditPress(type)} style={[gutters.padding_4, borders.rounded_4, { backgroundColor: c.fill.default.white.rest }]}>
                <AKIcon source={EditIcon} />
            </TouchableOpacity>
        )
    }

    const unFollowMutation = useMutation(
        {
            mutationFn: (data: any) => followUnfollowUser(data),
            onSuccess: (response) => {
                profileRefetch()
                // removeUsersDataFromFeed(profileInfo.id)
                setIsLoading(false)
                queryClient.setQueryData([`${userId}-followings`],
                    (cacheData: any) => {
                    if (cacheData != null) {
                        let pages = cacheData?.pages?.map((page: any) => {
                            let originalLength = page.data.length
                            let pageData = page?.data?.filter((user: any) => user.id !== id);
                            return {
                                ...page,
                                data: pageData,
                                total: page.total - (originalLength - pageData.length)

                            };
                        });
                        return {
                            ...cacheData,
                            pages: pages,
                        };
                    }
                });
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        }
    );
    const followMutation = useMutation(
    {
        mutationFn: (data: any) => followUnfollowUser(data),
        onSuccess: (response) => {
            profileRefetch()
            queryClient.invalidateQueries([`${userId}-followings`])
            setIsLoading(false)
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert("Error!", error.message)
        }
    });

    function onPressRemoveMember(): void {
        let options: RemoveMemberAlertOption[] = [
            {
                text: 'Cancel',
            },
            {
                text: 'Remove',
                onPress: async () => {
                    const apiData = {
                        user_id: id
                    };
                    console.log(apiData)
                    setIsLoading(true)
                    unFollowMutation.mutate(apiData);
                },
                style: 'destructive',
            },
        ]
        Alert.alert('Remove this member?', 'You won’t see their updates or shared activity anymore.', options)
    }

    const ownProfileActionButtons: ActionButtonItem[] = [
        {
            text: "View Members",
            onPress: () => navigation.navigate('Members', { userId, isOwnProfile }),
        },
        {
            text: "Edit Profile",
            onPress: () => {navigation.navigate('AccountSettings', { tabIndex: 1 });},
        },
    ];

    const memberProfileActionButtons: ActionButtonItem[] = [
        {
            text: "Block",
            onPress: () => {setIsBlockModalVisible(true)},
            icon: CircleCutIcon,
        },
        {
            text: "Remove",
            onPress: onPressRemoveMember,
            icon: UserIcon,
        },
    ]
    

    const nonMemberProfileActionButtons: ActionButtonItem[] = [
        {
            text: "Send Request",
            onPress: () => {
                const apiData = {
                    user_id: id
                };
                setIsLoading(true)
                followMutation.mutate(apiData);
            },
        }
    ]

    function onUserBlockedSuccessfully() {
        removeUsersDataFromFeed(profileInfo.id)
        navigation.goBack()
    }
    function removeUsersDataFromFeed(userId?: number) {
        queryClient.setQueriesData(
            {
                predicate: (query) => ['social-connect-posts-following', 'social-connect-posts'].includes(query?.queryKey[0] as string),
            }, 
            (cacheData: any) => {
                if (cacheData !=null) {
                    let pages = cacheData?.pages?.map((page: any) => {
                        let pageData = page.data.filter((post: z.infer<typeof socialConnectPostSchema>) => post?.user?.id != userId )
                        
                        if (pageData != null) {
                            return {
                                ...page,
                                data: pageData,
                            };
                        }
                        
                    });
                    let newPages = pages.filter((page) => page.data.length !=0)
                    return {
                        ...cacheData,
                        pages: newPages,
                    };
                }
            
        });
    }

    function onFollowersPressed() {
        navigation.push('Members', { userId: id, isOwnProfile });
    }

    let actionButtons: ActionButtonItem[] = [];
    if (isOwnProfile) {
        actionButtons = ownProfileActionButtons;
    } else {
        actionButtons = isFollowed ? memberProfileActionButtons : nonMemberProfileActionButtons;
    }

    const userName = `${firstName} ${lastName}`;
    
    return (
        <View style={[gutters.paddingBottom_20, { borderBottomWidth: 1, borderBottomColor: c.stoke.default.default, backgroundColor: 'white' }]}>
            <Spinner
                visible={isLoading}
            />
            <View>
                <AKFastImage
                    placeholder={require('@/theme/assets/images/CoverPlaceholder.png')}
                    uri={coverPhoto}
                    resizeMode={FastImage.resizeMode.cover}
                    style={{height: height * 0.27, width: '100%'}}
                />
                {
                    isOwnProfile && (
                        <View style={[layout.absolute, { bottom: 16, right: 16 }]}>
                            {getEditButton(false, EditButtonType.Cover)}
                        </View>
                    )
                }
            </View>
            <View ref={trackedViewRef} style={[gutters.marginLeft_16, { marginTop: -45, alignSelf: 'flex-start', shadowColor: '#000',
                        shadowOffset: {
                            width: 1,
                            height: 1,
                        },
                        shadowOpacity: 0.3,
                        shadowRadius: 3 }]}>
                <AKFastImage
                    uri={profilePhoto}
                    style={{
                        height: 90, width: 90, borderRadius: 75, backgroundColor: 'black', elevation: 5,
                    }}
                />
                {
                    isOwnProfile && (
                        <View style={[layout.absolute, { bottom: -4, right: -4, elevation: 10 }]}>
                            {getEditButton(false, EditButtonType.Profile)}
                        </View>
                    )
                }
            </View>
            {
                !!profileInfo && (
                    <View style={[gutters.paddingHorizontal_16, gutters.marginTop_12]}>
                        <Text style={[fonts.fontSizes.headings.H5, fonts.lineHeight.headings.H5, fonts.Bold, gutters.marginBottom_8, { color: c.content.default.emphasis }]}>{userName}</Text>
                        <View style={[layout.row, gutters.marginBottom_12, { gap: 8 }]}>
                            <TouchableOpacity disabled={isOwnProfile} onPress={onFollowersPressed} style={[layout.row, layout.itemsCenter, { gap: 4}]}>
                                <AKIcon source={UsersIcon} size={16} />
                                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Medium, { color: c.content.default.subdued }]}>{`${followersCount} Followers${followersCount > 1 ? '' : ''}`}</Text>
                            </TouchableOpacity>
                            <View style={[layout.row, layout.itemsCenter, { gap: 4}]}>
                                <AKIcon source={NewsIcon} size={16} />
                                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Medium, { color: c.content.default.subdued }]}>{`${postsCount} Post${postsCount > 1 ? 's' : ''}`}</Text>
                            </View>
                        </View>
                        {
                            !!profile?.bio && (
                                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, gutters.marginBottom_16, { color: c.content.default.default }]}>{profile.bio}</Text>
                            )
                        }
                        <View style={[layout.row, { gap: 12 }]}>
                            {
                                actionButtons.map((actionButton, index) => (
                                    <ProfileActionButton
                                        key={index}
                                        text={actionButton.text}
                                        icon={actionButton.icon}
                                        onPress={actionButton.onPress}
                                    />
                                ))
                            }
                        </View>
                    </View>
                )
            }
            <BlockBottomSheet isModalVisible={isBlockModalVisible} currentUserId={userId} setIsModalVisible={setIsBlockModalVisible} item={profileInfo} onUserBlocked={onUserBlockedSuccessfully}/>
            <SocialConnectUserPictureUpdatorBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} type={pictureType} successCallback={profileRefetch}/>
        </View>
    )
}
export default SocialConnectProfileHeader;
