import { ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme";
import { Text, TouchableOpacity, View } from "react-native"
import DPGroup from "@/theme/assets/images/DPGroup.png"
import { colorTokens } from "@/theme/colorTokens";
import { useTranslation } from "react-i18next";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import ArrowRightIcon from "@/theme/assets/images/SocialConnect/ArrowRightIcon.png";
import InviteMemberBottomSheet from "../InviteMemberBottomSheet/InviteMemberBottomSheet";
import { useState } from "react";
import { useNavigation } from "@react-navigation/native";

function EmptyPostView() {
    const {
        layout,
        gutters,
        fonts,
    } = useTheme();
    const tokenColors= colorTokens()
    const { t } = useTranslation(['home']);
    const navigation = useNavigation()
    const [isModalVisible, setIsModalVisible] = useState(false);

    function onInviteMemberTapped(): void {
        navigation.navigate('Search')
        // setIsModalVisible(true);
    }
    
    return (
        <View>
            <View style={[layout.absolute, layout.z10 ,{right: 22, top: 14}]}>
                <ImageVariant  source={DPGroup} style={{height: 75, width: 73, resizeMode: 'contain'}}/>
            </View>
            <View style={[gutters.marginHorizontal_12, gutters.marginTop_24,{backgroundColor: tokenColors.background.default.primary.default, height: 152, borderRadius: 12}]}>
                <View style={[gutters.marginLeft_12, gutters.marginTop_24]}>
                    <Text style={[fonts.fontSizes.headings.H4, fonts.Bold, fonts.lineHeight.headings.H4, { color: tokenColors.content.default.emphasis}]}>{t("home:AddMembers")}</Text>
                    <Text style={[gutters.marginRight_40, gutters.marginTop_8 ,fonts.fontSizes.body.xs, fonts.body, fonts.lineHeight.body.xs, {color: tokenColors.content.default.default, letterSpacing: 0.25}]}>{t("home:AddMemberDesc")}</Text>
                </View >
                <TouchableOpacity onPress={onInviteMemberTapped}>
                    <View style={[gutters.marginLeft_12, gutters.paddingHorizontal_12, gutters.marginTop_16 ,layout.itemsCenter, layout.justifyCenter, layout.row, {alignSelf: 'flex-start' ,height: 32, backgroundColor: tokenColors.fill.bold.neutrals.rest, borderRadius: 8, gap: 4}]}>
                        <Text style={[ fonts.fontSizes.utility.xs, fonts.SemiBold, fonts.lineHeight.utility.xs, { color: tokenColors.content.onBold.default.default, letterSpacing: 0.25}]}>{t("home:AddMember")}</Text>
                        <AKIcon size={18} tintColor={tokenColors.content.onBold.default.default} source={ArrowRightIcon} />
                    </View>
                </TouchableOpacity>
            </View>
            <InviteMemberBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} />
        </View>
    )
}

export default EmptyPostView