import { useTheme } from "@/theme";
import { notificationSchema } from "@/types/schemas/notificationSchema";
import { useQueryClient } from "@tanstack/react-query";
import { Linking, Text, TouchableOpacity, View } from "react-native";
import { z } from "zod";
import _Icon from 'react-native-vector-icons/MaterialIcons';
import { formatNotificationDate, getDeeplinkingScheme, isEmptyObjectOrArray } from "@/utils/utility";
import FastImage from "react-native-fast-image";
import DefaultProfileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png"
import { AKFastImage } from "@/components/atoms";
import { colorTokens } from "@/theme/colorTokens";
type Props = {
    item: z.infer<typeof notificationSchema>;
}

function NotificationItem({ item }: Props) {
    const queryClient = useQueryClient();

    const {
        layout,
        gutters,
        colors,
        fonts
    } = useTheme();

    const c = colorTokens();

    function onPress() {
        let data = item?.data;
        if (typeof data == 'string') {
            data = JSON.parse(data)
        }
        if (isEmptyObjectOrArray(data)){
            return;
        }
        let url = data?.url;
        if (data.comment_id != null) {
            url = url + '/true'
        }
        if (url) {
            Linking.openURL(getDeeplinkingScheme(url));
        }
        queryClient.setQueryData(['notifications'], (cacheData: any) => {
            return {
                ...cacheData,
                pages: cacheData?.pages?.map(page => ({
                        ...page,
                        data: page?.data?.map(item =>
                            item.id === item?.id ? {
                                ...item,
                                is_read: true 
                            } : item
                        )
                    })
                ),
            };
        })
    }

    return (
        <TouchableOpacity onPress={onPress} style={[layout.row, gutters.marginBottom_12]}>
            <AKFastImage
                uri={null}
                placeholder={DefaultProfileIcon}
                resizeMode={FastImage.resizeMode.contain}
                style={[{width: 40, height: 40, borderRadius: 50,  backgroundColor: colors.black}]}
            />
            <View style={[layout.flex_1, gutters.marginLeft_8, gutters.marginRight_12, {gap: 4}]}>
                <Text style={[fonts.fontSizes.body.sm, fonts.Medium, fonts.lineHeight.body.sm, {color: c.content.default.default}]}>{item.title}</Text>
                {/* <Text style={[gutters.marginBottom_8]}>{item.body}</Text> */}
                <Text style={[fonts.gray400]}>{formatNotificationDate(item.created_at)}</Text>
            </View>
            
        </TouchableOpacity>
    );
}

export default NotificationItem;
