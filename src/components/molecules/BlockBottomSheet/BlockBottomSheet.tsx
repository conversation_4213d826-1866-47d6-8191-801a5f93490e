import { Alert, Text, TouchableOpacity, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import BulletText from "@/components/atoms/BulletText/BulletText";
import { BLOCK_MEMBER_INSTRUCTIONS } from "@/utils/constants";
import FastImage from "react-native-fast-image";
import { z } from "zod"
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";
import { Query, useMutation, useQueryClient } from "@tanstack/react-query";
import blockUser from "@/services/socialConnect/users/blockUser";
import Spinner from "react-native-loading-spinner-overlay";
import { useState } from "react";
import { useCustomToast } from "@/hooks/useCustomToast";
import DefaultprofileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { profileResponseSchema } from "@/types/schemas/profile";
import AKBottomSheet from "@/components/atoms/AKBottomSheet/AKBottomSheet";
import { AKFastImage } from "@/components/atoms";

type Props = {
    isModalVisible: any;
    setIsModalVisible: (isVisible: boolean) => void;
    item: z.infer<typeof socialConnectUserSchema>;
    selectedTab?: string
    onUserBlocked?: () => void
    currentUserId?: number
}


function BlockBottomSheet(props: Props) {

    const { isModalVisible, setIsModalVisible, item, selectedTab } = props;
    const queryClient = useQueryClient();

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
        borders,
    } = useTheme();

    const c = colorTokens();
    const [isLoading, setIsLoading] = useState(false)
    const showToast = useCustomToast();
    
    function closeSheet(): void {
        setIsModalVisible(false);
    }

    function onPress(): void {
        setIsLoading(true)
        blockUserMutation.mutate(item.id)
    }

    function removeCacheDataFromMembers() {
        queryClient.setQueriesData(
            {
                predicate: (query) => [`${props.currentUserId}-followings`, `${props.currentUserId}-followers`].includes(query?.queryKey[0] as string),
            }, 
            (cacheData: any) => {
                if (cacheData != null) {
                    let pages = cacheData?.pages?.map((page: any) => {
                        let originalLength = page.data.length
                        let pageData = page?.data?.filter((user: any) => user.id !== item?.id);
                        return {
                            ...page,
                            data: pageData,
                            total: page.total - (originalLength - pageData.length)
                        };
                    });
                    let newPages = pages.filter((page) => page.data?.length !=0)

                    return {
                        ...cacheData,
                        pages: newPages,
                    };
                }
        });
        if (selectedTab == 'Followers') {
            decrementFollowersCount()
        }
    }
    function decrementFollowersCount(){
        queryClient.setQueryData([`get_user_profile${props.currentUserId}`], 
            (cacheData: z.infer<typeof profileResponseSchema>) => {
                if (cacheData != null) {
                    return {
                        ...cacheData,
                        user: {
                            ...cacheData.user,
                            followers_count:(cacheData.user.followers_count ?? 1 ) - 1

                        }
                    }
                }
            })
    }
    const blockUserMutation = useMutation(
        {
            mutationFn: (id: number) => blockUser(id),
            onSuccess: (response) => {
                setIsLoading(false)
                showToast('Member Blocked Successfully');
                removeCacheDataFromMembers()
                if (props.onUserBlocked != null) {
                    props.onUserBlocked()
                }
                closeSheet();

            },
            onError: (error) => {
                setIsLoading(false)
                closeSheet();
                Alert.alert("Error!", error.message)
            }
        }
    );
    

    const userName = `${item?.first_name} ${item?.last_name}`;

    return (
        <AKBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible}>
            <Spinner
                visible={isLoading}
            />
            <View style={[gutters.paddingHorizontal_16]}>
                <View style={[backgrounds.black, gutters.marginTop_12, gutters.marginBottom_16, { borderRadius: 24, height: 5, width: 48, alignSelf: 'center' }]} />
                <View>
                    <AKFastImage
                        uri={item?.profile_photo}
                        placeholder={DefaultprofileIcon}
                        resizeMode={FastImage.resizeMode.contain}
                        style={{
                            height: 88, width: 88, borderRadius: 88, backgroundColor: 'black', alignSelf: 'center'
                        }}
                    />
                    <Text style={[fonts.fontSizes.headings.H5, fonts.lineHeight.headings.H5, fonts.Bold, gutters.marginTop_12, gutters.marginBottom_8, {color: c.content.default.emphasis}]}>{`Block ${userName}`}</Text>
                    {
                        BLOCK_MEMBER_INSTRUCTIONS.map((inst, index) => (
                            <BulletText key={index} text={inst} />
                        ))
                    }
                    <TouchableOpacity onPress={onPress} style={[borders.rounded_8, gutters.paddingVertical_12, gutters.marginTop_12, layout.itemsCenter, layout.justifyCenter, { height: 48, backgroundColor: 'black' }]}>
                        <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, { color: c.content.onBold.default.default }]}>Block</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </AKBottomSheet>
    )
}

export default BlockBottomSheet;


