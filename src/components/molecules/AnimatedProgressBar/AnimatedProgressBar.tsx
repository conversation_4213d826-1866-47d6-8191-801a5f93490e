import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { Text, View } from "react-native"
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import CheckCircleFilledIcon from "@/theme/assets/images/SocialConnect/CheckCircleFilledIcon.png";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";

import RNAnimated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

type Props = {
    uploadProgress: number;
    isSuccess: boolean;
    isEditing: boolean;
}

function AnimatedProgressBar({ uploadProgress, isSuccess, isEditing }: Props) {    
    const { t } = useTranslation(['home']);
    const tokenColors = colorTokens()
    const {
        fonts,
        gutters,
        layout,
    } = useTheme();

    const progressBarWidth = useSharedValue(0);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            width: `${progressBarWidth.value * 100}%`,
        };
    });

    useEffect(() => {
        progressBarWidth.value = withTiming(uploadProgress, { duration: 500 });
    }, [uploadProgress])

    let text = isEditing ? 'Saving Changes...' : 'Uploading...';
    if (isSuccess) {
        text = 'Done';
    }
    
    return uploadProgress > 0 ? (
        (
            <RNAnimated.View style={[{ width: '100%' }]}>
                <View style={[gutters.marginLeft_16, layout.row, layout.itemsCenter, gutters.marginBottom_4]}>
                    {
                        isSuccess && (
                            <AKIcon source={CheckCircleFilledIcon} size={20} styles={[{marginRight: 6}]} />
                        )
                    }
                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Medium, {color: tokenColors.content.default.emphasis}]}>{text}</Text>
                </View>
                <RNAnimated.View style={[animatedStyle, { height: 3, backgroundColor: tokenColors.stoke.primary.default  }]} />
            </RNAnimated.View>
        )
    ) : null
}

export default AnimatedProgressBar;
