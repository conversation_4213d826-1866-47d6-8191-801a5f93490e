import React, { ReactNode, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { GestureHandlerRootView, TapGestureHandler } from "react-native-gesture-handler";
import { useTheme } from "@/theme"
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";
import _Icon from "react-native-vector-icons/FontAwesome";

const Icon = _Icon as React.ElementType;

type Props = {
	children: ReactNode;
	onDoubleTap: () => void;
	isLiked: boolean
}

const AnimatingHeart = ({ children, onDoubleTap, isLiked }: Props) => {
	const [isAnimating, setIsAnimating] = useState(false);
	const [isContentLiked, setIsContentLiked] = useState(!isLiked);
	const scale = useSharedValue(0);
	const childRef = useRef(null);

	const { colors } = useTheme();

	const animatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: scale.value }],
		opacity: scale.value,
	}));

	const handleDoubleTap = () => {
		if (isAnimating) {
			return;
		}
		setIsAnimating(true);
		setIsContentLiked(!isLiked);
		scale.value = withSpring(1);
		onDoubleTap();
		setTimeout(() => {
			scale.value = withSpring(0);
			setIsAnimating(false);
		}, 800)
	};

	return (
		<GestureHandlerRootView style={styles.container}>
			<TapGestureHandler onActivated={handleDoubleTap} numberOfTaps={2} simultaneousHandlers={"childTouchable"}>
				<View style={styles.container}>
					<View ref={childRef}>{children}</View>
					<Animated.View style={[styles.heartContainer, animatedStyle]}>
						<Icon
							name="heart"
							size={100}
							color={isContentLiked ? colors.green50 : colors.gray100}
						/>
					</Animated.View>
				</View>
			</TapGestureHandler>
		</GestureHandlerRootView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		width: '100%',
	},
	heartContainer: {
		...StyleSheet.absoluteFillObject,
		justifyContent: 'center',
		alignItems: 'center',
	},
});

export default AnimatingHeart;
