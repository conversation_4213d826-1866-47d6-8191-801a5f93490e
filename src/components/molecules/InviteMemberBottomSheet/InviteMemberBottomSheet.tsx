import { StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useCustomToast } from "@/hooks/useCustomToast";
import { useEffect, useState } from "react";

import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

type Props = {
    isModalVisible: any;
    setIsModalVisible: (isVisible: boolean) => void;
}

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

function InviteMemberBottomSheet(props: Props) {

    const { isModalVisible, setIsModalVisible } = props;
    const showToast = useCustomToast();

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
        borders,
    } = useTheme();

    const [text, setText] = useState('');
    const isDisabled = text.trim() == '';

    const c = colorTokens();

    function closeSheet(): void {
        setIsModalVisible(false);
    }

    function onPressInvite(): void {
        setText('');
        closeSheet();
        showToast('Invite Sent Successfully');
    }

    useEffect(() => {
        alignDirection.value = isDisabled ? 'flex-end' : 'flex-start';
        fillWidth.value = withTiming(isDisabled ? 0 : 1, { duration: 500 });
    }, [isDisabled]);

    const fillWidth = useSharedValue(isDisabled ? 0 : 1);
    const alignDirection = useSharedValue(isDisabled ? 'flex-end' : 'flex-start');

    const fillStyle = useAnimatedStyle(() => {
        return {
            width: `${fillWidth.value * 100}%`,
            alignSelf: alignDirection.value as 'flex-start' | 'flex-end',
        };
    });

    return (
        <Modal
            propagateSwipe={true}
            isVisible={isModalVisible}
            animationIn="slideInUp"
            style={[layout.justifyEnd, gutters.margin_0]}
            swipeDirection={['down']}
            onBackdropPress={closeSheet}
            avoidKeyboard={true}
            useNativeDriverForBackdrop={true}
        >
            <View style={[backgrounds.white, gutters.paddingBottom_40, gutters.paddingTop_8, gutters.paddingHorizontal_16, {borderTopRightRadius: 12, borderTopLeftRadius: 12, overflow: 'hidden', width: '100%'}]}>
                <View style={[backgrounds.black, gutters.marginTop_12, gutters.marginBottom_16, { borderRadius: 24, height: 5, width: 48, alignSelf: 'center' }]} />
                <View style={{gap: 20}}>
                    <View style={{gap: 8}}>
                        <Text style={[fonts.fontSizes.headings.H5, fonts.lineHeight.headings.H5, fonts.Bold, {color: c.content.default.emphasis}]}>Invite a Member</Text>
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, {color: c.content.default.default}]}>Invite anyone on Akina using e-mail or phone.</Text>
                    </View>
                    <TextInput
                        value={text}
                        onChangeText={e => setText(e)}
                        placeholder="Phone or email"
                        style={[borders.rounded_8, gutters.paddingHorizontal_12, {height: 48, borderWidth: 1, borderColor: 'lightgray'}]}
                    />
                    <View style={[borders.rounded_8, gutters.paddingVertical_12, gutters.marginTop_12, layout.itemsCenter, layout.justifyCenter, { height: 48, backgroundColor: '#929292' }]}>
                        <AnimatedTouchable
                            onPress={onPressInvite}
                            style={[
                                {
                                    ...StyleSheet.absoluteFillObject,
                                    backgroundColor: 'black',
                                    height: 48
                                },
                                fillStyle,
                                borders.rounded_8,
                            ]}
                        />
                        <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, layout.absolute, { color: c.content.onBold.default.default }]}>Send Invite</Text>
                    </View>
                </View>
            </View>

        </Modal>
    )
}

export default InviteMemberBottomSheet;
