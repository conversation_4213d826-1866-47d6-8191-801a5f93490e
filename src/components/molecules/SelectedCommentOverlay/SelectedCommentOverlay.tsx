// SelectedCommentOverlay.tsx
import { View, Text } from "react-native";
import { BlurView } from "@react-native-community/blur";
import FastImage from "react-native-fast-image";
import DefaultProfileIcon from "@/theme/assets/images/SocialConnect/DefaultProfileIcon.png";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { timeSince } from "@/utils";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { AKFastImage } from "@/components/atoms";

export interface SelectedCommentType {
  profilePhoto: string;
  avatarSize: number;
  userName: string;
  updatedAt: string;
  displayText: string;
}

interface Coordinates {
  x: number;
  y: number;
}

interface SelectedCommentOverlayProps {
  selectedComment: any;
  commentCoordinates: Coordinates | null;
  boxYPosition: number;
}

function SelectedCommentOverlay({ 
  selectedComment, 
  commentCoordinates, 
  boxYPosition 
}: SelectedCommentOverlayProps) {
  const { layout, gutters, fonts, colors, borders } = useTheme();
  const c = colorTokens();

  const { bottom } = useSafeAreaInsets();

  if (!selectedComment) return null;

  return (
    <View style={[layout.absolute, layout.flex_1, { top: 0, left: 0, right: 0, bottom: bottom ? 0 : -8, zIndex: 5 }]}>
      <BlurView blurType="light" blurAmount={7} style={[layout.flex_1]} />
      <View style={[
        layout.absolute, 
        gutters.paddingHorizontal_16, 
        { 
          width: '100%', 
          top: commentCoordinates ? commentCoordinates.y - boxYPosition : 0, 
          left: commentCoordinates ? commentCoordinates.x : 0 
        }
      ]}>
        <View style={[layout.row]}>
          <AKFastImage
            uri={selectedComment.profilePhoto}
            placeholder={DefaultProfileIcon}
            resizeMode={FastImage.resizeMode.contain}
            style={[{ width: selectedComment.avatarSize, height: selectedComment.avatarSize, borderRadius: 50, backgroundColor: colors.black }]}
          />
          <View
            style={[
              gutters.marginLeft_8,
              gutters.paddingHorizontal_12,
              gutters.paddingVertical_8,
              borders.rounded_12,
              { gap: 2, backgroundColor: c.background.default.neutrals.secondary, maxWidth: 300 }
            ]}
          >
            <View style={[layout.row, { gap: 4 }]}>
              <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.SemiBold, { color: c.content.default.emphasis }]}>
                {selectedComment.userName}
              </Text>
              <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: c.content.default.subdued }]}>
                {timeSince(new Date(selectedComment.updatedAt))}
              </Text>
            </View>
            <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: c.content.default.default }]}>
              {selectedComment.displayText}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}

export default SelectedCommentOverlay;