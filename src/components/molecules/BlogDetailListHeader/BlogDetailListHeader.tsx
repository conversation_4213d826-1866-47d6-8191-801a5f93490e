import { AddCommentSection, BlogDetailTopSection } from "@/components/molecules";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useRef } from "react";
import { Linking, Text, useWindowDimensions, View } from "react-native"
import AutoHeightWebView from "react-native-autoheight-webview";

type Props = {
    postDetail: any,
    onLikeButtonPressed: () => void
    onBookmarkButtonPressed: () => void
    onCommentButton: () => void
    onCommentSend: () => void
    commentRef: any
    profilePhoto: string | undefined
}

function BlogDetailListHeader(props: Props) {
    const {postDetail, onLikeButtonPressed, onBookmarkButtonPressed, onCommentButton,
        onCommentSend, commentRef, profilePhoto
    } = props;
    const {
		layout,
		gutters,
        fonts,
	} = useTheme();
    const c = colorTokens();
    const { width } = useWindowDimensions();
    const webRef = useRef<AutoHeightWebView>(null)

    return (
        <View>
            <BlogDetailTopSection 
                isSaveSelected={postDetail?.is_bookmarked} 
                isLikeSelected={postDetail?.is_liked} 
                image={postDetail?.thumbnail}
                onLikeButton={onLikeButtonPressed}
                onSaveButton={onBookmarkButtonPressed}
                onCommentButton={onCommentButton}

            />
            <View style={[gutters.marginHorizontal_16, layout.justifyCenter]}>
                <Text style={[fonts.size_32, fonts.Medium, { color: c.content.default.default }]}>{postDetail?.title}</Text>
            </View>
            <View style={[gutters.marginHorizontal_16, gutters.marginTop_16]}>
                <View style={[layout.flex_1,]}>
                    <AutoHeightWebView
                        ref={webRef}
                        style={{ width:  width-40,}}
                        source={{ html: postDetail?.content ?? postDetail?.description ?? '' }}
                        scalesPageToFit={false}
                        scrollEnabled={false}
                        onNavigationStateChange={(event) => {
                            if (event.url != "about:blank") {
                                webRef.current?.stopLoading()
                                Linking.openURL(event.url)
                            }
                        }}
                    />
                </View>
            </View>
            <AddCommentSection  
                ref={commentRef} 
                onSend={onCommentSend} 
                image={profilePhoto}/>
        </View>
    )
}

export default BlogDetailListHeader;
