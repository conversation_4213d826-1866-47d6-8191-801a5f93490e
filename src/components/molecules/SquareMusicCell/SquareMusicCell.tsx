import { AKFastImage } from "@/components/atoms";
import { useTheme } from "@/theme";
import { musicSchema } from "@/types/schemas/musicListing";
import { TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { z } from "zod";
type Props = {
    item: z.infer<typeof musicSchema>
    onPress?: (item:  z.infer<typeof musicSchema>) => void

}
function SquareMusicCell(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    function onItemPressed() {
        if (props.onPress != null) {
            props.onPress(props.item)
        }
    }
    return (
        <TouchableOpacity style={[layout.flex_1]} onPress={onItemPressed}>
            <View style={[layout.flex_1]}>
                <AKFastImage
                    uri={props.item.thumbnail}
                    style={[layout.flex_1,]}
                />
            </View>
        </TouchableOpacity>
    )
}
export default SquareMusicCell