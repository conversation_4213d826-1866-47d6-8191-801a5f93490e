import { Dispatch, SetStateAction } from "react";
import SocialConnectPostComment from "../SocialConnectPostComment/SocialConnectPostComment";
import { Comment } from "@/types/models/Comment";

interface CommentItemProps {
  comment: Comment;
  setReplyingComment: Dispatch<SetStateAction<Comment | null>>;
  onDeleteComment: (comment: Comment) => void;
  onEditComment: (comment: Comment) => void;
  onPressComment: (data?: any) => void;
  displayPopoverBackdrop?: boolean;
}

function CommentItem({ 
  comment, 
  setReplyingComment, 
  onDeleteComment, 
  onEditComment, 
  onPressComment, 
  displayPopoverBackdrop 
}: CommentItemProps) {
  return (
    <SocialConnectPostComment
      comment={comment}
      setReplyingComment={setReplyingComment}
      onDeleteComment={onDeleteComment}
      onEditComment={onEditComment}
      onPressComment={onPressComment}
      displayPopoverBackdrop={displayPopoverBackdrop ?? false}
    />
  );
}

export default CommentItem;
