import { Text, TouchableOpacity, View, Platform, ImageSourcePropType, ActivityIndicator } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import { colorTokens } from "@/theme/colorTokens";
import ImageIcon from "@/theme/assets/images/ImageIcon.png";
import BinIcon from "@/theme/assets/images/SocialConnect/BinIcon.png";
import CameraIcon from "@/theme/assets/images/CameraIcon.png";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import { UserPictureType } from "@/utils/constants";
import { usePersonalDetailsUpdator } from "@/hooks/usePersonalDetailsUpdator";
import AKBottomSheet from "@/components/atoms/AKBottomSheet/AKBottomSheet";
import { useEffect } from "react";

type Props = {
    isModalVisible: any;
    setIsModalVisible: (isVisible: boolean) => void;
    type: string;
    successCallback?: () => void;
}

type Option = {
    id: number;
    value: string;
    icon: ImageSourcePropType;
    textColor?: string;
    iconColor?: string;
    onPress: () => void;
};

type UploadPhotoData = {
    profile_photo?: {
        name: string;
        type: string;
        uri: string;
    };
    cover_photo?: {
        name: string;
        type: string;
        uri: string;
    };
};


function SocialConnectUserPictureUpdatorBottomSheet(props: Props) {
    const { isModalVisible, setIsModalVisible, type, successCallback } = props;
    const {
        layout,
        gutters,
        fonts,
    } = useTheme();

    const c = colorTokens();

    const { isLoading, onUpdateProfile } = usePersonalDetailsUpdator(successCallback);

    function closeSheet(): void {
        setIsModalVisible(false);
    }

    useEffect(() => {
        if (!isLoading) {
            closeSheet();
        }
    }, [isLoading]);

    async function onPressOption(launchFunction: any): Promise<void> {
        const result = await launchFunction({mediaType: "photo", quality: 0.1});
        if (result.assets != null && result.assets.length > 0) {
            const photo = result.assets[0];
            const data: UploadPhotoData = {};
            const key = type === UserPictureType.PROFILE ? 'profile_photo' : 'cover_photo';
            data[key] = {
                name: photo.fileName,
                type: photo.type,
                uri: Platform.OS === 'ios' ? photo?.uri?.replace('file://', '') : photo?.uri,
            };
            onUpdateProfile(data);
        }
    }

    function onPressRemoveCurrentPhoto(): void {
        const key = type === UserPictureType.PROFILE ? 'remove_profile_photo' : 'remove_cover_photo';
        const data: Record<string, number> = {
            [key]: 1
        }
        onUpdateProfile(data);
    }

    const options: Option[] = [
        {
            id: 1,
            value: 'Open Gallery',
            icon: ImageIcon,
            onPress: () => onPressOption(launchImageLibrary)
        },
        {
            id: 2,
            value: 'Open Camera',
            icon: CameraIcon,
            onPress: () => onPressOption(launchCamera)
        },
        {
            id: 3,
            value: 'Remove Current Photo',
            icon: BinIcon,
            textColor: c.content.error.default,
            iconColor: c.content.error.default,
            onPress: () => onPressRemoveCurrentPhoto()
        }
    ]

    return (
        <AKBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible}>
            {
                isLoading && (
                    <ActivityIndicator animating={true} style={[gutters.marginTop_4]} />
                )
            }
            {
                options.map(option => (
                    <TouchableOpacity key={option.id} onPress={option.onPress} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_12, gutters.paddingHorizontal_16]}>
                        <AKIcon source={option.icon} tintColor={option.iconColor || c.content.default.default} styles={[gutters.marginRight_12]} />
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, {color: option.textColor || c.content.default.default}]}>{option.value}</Text>
                    </TouchableOpacity>
                ))
            }
        </AKBottomSheet>
    )
}
export default SocialConnectUserPictureUpdatorBottomSheet;