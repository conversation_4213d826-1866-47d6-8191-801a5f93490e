import { AKFastImage } from "@/components/atoms";
import { useTheme } from "@/theme";
import { musicSchema } from "@/types/schemas/musicListing";
import { podcastSchema } from "@/types/schemas/podcastListing";
import { videoSchema } from "@/types/schemas/videoListing";
import { TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { z } from "zod";
type Props = {
    item: z.infer<typeof videoSchema>
    onPress?: (item:  z.infer<typeof videoSchema>) => void

}
function SquareVideoCell(props: Props) {
    const {
		layout
	} = useTheme();
    function onItemPressed() {
        if (props.onPress != null) {
            props.onPress(props.item)
        }
    }
    return (
        <TouchableOpacity style={[layout.flex_1]} onPress={onItemPressed}>
            <View style={[layout.flex_1]}>
                <AKFastImage
                    uri={props.item.thumbnail}
                    style={[layout.flex_1]}
                />
            </View>
        </TouchableOpacity>
    )
}
export default SquareVideoCell