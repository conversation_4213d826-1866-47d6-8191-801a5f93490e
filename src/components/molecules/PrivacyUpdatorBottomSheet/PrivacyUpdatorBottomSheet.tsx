import { Text, TouchableOpacity, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useEffect, useState } from "react";
import RadioButton from "@/components/atoms/RadioButton/RadioButton";
import AKBottomSheet from "@/components/atoms/AKBottomSheet/AKBottomSheet";

type Props = {
    isModalVisible: any;
    setIsModalVisible: (isVisible: boolean) => void;
    currentPrivacy: string;
    setCurrentPrivacy: (privacy: string) => void;
}

const radioOptions = [
    {
        id: 1,
        label: 'Members Only (Private)',
        info: 'Only invited members can join. Posts are only visible within the group.',
        value: 'Members Only'
    },
    {
        id: 2,
        label: 'Visible to All (Public)',
        info: 'Anyone can find and join this group. Posts are visible to all users.',
        value: 'Visible to All'
    }
]

function PrivacyUpdatorBottomSheet(props: Props) {

    const { isModalVisible, setIsModalVisible, currentPrivacy, setCurrentPrivacy } = props;

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
        borders,
    } = useTheme();

    const [selectedPrivacy, setSelectedPrivacy] = useState('');

    const c = colorTokens();

    useEffect(() => {
        if (isModalVisible) {
            setSelectedPrivacy(currentPrivacy);
        }
    }, [isModalVisible]);

    function closeSheet(): void {
        setIsModalVisible(false);
    }

    function onPressDone(): void {
        setCurrentPrivacy(selectedPrivacy);
        closeSheet();
    }

    return (
        <AKBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible}>
            <View style={[backgrounds.white,  {borderTopRightRadius: 12, borderTopLeftRadius: 12, overflow: 'hidden', width: '100%'}]}>
                <View style={[layout.itemsCenter, gutters.paddingVertical_12, { gap: 12, borderBottomWidth: 1, borderBottomColor: c.stoke.default.subdued }]}>
                    <View style={[backgrounds.black, { borderRadius: 24, height: 5, width: 48 }]} />
                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, {color: c.content.default.emphasis}]}>Choose Privacy</Text>
                </View>
                <View style={[gutters.paddingHorizontal_16, gutters.paddingVertical_20, {gap: 8}]}>
                    {
                        radioOptions.map(option => (
                            <RadioButton
                                key={option.id} label={option.label} isSelected={selectedPrivacy == option.value}
                                onPress={() => setSelectedPrivacy(option.value)} info={option.info} />
                        ))
                    }
                </View>
                <View style={[gutters.paddingHorizontal_16, { borderTopWidth: 1, borderTopColor: c.stoke.default.subdued }]}>
                    <TouchableOpacity onPress={onPressDone} style={[borders.rounded_8, gutters.paddingVertical_12, gutters.marginTop_12, layout.itemsCenter, layout.justifyCenter, { height: 48, backgroundColor: c.fill.bold.neutrals.rest }]}>
                        <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, layout.absolute, { color: c.content.onBold.default.default }]}>Done</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </AKBottomSheet>
    )
}

export default PrivacyUpdatorBottomSheet;
