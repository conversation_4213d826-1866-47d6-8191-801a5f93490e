import { useTheme } from "@/theme";
import { categoriesListingSchema, musicSchema } from "@/types/schemas/musicListing";
import { Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import Carousel from "react-native-reanimated-carousel";
import { z } from "zod";
import PlayRedIcon from "@/theme/assets/images/Home/PlayRedIcon.png"
import { AKFastImage, ImageVariant } from "@/components/atoms";
import { videoCategorySchema, videoSchema } from "@/types/schemas/videoListing";
import _Icon from 'react-native-vector-icons/FontAwesome';

type Props = {
    item: z.infer<typeof videoCategorySchema>
    onPress?: (item:  z.infer<typeof videoSchema>) => void
}
function VideoCarouselView(props: Props) {
    const Icon = _Icon as React.ElementType
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    
    function handleItemPress(item: z.infer<typeof videoSchema>) {
        if (props.onPress != null) {
            props.onPress(item)
        }
    }
    return (
        <View>
            <View style={[layout.row,layout.itemsCenter,layout.flex_1, gutters.marginTop_12]}>
                <Text style={[gutters.marginHorizontal_12,fonts.musicCategoryTitleColor, fonts.size_24, fonts.Bold]}>{props.item.name}</Text>
                <Icon style={[gutters.marginLeft_16]} name='angle-right' size={30} color={colors.musicCategoryTitleColor}/>
            </View>
            <View style={[layout.itemsCenter, layout.flex_1]}>
                <Carousel
                    loop
                    width={327}
                    height={312}
                    mode="parallax"
                    modeConfig={{
                        parallaxScrollingOffset: 270,
                        parallaxScrollingScale: 0.8
                    }}
                    data={props.item.videos}
                    scrollAnimationDuration={1000}
                    onSnapToItem={(index) => console.log('current index:', index)}
                    renderItem={({item, index }) => (
                        <TouchableOpacity style={{flex: 1}} onPress={() => handleItemPress(item)}>
                            <View
                                style={{
                                    flex: 1,
                                    justifyContent: 'center',
                                    borderRadius: 40,
                                    overflow: 'hidden'
                                }}
                            >
                                <AKFastImage
                                    uri={item.thumbnail}
                                    style={[layout.flex_1]}
                                />
                                <View style={[layout.row, gutters.marginBottom_0, layout.flex_1 ,layout.absolute, , {bottom: 20} ]}>
                                    <View style={[layout.row, layout.flex_1, layout.itemsCenter, layout.justifyBetween]}>
                                        <Text style={[layout.flex_1, gutters.marginHorizontal_24 ,fonts.gray50, fonts.size_24, fonts.Medium]}>{item.title}</Text>
                                        <ImageVariant
                                            source={PlayRedIcon}
                                            style={ [layout.itemsCenter, gutters.marginRight_12 ,{ width: 50, height: 50}]}
                                        />
                                    </View>
                                    
                                </View>
                            </View>
                        </TouchableOpacity>
                        
                    )}
                />
            </View>
        </View>
    )
}
export default VideoCarouselView