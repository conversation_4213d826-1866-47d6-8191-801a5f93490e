import { ImageVariant, MessageRow } from "@/components/atoms";
import { Animated, Text, TouchableOpacity, View, Keyboard } from "react-native";
import ReplyIcon from "@/theme/assets/images/Home/ReplyIcon.png"
import { useTheme } from "@/theme";
import { Comment } from "@/types/models/comment";
import { useTranslation } from "react-i18next";
import { memo, MutableRefObject, useRef, useState, useEffect } from "react";
import ReplyCell from "../ReplyCell/ReplyCell";
import { Reply } from "@/types/models/reply";
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import AddCommentSection from "../AddCommentSection/AddCommentSection";
import { useQuery } from "@tanstack/react-query";
import { getProfile } from "@/services/users";
import { colorTokens } from "@/theme/colorTokens";
import basicProfile from "@/services/users/basicProfile";

export type CommentItem = {
    comment: Comment
    onCommentReply?: (commentId: number, reply: string) => void
    onEditComment?: (comment: Comment) => void
    onDeleteComment?: (comment: Comment) => void
    onCommentSend?: (text: string, isEdit: boolean, comment?: Comment, commentRef?: MutableRefObject<{editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>) => void
    onReportComment?: (postId: number, commentID: number, replyID?: number, userID?: number) => void
}


function CommentCell(props: CommentItem) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();

    const c = colorTokens();

    const {data } = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile
    });
    const [isRepliesExpanded, setIsRepliesExpanded] = useState(false)
    const [isReplySectionExpanded, setIsReplySectionExpanded] = useState(false)
    const [isReplyVisible, setIsReplyVisible] = useState(!props?.comment?.reported_comment_exists);
    
    const { t } = useTranslation(['home']);
    const commentRef = useRef<{editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>()

    useEffect(() => {
        setIsReplyVisible(!props?.comment?.reported_comment_exists);
    }, [props?.comment?.reported_comment_exists]);

    function replyButtonTapped() {
        setIsRepliesExpanded(!isRepliesExpanded)
    }
    function renderItem({item}: any) {
        let reply: Reply = item
        return (
            <ReplyCell 
                reply={reply}
                commentId={props.comment.id}
                userId={data?.user.id}
                onDeleteComment={props.onDeleteComment}
                onEditComment={onEditReply}
                onReportComment={props.onReportComment}
            />
        )
    }
    function onReply(text: string, isEdit: boolean, comment?: Comment) {
        if (isEdit && props.onCommentSend != null) {
            props.onCommentSend(text, isEdit, comment, commentRef);
        }
        else if (props.onCommentReply != null) {
            props.onCommentReply(props.comment.id, text)
        }
        setIsReplySectionExpanded(false)
        Keyboard.dismiss()
        
    }

    function onEditReply(comment: Comment) {
        if (!isReplySectionExpanded) {
            setIsReplySectionExpanded(true)
        }
        if (commentRef.current != null) {
            commentRef?.current.editComment(comment)
        }
        setTimeout(() => {
            if (commentRef.current != null) {
                commentRef.current.makeCommentFirstResponsder()
            }
        }, 200)
    }

    return (
        <View>
            <View style={[gutters.marginHorizontal_32, gutters.marginTop_16]}>
                <MessageRow
                    comment={props.comment}
                    isTooltipVisible={true}
                    userId={data?.user.id}
                    onDeleteComment={props.onDeleteComment}
                    onEditComment={props.onEditComment}
                    onReportComment={props.onReportComment}
                    setIsReported={setIsReplyVisible}
                />
                <View style={[layout.row, gutters.marginTop_16, layout.justifyBetween]}>
                    <TouchableOpacity onPress={replyButtonTapped}>
                        <View style={[layout.row, borders.rounded_4, layout.justifyCenter, layout.itemsCenter,{ height: 30}]}>
                            <Text style={[fonts.Medium, {color: c.content.default.default}]}>{t('home:Replies', { length: props.comment.replies.length })}</Text>
                        </View>
                    </TouchableOpacity>
                    {
                        isReplyVisible && (
                            <View>
                                <TouchableOpacity onPress={() => {
                                    setIsReplySectionExpanded(!isReplySectionExpanded)
                                    if (isReplySectionExpanded == false) {
                                        commentRef?.current?.makeCommentFirstResponsder()

                                    }
                                }}>
                                    <View style={[layout.row, layout.itemsCenter, {height: 30}]}>
                                        <ImageVariant
                                            source={ReplyIcon}
                                            style={ [layout.itemsCenter, {tintColor: c.content.default.default}]}
                                        />
                                        <Text style={[gutters.marginLeft_4, { color: c.content.default.default }]}>{t('home:Reply')}</Text>

                                    </View>

                                </TouchableOpacity>
                                
                            </View>
                        )
                    }
                </View>
                {
                    isReplyVisible && (
                        <Animated.View  style={[isReplySectionExpanded ? layout.flex_1 : {height: 0} , {overflow: 'hidden'}]}>
                            <AddCommentSection ref={commentRef} onSend={onReply} />
                        </Animated.View>
                    )
                }
                {props.comment.replies.length > 0 &&
                <View>
                    

                    <Animated.View style={[isRepliesExpanded ? layout.flex_1 : {height: 0} , {overflow: 'hidden'}]}>
                        <KeyboardAwareFlatList 
                            // ListHeaderComponent={headerContent}
                            showsVerticalScrollIndicator={false} 
                            contentContainerStyle={{ paddingBottom: 20}} 
                            data={props.comment.replies}
                            keyExtractor={
                                (_, index)=> index.toString()
                            }
                            renderItem={renderItem}
                            keyboardShouldPersistTaps={'handled'}
                        />

                    </Animated.View>
                </View>
                }

            </View>
            

        </View>
    )
}
export default memo(CommentCell)