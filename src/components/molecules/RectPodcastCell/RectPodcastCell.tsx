import { AKFastImage } from "@/components/atoms";
import { useTheme } from "@/theme";
import { podcastSchema } from "@/types/schemas/podcastListing";
import { Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { z } from "zod";
type Props = {
    item: z.infer<typeof podcastSchema>
    onPress?: (item:  z.infer<typeof podcastSchema>) => void
}
function RectPodcastCell(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    function onItemPressed() {
        if (props.onPress != null) {
            props.onPress(props.item)
        }
    }
    return (
        <TouchableOpacity style={[layout.flex_1]} onPress={onItemPressed}>
            <View style={[layout.flex_1]}>
                <AKFastImage
                    uri={props.item.thumbnail}
                    style={[layout.flex_1,borders.rounded_16]}
                />
                <View style={[layout.row , gutters.marginTop_4]}>
                    <Text style={[layout.flex_1, gutters.marginHorizontal_0, fonts.gray800, fonts.size_12, fonts.Medium]}>{props.item.title}</Text>
                </View>
            </View>
        </TouchableOpacity>
    )
}
export default RectPodcastCell