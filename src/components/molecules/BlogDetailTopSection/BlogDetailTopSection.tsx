import { ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme"
import { View } from "react-native"
import FastImage from "react-native-fast-image"
import HeartIcon from "@/theme/assets/images/Home/HeartIcon.png"
import MessageIcon from "@/theme/assets/images/Home/MessageIcon.png"
import SaveIcon from "@/theme/assets/images/Home/SaveIcon.png"
import { TouchableOpacity } from "react-native-gesture-handler"
import BlogButtonsContainer from "../BlogButtonsContainer/BlogButtonsContainer"
type Props = { 
    image?: string
    isLikeSelected?: boolean
    isSaveSelected?: boolean
    onLikeButton?: () => void
    onCommentButton?: () => void
    onSaveButton?: () => void
}
function BlogDetailTopSection(props: Props) {
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    return (
        <View>
            <View style={[ gutters.marginTop_16, gutters.marginHorizontal_16  ,{height: 250}]}>
                <FastImage
                    style={[layout.flex_1]}
                    source={{uri: props.image}}
                />
            </View>
            <BlogButtonsContainer 
                isLikeSelected={props.isLikeSelected}
                isSaveSelected={props.isSaveSelected}
                onSaveButton={props.onSaveButton}
                onLikeButton={props.onLikeButton}
                onCommentButton={props.onCommentButton}
            />
        </View>
    )
}
export default BlogDetailTopSection