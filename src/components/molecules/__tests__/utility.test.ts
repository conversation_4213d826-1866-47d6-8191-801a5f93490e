import { getSocialConnectCellComponent } from '../utility';
import { SocialConnectPostCell, SocialConnectStoryCell, SocialConnectTextCell } from "@/components/molecules";

// Mock the component imports
jest.mock("@/components/molecules", () => ({
  SocialConnectTextCell: "SocialConnectTextCell",
  SocialConnectStoryCell: "SocialConnectStoryCell",
  SocialConnectPostCell: "SocialConnectPostCell"
}));

describe('Molecules Utility Functions', () => {
  describe('getSocialConnectCellComponent', () => {
    it('should return the correct component for text type', () => {
      const component = getSocialConnectCellComponent('text');
      expect(component).toBe(SocialConnectTextCell);
    });

    it('should return the correct component for story type', () => {
      const component = getSocialConnectCellComponent('story');
      expect(component).toBe(SocialConnectStoryCell);
    });

    it('should return the correct component for post type', () => {
      const component = getSocialConnectCellComponent('post');
      expect(component).toBe(SocialConnectPostCell);
    });

    it('should return undefined for unknown type', () => {
      const component = getSocialConnectCellComponent('unknown');
      expect(component).toBeUndefined();
    });

    it('should handle undefined type', () => {
      const component = getSocialConnectCellComponent(undefined);
      expect(component).toBeUndefined();
    });

    it('should handle null type', () => {
      const component = getSocialConnectCellComponent(null);
      expect(component).toBeUndefined();
    });
  });
});