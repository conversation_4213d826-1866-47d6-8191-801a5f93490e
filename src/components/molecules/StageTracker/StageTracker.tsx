import { View, Text } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import TickIcon from "@/theme/assets/images/TickIcon.png";
import { Stage } from "@/screens/SideTabs/Settings/constants";

type Props = {
    stages: Stage[];
    activeStage: Stage;
}

function StageTracker({ stages, activeStage }: Props) {

    const { layout, gutters, fonts, colors } = useTheme();
        
    const c = colorTokens();

    return (
        <View>
            <View style={[layout.row, layout.justifyBetween]}>
                <View style={[layout.absolute, {height: 2, width: '95%', backgroundColor: c.content.default.disabled, top: 16 }]} />
                <View style={[layout.absolute, {height: 2, width: `${activeStage.index * 47}%`, backgroundColor: c.background.medium.primary.default, top: 16 }]} />
                {
                    stages.map(stage => {
                        let currentStage;
                        if (stage.index == activeStage.index) {
                            currentStage = (
                                <View style={[layout.itemsCenter, layout.justifyCenter, { width: 32, height: 32, borderRadius: 40, backgroundColor: 'white', borderWidth: 2, borderColor: c.background.medium.primary.default }]}>
                                    <View style={[layout.itemsCenter, layout.justifyCenter, { width: 24, height: 24, borderRadius: 24, backgroundColor: c.background.medium.primary.default }]}>
                                        <View style={[{ width: 8, height: 8, borderRadius: 8, backgroundColor: 'white' }]} />
                                    </View>
                                </View>
                            )
                        } else if (stage.index > activeStage.index) {
                            currentStage = (
                                <View style={[layout.itemsCenter, layout.justifyCenter, gutters.marginTop_4, { width: 24, height: 24, borderRadius: 24, backgroundColor: 'white', borderWidth: 1, borderColor: c.background.default.neutrals.tertiary }]}>
                                    <View style={[{ width: 8, height: 8, borderRadius: 8, backgroundColor: colors.neutrals100 }]} />
                                </View>
                            )
                        } else {
                            currentStage = (
                                <View style={[layout.itemsCenter, layout.justifyCenter, gutters.marginTop_4, { width: 24, height: 24, borderRadius: 24, backgroundColor: c.background.medium.primary.default }]}>
                                    <AKIcon source={TickIcon} size={16} />
                                </View>
                            )
                        }
                        return (
                            <View key={stage.index} style={[layout.itemsCenter, layout.justifyBetween]}>
                                {currentStage}
                                <View style={[gutters.marginTop_4]}>
                                    <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: stage.index == activeStage.index ? c.content.primary.default : c.content.default.emphasis }]}>{stage.label}</Text>
                                </View>
                            </View>
                        )
                    })
                }
            </View>
        </View>
    )
}
export default StageTracker;