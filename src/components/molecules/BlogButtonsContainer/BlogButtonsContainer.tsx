import { ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme";
import { TouchableOpacity, View } from "react-native"
import HeartIcon from "@/theme/assets/images/Home/HeartIcon.png"
import MessageIcon from "@/theme/assets/images/Home/MessageIcon.png"
import SaveIcon from "@/theme/assets/images/Home/SaveIcon.png"
type Props = { 
    isLikeSelected?: boolean
    isSaveSelected?: boolean
    onLikeButton?: () => void
    onCommentButton?: () => void
    onSaveButton?: () => void
}
function BlogButtonsContainer(props: Props) {
    const {
		layout,
		gutters,
        colors,
	} = useTheme();
    return (
        <View style={[layout.row, gutters.marginHorizontal_32, layout.itemsCenter, layout.justifyBetween ,{height: 40}]}>
                <View style={[layout.row]}>
                    <TouchableOpacity onPress={props.onLikeButton}>
                        <View style={[layout.justifyCenter,layout.itemsCenter ,{height:40, width: 40}]}>
                            <ImageVariant
                                source={HeartIcon}
                                style={ [layout.itemsCenter, {tintColor: props.isLikeSelected ? colors.green50 : colors.gray400}]}
                            />
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={props.onCommentButton}>
                        <View style={[layout.justifyCenter, layout.itemsCenter ,{height:40, width: 40}]}>
                            <ImageVariant
                                source={MessageIcon}
                                style={ [layout.itemsCenter , {tintColor: colors.gray400}]}
                            />
                        </View>
                    </TouchableOpacity>
                </View>
                <View>
                    <TouchableOpacity onPress={props.onSaveButton}>
                        <View style={[layout.justifyCenter, layout.itemsCenter,{height:40, width: 40}]}>
                            <ImageVariant
                                source={SaveIcon}
                                style={ [layout.itemsCenter, {tintColor: props.isSaveSelected ? colors.green50 : colors.gray400}]}
                            />
                        </View>
                    </TouchableOpacity>
                </View>

            </View>
    )
}
export default BlogButtonsContainer