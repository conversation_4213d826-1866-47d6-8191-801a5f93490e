import React from 'react';
import { View, TouchableOpacity, Text, ViewStyle, TextStyle, StyleProp } from 'react-native';
import { z } from 'zod';
import { conversationSchema } from '@/types/schemas/conversationSchema';
import { timeSince } from '@/utils';
import { HighlightedText } from '../HighlightedText/HighLightedText';

interface ConversationItemProps {
  item: z.infer<typeof conversationSchema>;
  onPress: (item: z.infer<typeof conversationSchema>) => void;
  searchQuery?: string;
  containerStyle?: ViewStyle;
  titleStyle?: StyleProp<TextStyle>;
  timeStyle?: StyleProp<TextStyle>;
  highlightStyle?: StyleProp<TextStyle>;
  showBullet?: boolean;
}

export function ConversationItem({
  item,
  onPress,
  searchQuery = '',
  containerStyle,
  titleStyle,
  timeStyle,
  highlightStyle,
  showBullet = true
}: ConversationItemProps) {
  const displayTitle = showBullet ? `\u2022 ${item.title}` : item.title;
  
  return (
    <TouchableOpacity onPress={() => onPress(item)}>
      <View style={[{ marginTop: 16, marginHorizontal: 16 }, containerStyle]}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <View style={{ flex: 1, marginRight: 8 }}>
            <HighlightedText 
              text={displayTitle}
              searchQuery={searchQuery}
              style={titleStyle}
              highlightStyle={highlightStyle}
            />
          </View>
          <View style={{ flexShrink: 0 }}>
            <Text style={timeStyle}>
              {timeSince(new Date(item.updated_at))}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}
