import { Alert, ImageSourcePropType, Text, TouchableOpacity, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import Modal from "react-native-modal";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import RemoveUserIcon from "@/theme/assets/images/SocialConnect/RemoveUserIcon.png";
import CircleCutIcon from "@/theme/assets/images/SocialConnect/CircleCutIcon.png";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import followUnfollowUser from "@/services/socialConnect/users/followUnfollowUser";
import { useCustomToast } from "@/hooks/useCustomToast";
import { z } from 'zod';
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";
import AKBottomSheet from "@/components/atoms/AKBottomSheet/AKBottomSheet";

type Props = {
    isModalVisible: any;
    setIsModalVisible: (isVisible: boolean) => void;
    item: z.infer<typeof socialConnectUserSchema>;
    isAddedMember?: boolean;
    userId: number | undefined;
    onPressBlock: (item: z.infer<typeof socialConnectUserSchema>) => void;
}

type RemoveMemberAlertOption = {
    text: string;
    onPress?: () => void | Promise<void>;
    style?: 'default' | 'cancel' | 'destructive';
};

type SettingItem = {
    id: number;
    text: string;
    textColor: string;
    icon: ImageSourcePropType;
    iconColor?: string;
    onPress: () => void;
};

function MembersActionBottomSheet(props: Props) {

    const { isModalVisible, setIsModalVisible, item, isAddedMember, userId, onPressBlock } = props;
    const showToast = useCustomToast();

    const {
        layout,
        gutters,
        backgrounds,
        fonts,
    } = useTheme();

    const queryClient = useQueryClient();

    const c = colorTokens();

    function closeSheet(): void {
        setIsModalVisible(false);
    }

    const unFollowMutation = useMutation(
        {
            mutationFn: (data: any) => followUnfollowUser(data),
            onSuccess: (response) => {
                closeSheet();
                showToast('Member Removed Successfully');
                queryClient.setQueryData([`${userId}-followings`],
                    (cacheData: any) => {
                    if (cacheData != null) {
                        let pages = cacheData?.pages?.map((page: any) => {
                            let pageData = page?.data?.filter((user: any) => user.id !== item?.id);
                            return {
                                ...page,
                                data: pageData,
                                total: page.total - 1
                            };
                        });
                        return {
                            ...cacheData,
                            pages: pages,
                        };
                    }
                });
            },
            onError: (error) => {
                closeSheet();
                Alert.alert("Error!", error.message)
            }
        }
    );

    function onPressRemoveMember(): void {
        let options: RemoveMemberAlertOption[] = [
            {
                text: 'Cancel',
            },
            {
                text: 'Remove',
                onPress: async () => {
                    const apiData = {
                        user_id: item?.id
                    };
                    console.log(apiData)
                    unFollowMutation.mutate(apiData);
                },
                style: 'destructive',
            },
        ]
        Alert.alert('Remove this member?', 'You won’t see their updates or shared activity anymore.', options)
    }

    function onPressBlockMember(): void {
        closeSheet();
        onPressBlock(item);
    }

    const settings: SettingItem[] = isAddedMember
        ? [
            { id: 1, text: 'Remove Member', textColor: c.content.error.default, icon: RemoveUserIcon, onPress: onPressRemoveMember },
            { id: 2, text: 'Block Member', textColor: c.content.error.default, icon: CircleCutIcon, iconColor: c.content.error.default, onPress: onPressBlockMember },
        ]
        : [
            { id: 2, text: 'Block Member', textColor: c.content.error.default, icon: CircleCutIcon, iconColor: c.content.error.default, onPress: onPressBlockMember },
        ];

    return (
        <AKBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible}>
            {
                settings.map(option => (
                    <TouchableOpacity key={option.id} onPress={option.onPress} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_12, gutters.paddingHorizontal_16]}>
                        <AKIcon source={option.icon} styles={[gutters.marginRight_12]} tintColor={option.iconColor} />
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, {color: option.textColor}]}>{option.text}</Text>
                    </TouchableOpacity>
                ))
            }
        </AKBottomSheet>
    )
}

export default MembersActionBottomSheet;
