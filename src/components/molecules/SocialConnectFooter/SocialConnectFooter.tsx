import { useTheme } from "@/theme"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { Text, View } from "react-native"
import { z } from "zod"
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import SendIcon from "@/theme/assets/images/SocialConnect/SendIcon.png";
import BookmarkIcon from "@/theme/assets/images/SocialConnect/BookmarkIcon.png";
import BookMarkFilledIcon from "@/theme/assets/images/SocialConnect/BookMarkFilledIcon.png";
import CommentIcon from "@/theme/assets/images/SocialConnect/CommentIcon.png";
import ThumbsUpIcon from "@/theme/assets/images/SocialConnect/ThumbsUpIcon.png";
import ThumbsUpFilledIcon from "@/theme/assets/images/SocialConnect/ThumbsUpFilledIcon.png";
import { onShareButtonTapped } from "@/screens/SideTabs/Home/SocialConnect/utility";
import { colorTokens } from "@/theme/colorTokens";
import { usePremiumMemberPopup } from "@/hooks/usePremiumMemberPopup";
import { useQuery } from "@tanstack/react-query";
import { getProfile } from "@/services/users";
import { UserTypes } from "@/utils/constants";
import basicProfile from "@/services/users/basicProfile";

type Props = {
    item: z.infer<typeof socialConnectPostSchema>;
    isWhite?: boolean;
    onCommentButtonTapped: (item: z.infer<typeof socialConnectPostSchema>) => void;
    onLikeButton: (item: z.infer<typeof socialConnectPostSchema>) => void;
    onBookmarkButton: (item: z.infer<typeof socialConnectPostSchema>) => void;
}

function SocialConnectPostFooter(props: Props) {
    const {
        layout,
        fonts,
        gutters,
    } = useTheme();

    const { data } = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile
    });

    const c = colorTokens();

    const showPremiumMemberPopup = usePremiumMemberPopup();

    const { comments_count: commentsCount, likes_count: likesCount, bookmarks_count: bookmarksCount } = props.item;

    function onSharePress() {
        onShareButtonTapped(props.item)
    }

    function onCommentPress() {
        props.onCommentButtonTapped(props.item);
    }

    function onLikePress() {
        if (data?.user.user_type == UserTypes.FREE) {
            showPremiumMemberPopup();
        } else {
            props.onLikeButton(props.item);
        }
    }

    function onBookmarkPress() {
        if (data?.user.user_type == UserTypes.FREE) {
            showPremiumMemberPopup();
        } else {
            props.onBookmarkButton(props.item);
        }
    }

    const textColor = props.isWhite ? c.content.onBold.default.default : c.content.default.default;
    const iconColor = props.isWhite ? c.content.onBold.default.default : c.content.default.default;

    return (
        <View style={[layout.row, gutters.paddingHorizontal_12, gutters.paddingTop_12, gutters.paddingBottom_16, { gap: 4 }]}>
            <View style={[layout.row, layout.itemsCenter]}>
                <AKIcon tintColor={ props.item.is_liked ? c.fill.bold.primary.rest : iconColor} source={props.item.is_liked ? ThumbsUpFilledIcon : ThumbsUpIcon} size={24} styles={[gutters.marginHorizontal_4]} onPress={onLikePress} />
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: textColor }]}>{likesCount || 0}</Text>
            </View>
            <View style={[layout.row, layout.itemsCenter]}>
                <AKIcon tintColor={iconColor} source={ CommentIcon} size={24} styles={[gutters.marginHorizontal_4]} onPress={onCommentPress} />
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: textColor }]}>{commentsCount || 0}</Text>
            </View>
            <View style={[layout.row, layout.itemsCenter]}>
                <AKIcon tintColor={iconColor} source={props.item.is_bookmarked ? BookMarkFilledIcon : BookmarkIcon} size={24} styles={[gutters.marginHorizontal_4]} onPress={onBookmarkPress} />
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.body, { color: textColor }]}>{bookmarksCount || 0}</Text>
            </View>
            <View style={[layout.flex_1, layout.itemsEnd]}>
                <AKIcon tintColor={iconColor} source={SendIcon} size={24} styles={[gutters.marginHorizontal_4]} onPress={onSharePress} />
            </View>
        </View>
    )
}

export default SocialConnectPostFooter;
