import { TouchableOpacity, View, Text } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import ArrowLeftIcon from "@/theme/assets/images/ArrowLeftIcon.png";
import ArrowRightIcon from "@/theme/assets/images/SocialConnect/ArrowRightIcon.png";

type Props = {
    onBack?: () => void;
    onNext?: () => void;
    isLast?: boolean;
}

function PersonalDetailsFooter({ onBack, onNext, isLast }: Props) {

    const { layout, gutters, fonts, borders } = useTheme();
    const c = colorTokens();

    function onPressBack(): void {
        if (onBack != null) {
            onBack();
        }
    }

    function onPressNext(): void {
        if (onNext != null) {
            onNext();
        }
    }

    return (
        <View>
            <View style={[layout.row, layout.itemsCenter, gutters.marginTop_16, { gap: 16 }]}>
                <TouchableOpacity onPress={onPressBack} style={[layout.row, layout.flex_1, layout.itemsCenter, layout.justifyCenter, borders.rounded_8, gutters.paddingVertical_8, gutters.paddingHorizontal_16, gutters.marginTop_4, { gap: 8, borderWidth: 1, borderColor: c.stoke.default.default, opacity: !!onBack ? 1 : 0.7 }]}>
                    <AKIcon source={ArrowLeftIcon} size={16} tintColor={c.content.default.default} />
                    <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, { color: c.content.default.default }]}>Back</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={onPressNext} style={[layout.row, layout.flex_1, layout.itemsCenter, layout.justifyCenter, borders.rounded_8, gutters.paddingVertical_8, gutters.paddingHorizontal_16, gutters.marginTop_4, { gap: 8, backgroundColor: c.fill.bold.neutrals.rest }]}>
                    <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, { color: c.content.onBold.default.default }]}>{isLast ? 'Finish' : 'Next'}</Text>
                    <AKIcon source={ArrowRightIcon} size={16} tintColor={c.custom.white} />
                </TouchableOpacity>
            </View>
        </View>
    )
}

export default PersonalDetailsFooter;