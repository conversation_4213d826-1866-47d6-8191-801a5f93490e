import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import AkinaHubCell from "../AkinaHubCell/AkinaHubCell";
import { z } from "zod";
import { moduleSchema } from "@/types/schemas/module";
import { useTranslation } from "react-i18next";
import { getTimeBasedGreeting } from "@/utils/utility";
import { User } from "@/types/models";
type Props = {
    user: User
    itemPressed: (item: z.infer<typeof moduleSchema>) => void
    moduleList: Array<z.infer<typeof moduleSchema>>
}
function AkinaHubHeader(props: Props){
    const { t } = useTranslation(['home']);
    const {
            layout,
            gutters,
            fonts,
        } = useTheme();
    const tokenColors = colorTokens()
    function renderHeaderItem({ item }: any) {
        return (
            <TouchableOpacity onPress={() => props.itemPressed(item)}>
               <AkinaHubCell item={item}/>
            </TouchableOpacity>
        );
    }

    return (
        <View style={[layout.flex_1, {backgroundColor: tokenColors.background.default.neutrals.default}]}>
            <View style={[gutters.marginHorizontal_0]}>
                <Text style={[gutters.marginTop_16, gutters.marginLeft_16,fonts.fontSizes.headings.H5, fonts.Bold, fonts.lineHeight.headings.H6, {color: tokenColors.content.default.emphasis}]}>{`${getTimeBasedGreeting()}, ${props.user?.first_name ?? ''}!`}</Text>
                <FlatList
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={[gutters.marginTop_12, { paddingBottom: 8 }]}
                    data={props.moduleList}
                    keyExtractor={(_, index) => index.toString()}
                    renderItem={renderHeaderItem}
                    horizontal
                />
            </View>
        </View>
    )
    
}
export default AkinaHubHeader