import React from 'react';
import { View, TextInput, TouchableOpacity, ViewStyle, TextStyle, StyleProp } from 'react-native';
import { ImageVariant } from '@/components/atoms';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  placeholderTextColor?: string;
  containerStyle?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  searchIcon?: any;
  clearIcon?: any;
  onClear?: () => void;
  clearButtonMode?: 'never' | 'while-editing' | 'unless-editing' | 'always';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
}

export function SearchBar({
  value,
  onChangeText,
  placeholder = "Search here",
  placeholderTextColor,
  containerStyle,
  inputStyle,
  searchIcon,
  clearIcon,
  onClear,
  clearButtonMode = 'never',
  autoCapitalize = 'none',
  autoCorrect = false
}: SearchBarProps) {
  const handleClear = () => {
    onChangeText('');
    onClear?.();
  };

  return (
    <View style={containerStyle}>
      {searchIcon && (
        <View style={{ paddingLeft: 16, paddingRight: 8 }}>
          <ImageVariant source={searchIcon} style={{ height: 20, width: 20 }} />
        </View>
      )}
      
      <TextInput
        style={[{ flex: 1, padding: 0 }, inputStyle]}
        placeholder={placeholder}
        placeholderTextColor={placeholderTextColor}
        value={value}
        onChangeText={onChangeText}
        clearButtonMode={clearButtonMode}
        autoCapitalize={autoCapitalize}
        autoCorrect={autoCorrect}
      />
      
      {value.length > 0 && clearIcon && (
        <TouchableOpacity 
          onPress={handleClear}
          style={{ paddingLeft: 8, paddingRight: 16 }}
        >
          <ImageVariant source={clearIcon} style={{ height: 20, width: 20 }} />
        </TouchableOpacity>
      )}
    </View>
  );
}