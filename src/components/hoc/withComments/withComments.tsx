import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Alert } from "react-native";
import { Comment } from "@/types/models/comment";
import { ReactElement, useRef, useState } from "react";
import addComment from "@/services/home/<USER>";
import likeUnlikeModule from "@/services/home/<USER>";
import saveUnsaveModule from "@/services/home/<USER>";
import editComment from "@/services/home/<USER>";
import deleteComment from "@/services/home/<USER>";
import addReply from "@/services/home/<USER>";
import editCommentReply from "@/services/home/<USER>";
import deleteCommentReply from "@/services/home/<USER>";
import { getObjectNameFromModule } from "@/utils/utility";
import tempAuth from "@/services/users/tempAuth";
import { useTranslation } from "react-i18next";
import { getProfile } from "@/services/users";
import { UserTypes } from "@/utils/constants";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import basicProfile from "@/services/users/basicProfile";
import ReportPostBottomSheet, { ReportingContent } from "@/components/molecules/ReportPostBottomSheet/ReportPostBottomSheet";

type EditMutationData = {
    data: {
        reply?: string;
        comment?: string;
    };
    commentId?: number;
    replyId?: number;
    replyRef?: any
};

type DeleteMutationData = {
    commentId: number;
    replyId?: number;
}

type AddMutationData = {
    comment: string;
}

type ReplyMutationData = {
    commentId: number;
    payload: {
        reply: string;
    }
}

export function withComments(Component: any, moduleName: string) {
    return (props: any) => {
        const profileResponse = useQuery({
                queryKey: ['get_basic_profile'],
                queryFn: basicProfile
        });
        const { t } = useTranslation(['home'])
        const commentRef = useRef<{editComment: (comment?: Comment) => void, makeCommentFirstResponsder: () => void}>();
        const queryClient = useQueryClient();
        const [itemId, setItemId] = useState(props.route.params.item?.id)
        const [reportingContent, setReportingContent] = useState<ReportingContent>(null)

        function onErrorMutation(error: Error): void {
            Alert.alert("Error!", error.message);
        }
        const tempAuthMutation = useMutation({
            mutationFn: () => tempAuth(),
            onSuccess: (response) => {
                props.navigation.navigate('AKWebView', {source: `${response.web_url}?paymentKey=${response.key}`})
            },
            onError: (error) => {
                Alert.alert("Error!", error.message)
            }
        })
        function showPremiumMemberPopup() {
            let options = [{text: t("home:Upgrade"), onPress: async () => {
                tempAuthMutation.mutate()
            }}, {text: t("home:Cancel"), onPress: async () => {
                
            }}]
            Alert.alert(t("home:PremiumUser"), t("home:PremiumUserDesc"), options)
        }
    
        function setCurrentItemId(id: number) {
            setItemId(id)
        }
        function onSuccessMutation(response: any) {
            queryClient.setQueryData([`${moduleName}Detail${itemId}`], (cachedData: any) => {
                if (cachedData != null) {
                    return {
                        ...cachedData,
                        is_liked: response[getObjectNameFromModule(moduleName)].is_liked,
                        is_bookmarked: response[getObjectNameFromModule(moduleName)].is_bookmarked,
                        comments: response[getObjectNameFromModule(moduleName)].comments
                    }
                }
            })
        }

        const addCommentMutation = useMutation(
            {
                mutationFn: (data: AddMutationData) => addComment(props.route.params.item.module_id, moduleName, itemId, data),
                onSuccess: (response) => onSuccessMutation(response),
                onError: onErrorMutation,
            },
        );
        const editCommentMutation = useMutation(
            {
                mutationFn: async (data: EditMutationData) => {
                    var newData = data
                    if (!!data.replyId) {
                        newData.response = await editCommentReply(props.route.params.item.module_id, moduleName, itemId, data.commentId, data.replyId, data.data)
                    } else {
                        newData.response = await editComment(props.route.params.item.module_id, moduleName, itemId, data.commentId ,data.data)
                    }
                    return data;
                },
                onSuccess: (response: any) => {
                    if(response.replyRef != null) {
                        response?.replyRef.current?.editComment(null); // response is actually replyRef returned on success
                    } else {
                        commentRef?.current?.editComment(null);
                    }
                    onSuccessMutation(response.response);
                },
                onError: onErrorMutation,
            },
        );
        const deleteCommentMutation = useMutation(
            {
                mutationFn: (data: DeleteMutationData) => {
                    if (!!data.replyId) {
                        return deleteCommentReply(props.route.params.item.module_id, moduleName, itemId, data.commentId, data.replyId)
                    } else {
                        return deleteComment(props.route.params.item.module_id, moduleName, itemId, data.commentId)
                    }
                },
                onSuccess: (response) => onSuccessMutation(response),
                onError: onErrorMutation,
            },
        );
        const likeUnlikeMutation = useMutation(
            {
                mutationFn: () => likeUnlikeModule(props.route.params.item.module_id, moduleName, itemId),
                onSuccess: (response) => onSuccessMutation(response),
                onError: onErrorMutation,
            }
        );
        const saveUnsaveMutation = useMutation(
            {
                mutationFn: () => saveUnsaveModule(props.route.params.item.module_id, moduleName, itemId),
                onSuccess: (response) => onSuccessMutation(response),
                onError: onErrorMutation,
            }
        );
        const commentReplyMutation = useMutation(
            {
                mutationFn: (data: ReplyMutationData) => addReply(props.route.params.item.module_id, moduleName, itemId, data.commentId, data.payload ),
                onSuccess: (response) => onSuccessMutation(response),
                onError: onErrorMutation,
            }
        )

        function onDeleteComment(comment: Comment) {
            if (profileResponse.data?.user.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                const data: DeleteMutationData = {
                    commentId: comment.id,
                    replyId: comment.replyId,
                }
                deleteCommentMutation.mutate(data);
            }
        }

        function onCommentReply(commentId: number, reply: string) {
            if (profileResponse.data?.user.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                const data: ReplyMutationData = {
                    commentId,
                    payload: { 
                        reply
                    }
                }
                commentReplyMutation.mutate(data);
            }
            
        }

        function onCommentSend(text: string, isEdit: boolean, comment: Comment, replyRef: any) {
            if (profileResponse.data?.user.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                if (isEdit && comment) {
                    const bodyKey = !!comment.replyId ? 'reply' : 'comment';
                    const data: EditMutationData = {
                        data: { [bodyKey]: text },
                        commentId: comment.id,
                        replyId: comment.replyId,
                        replyRef,
                    }
                    editCommentMutation.mutate(data);
                } else {
                    const data: AddMutationData = {
                        comment: text
                    }
                    addCommentMutation.mutate(data);
                }
            }
            
        }

        function onCommentButton() {
            if (profileResponse.data?.user.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                commentRef?.current?.makeCommentFirstResponsder()
            }
        }

        function onLikeButtonPressed() {
            if (profileResponse.data?.user.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                likeUnlikeMutation.mutate();
            }
        }

        function onBookmarkButtonPressed() {
            if (profileResponse.data?.user.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                saveUnsaveMutation.mutate();
            }
        }

        function onReportComment(postId: number, commentID: number, replyID?: number, userID?: number): void {
            setTimeout(() => {
                setReportingContent({
                    postID: postId,
                    commentID,
                    replyID: replyID,
                    userID: userID,
                });
            }, 500)
        }

        return (
            <AKSafeAreaView>
                <Component
                    {...props}
                    setCurrentItemId={setCurrentItemId}
                    commentRef={commentRef}
                    onDeleteComment={onDeleteComment}
                    onCommentReply={onCommentReply}
                    onCommentSend={onCommentSend}
                    onCommentButton={onCommentButton}
                    onLikeButtonPressed={onLikeButtonPressed}
                    onBookmarkButtonPressed={onBookmarkButtonPressed}
                    onReportComment={onReportComment}
                />
                <ReportPostBottomSheet
                    isModalVisible={!!reportingContent}
                    setReportingContent={setReportingContent}
                    reportingContent={reportingContent}
                    moduleInfo={{ moduleName, itemId }}
                />
            </AKSafeAreaView>
        );
    };
}
