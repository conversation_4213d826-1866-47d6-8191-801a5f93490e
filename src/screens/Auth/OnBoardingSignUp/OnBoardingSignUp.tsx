import { <PERSON><PERSON><PERSON><PERSON>, ImageVariant } from "@/components/atoms";
import { FlatList, Linking, NativeScrollEvent, NativeSyntheticEvent, ScrollView, Text, TouchableOpacity, useWindowDimensions, View } from "react-native";
import AkinaLogo from '@/theme/assets/images/AkinaLogo.png'
import { colorTokens } from "@/theme/colorTokens";
import { useTheme } from "@/theme";
import LinearGradient from "react-native-linear-gradient";
import Dots from 'react-native-dots-pagination';
import { useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import { useSafeAreaInsets } from "react-native-safe-area-context";
export type OnBoardingScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList
>;
function OnBoarding() {
    const screen = useWindowDimensions()    
    const c = colorTokens()
    const {
		layout,
		gutters,
		fonts,
		backgrounds,
	} = useTheme();
    const [currentPage, setCurrentPage] = useState(0)

    const { top, bottom } = useSafeAreaInsets();
    let data = [
        {title: 'Connect with women like you, worldwide', gradientStart: '#FDE6E9'},
        {title: 'All your music and podcasts, right here',  gradientStart: '#FFFFFF'},
        {title: 'A collection of resources and more',  gradientStart: '#FDE6E9'},
        {title: 'AkinaAI - Your Smartest Ally & Companion',  gradientStart: '#FFFFFF'}
    ]
    const navigation = useNavigation<OnBoardingScreenNavigationProp>()
    function onScroll(event: NativeSyntheticEvent<NativeScrollEvent>){
        var offsetX = event.nativeEvent.contentOffset.x,
        pageWidth = screen.width - 10;
        setCurrentPage(Math.floor((offsetX - pageWidth / 2) / pageWidth) + 1)
    }
    function renderItem({item}: any) {

        return (
            <LinearGradient 
                locations={[0,1]} 
                colors={['#FFFFFF',item.gradientStart]} 
                useAngle={true} 
                angle={0} 
                style={[layout.flex_1,layout.justifyCenter, {width: screen.width}]}>
                <View style={[gutters.marginHorizontal_16]}>
                    <ImageVariant
                        source={AkinaLogo}
                        tintColor={c.content.primary.default}
                        style={ [layout.z10, layout.itemsCenter, gutters.marginTop_24]}
                    />
                    <Text style={[gutters.marginTop_40,fonts.fontSizes.headings.H1, fonts.lineHeight.headings.H1, fonts.SemiBold, {color: c.content.default.emphasis}]}>{item.title}</Text>
                    <TouchableOpacity onPress={() => navigation.navigate('TermsAndConditions')}>
                        <Text style={[gutters.marginTop_4,fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, {color: c.content.primary.default}]}>Terms apply</Text>
                    </TouchableOpacity>
                </View>
            </LinearGradient>
        )
    }
    return (
        <View style={[layout.flex_1]}>
            <FlatList
                data={data}
                horizontal
                pagingEnabled
                renderItem={renderItem}
                onScroll={onScroll}
            />
            <View style={{position:'absolute', left:0, right:0, bottom:170}}>
                <Dots 
                    length={data.length} 
                    active={currentPage}
                    activeColor={c.background.medium.primary.default}
                    passiveColor={c.background.default.neutrals.tertiary}
                    activeDotWidth={5}
                    activeDotHeight={5}
                    passiveDotHeight={5}
                    passiveDotWidth={5}

                />
            </View>
            <AKButton
                title="Get Started"
                viewStyle={[layout.absolute ,{bottom: bottom + 10, left: 16, right: 16}]}
                borderRadius={8}
                backgroundColor={c.fill.bold.neutrals.rest}
                onPress={() => navigation.goBack()}
            />
            <TouchableOpacity onPress={() => Linking.openURL('mailto:<EMAIL>')}  style={[layout.absolute, {top: top ? top + 10 : 20, right: 20}]}>
                <Text style={[ fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.SemiBold, {color: c.content.default.default}]}>Need Help?</Text>
            </TouchableOpacity>

        </View>
    )
}
export default OnBoarding