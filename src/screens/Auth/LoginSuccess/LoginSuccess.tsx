import { Text, View } from "react-native";
import CheckIcon from "@/theme/assets/images/CheckIcon.png"
import { AKButton, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { useTranslation } from "react-i18next";
import { ApplicationScreenProps } from "@/types/navigation";
import { useHandleTokenUpdate } from "@/hooks/useHandleTokenUpdate";
import { registerNotificationForegroundHandler } from "@/integrations/fireabase/messaging";
function LoginSuccess({ navigation }: ApplicationScreenProps) {
    const {
		layout,
		gutters,
		fonts,
		backgrounds,
	} = useTheme();
    const { t } = useTranslation(['auth']);
    const handleTokenUpdate = useHandleTokenUpdate()
    
    function continueButtonTapped() {
        handleTokenUpdate();
        registerNotificationForegroundHandler();
        navigation.reset({
            index: 0,
            routes: [{name: 'Main'}]
        })
    }
    return (
        <View style={[layout.flex_1 ,layout.itemsCenter, layout.justifyCenter]}>
            <View style={[ {height: 400}]}>
                <View style={[ layout.justifyCenter, layout.itemsCenter]}>
                    <ImageVariant
                        source={CheckIcon}
                        style={ [layout.itemsCenter, gutters.marginTop_24]}
                    />
                </View>
                <View style={[layout.justifyCenter, layout.itemsCenter]}>
                    <Text style={[fonts.bold,fonts.size_24 ,gutters.marginTop_16]}>{t("auth:Success")}</Text>
                    <Text style={[gutters.marginHorizontal_32, fonts.alignCenter, gutters.marginTop_16]}>{t("auth:SuccessDescription")}</Text>
                </View>
                <View style={[layout.justifyEnd ,gutters.marginHorizontal_32, gutters.marginTop_24]}>
                    <AKButton 
                        height={48}
                        title={t("auth:Continue")}
                        onPress={continueButtonTapped}
                        borderRadius={10}
                    />
                </View>
            </View>
        </View>
    )
}
export default LoginSuccess