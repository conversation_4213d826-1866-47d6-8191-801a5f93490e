import { <PERSON><PERSON><PERSON>on, AKTextField, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { ApplicationScreenProps, ApplicationStackParamList } from "@/types/navigation";
import { VerificationCodeConstant } from "@/utils/constants";
import { RouteProp, useRoute } from "@react-navigation/native";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { CodeField, useBlurOnFulfill, useClearByFocusCell, Cursor } from "react-native-confirmation-code-field";
import Spinner from "react-native-loading-spinner-overlay";
import BackIcon from '@/theme/assets/images/BackIcon.png'
import _ from "lodash";
import { error } from "console";
import { useMutation } from "@tanstack/react-query";
import resetPassword from "@/services/auth/resetPassword";
import EyeIcon from "@/theme/assets/images/EyeIcon.png"

function ResetPassword({ navigation }: ApplicationScreenProps) {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'ResetPassword'>>();
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();
    const { t } = useTranslation(['auth']);
    const [value, setValue] = useState('')
    const [isNewPasswordEyeSelected, setIsNewPasswordEyeSelected] = useState(false)
    const [isConfirmPasswordEyeSelected, setIsConfirmPasswordEyeSelected] = useState(false)

    const ref = useBlurOnFulfill({value, cellCount: VerificationCodeConstant.CELL_COUNT});
    const [props, getCellOnLayoutHandler] = useClearByFocusCell({
        value,
        setValue,
    });
    const [isLoading, setIsLoading] = useState(false)
    const [form, setForm] = useState({
        newPassword: '',
        confirmNewPassword: ''
    })
    const [errors, setErrors] = useState({
        newPassword: null,
        confirmNewPassword: null
    })
    const resetMutation = useMutation(
        {
            mutationFn: (data: Object) => resetPassword(data),
            onSuccess: (response) => {
                setIsLoading(false)
                setValue('')
                setForm({
                    newPassword: '',
                    confirmNewPassword: ''
                })
                let options = [{text: t("auth:Ok"), onPress: async () => {
                    navigation.goBack()
                }}]
                Alert.alert(t("auth:Success"), response.message, options)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t("auth:Error"), error.message)
            }
        },
    );
    function eyeIconPressed(index: number) {
        switch (index) {
            case 0:
                setIsNewPasswordEyeSelected(!isNewPasswordEyeSelected)
                return
            case 1:
                setIsConfirmPasswordEyeSelected(!isConfirmPasswordEyeSelected)
                return
            default:
                return
        }
    }
    function renderRightEyeIcon(index: number) {
        return (
            <TouchableOpacity onPress={() => eyeIconPressed(index)}>
                <ImageVariant
                    source={EyeIcon}
                    style={[{tintColor: index == 0 ? 
                        (isNewPasswordEyeSelected ? colors.black : colors.gray200) : 
                        (isConfirmPasswordEyeSelected ? colors.black : colors.gray200)
                    }]}
                />
            </TouchableOpacity>
        )
    }
    function backButtonTapped() {
        navigation.goBack()
    }
    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});
    }
    function confirmButtonTapped() {
        if (isValidInput()) {
            let email = route.params.email
            let data = {
                otp: value,
                email: email,
                new_password: form.newPassword,
                new_password_confirmation: form.confirmNewPassword
            }
            setIsLoading(true)
            resetMutation.mutate(data)
        }

    }
    function isValidInput() {
        if (value.length != VerificationCodeConstant.CELL_COUNT) {
            Alert.alert(t("auth:Warning"), t("auth:InserVerificationCodeError"))
            return false
        } else if (form.newPassword == '') {
            setErrors({..._.set(errors, 'newPassword', t("auth:EnterNewPasswordError"))});
            return false
        } else if (form.confirmNewPassword == '') {
            setErrors({..._.set(errors, 'newPassword', null)});
            setErrors({..._.set(errors, 'confirmNewPassword', t("auth:EnterConfirmNewPasswordError"))});
            return false
        } else if (form.newPassword != form.confirmNewPassword) {
            setErrors({..._.set(errors, 'newPassword', null)});
            setErrors({..._.set(errors, 'confirmNewPassword', t("auth:PasswordNotMatchError"))});
            return false
        } else {
            setErrors({
                newPassword: null,
                confirmNewPassword: null
            })
            return true
        }
    }
    return (
        <View style={layout.flex_1}>
            <Spinner
                visible={isLoading}
            />
            <TouchableOpacity onPress={backButtonTapped}>
                <View style={[gutters.marginLeft_4, layout.justifyCenter, layout.itemsCenter, gutters.marginTop_40 ,{width: 50, height: 50}]}>
                    <ImageVariant
                        source={BackIcon}
                    />
                </View>
            </TouchableOpacity>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_16]}>
                <Text style={[fonts.size_24, fonts.bold, fonts.black]}>{t("auth:CheckYourEmail")}</Text>
                <Text style={[gutters.marginTop_16, fonts.gray200, fonts.Medium]}>{t("auth:CheckEmailDesc", {email: route.params.email})}</Text>
            </View>
            <View style={[gutters.marginHorizontal_24]}>
                <CodeField
                ref={ref}
                {...props}
                    // Use `caretHidden={false}` when users can't paste a text value, because context menu doesn't appear
                    value={value}
                    onChangeText={setValue}
                    cellCount={VerificationCodeConstant.CELL_COUNT}
                    rootStyle={[gutters.marginTop_24]}
                    keyboardType="number-pad"
                    textContentType="oneTimeCode"
                    testID="my-code-input"
                    renderCell={({index, symbol, isFocused}) => (
                    <Text
                        key={index}
                        style={[styles.cell, isFocused && borders.green50]}
                        onLayout={getCellOnLayoutHandler(index)}>
                        {symbol || (isFocused ? <Cursor/> : null)}
                    </Text>
                    )}
                
                />
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_16]}>
                <Text style={[fonts.size_24, fonts.bold, fonts.black]}>{t("auth:SetNewPasswordTitle")}</Text>
                <Text style={[gutters.marginTop_16, fonts.gray200, fonts.Medium]}>{t("auth:SetNewPasswordDesc", {email: route.params.email})}</Text>
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField rightAccessory={() => renderRightEyeIcon(0)} error={errors.newPassword} isSecure={!isNewPasswordEyeSelected} name='newPassword' label={t("auth:NewPassword")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField rightAccessory={() => renderRightEyeIcon(1)} error={errors.confirmNewPassword} isSecure={!isConfirmPasswordEyeSelected} name='confirmNewPassword' label={t("auth:ConfirmNewPassword")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
            </View>
            <View style={[layout.justifyEnd ,gutters.marginHorizontal_32, gutters.marginTop_24]}>
                <AKButton 
                    height={48}
                    title={t("auth:UpdatePassword")}
                    onPress={confirmButtonTapped}
                    borderRadius={10}
                    
                />
            </View>
           
        </View>

    )
}
const styles = StyleSheet.create({
    root: {flex: 1, padding: 20},
    title: {textAlign: 'center', fontSize: 30},
    codeFieldRoot: {marginTop: 20},
    cell: {
      width: 60,
      height: 60,
      lineHeight: 58,
      fontSize: 24,
      borderWidth: 1,
      borderRadius: 10,
      borderColor: '#00000030',
      textAlign: 'center',
    },
    focusCell: {
      borderColor: '#000',
    },
  });

export default ResetPassword