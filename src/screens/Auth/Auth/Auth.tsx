import { View, Text, KeyboardAvoidingView, ScrollView, Linking } from "react-native";
import { useTheme } from '@/theme';
import AKButton from "@/components/atoms/AKButton/AKButton";
import AuthBg from '@/theme/assets/images/AuthBg.png'
import AkinaLogo from '@/theme/assets/images/AkinaLogo.png'
import { ImageVariant } from "@/components/atoms";
import { useTranslation } from "react-i18next";
import { SignUp } from "../index";
import { useState } from "react";
import Modal from "react-native-modal";
import { REFERRAL_CODE_STORAGE_KEY } from "@/utils/constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useNavigation } from "@react-navigation/native";
import { LoginScreenNavigationProp } from "../Login/Login";
import { colorTokens } from "@/theme/colorTokens";
import { storage } from "@/App";
import { getAndClearPendingDeeplink } from "@/navigators/utility";
import { useSafeAreaInsets } from "react-native-safe-area-context";

function Auth() {
    const { t } = useTranslation(['auth']);
    const [isModalVisible, setIsModalVisible] = useState(false)
    const navigation = useNavigation<LoginScreenNavigationProp>()
    const {
		layout,
		gutters,
		fonts,
		backgrounds,
	} = useTheme();

    const c = colorTokens();
    const { top, bottom } = useSafeAreaInsets();

    function loginButtonPressed() {
        navigation.navigate('NewLogin');
    }
    function signUpButtonPressed() {
        let registerURL = `${process.env.WEB_BASE_URL}register`; 
        const pendingLink = getAndClearPendingDeeplink();
        if (pendingLink) {
            const refCode = pendingLink?.split('ref=')[1].split('&')[0];
            registerURL = `${registerURL}?ref=${refCode}&np=1`;
        }

        const savedRefCode = storage.getString(REFERRAL_CODE_STORAGE_KEY);
        if (savedRefCode) {
            registerURL = `${registerURL}?ref=${savedRefCode}&np=1`;
            storage.delete(REFERRAL_CODE_STORAGE_KEY);
        }

        navigation.navigate('AKWebView', {source: registerURL, isFromSignup: true})
    }

    function onDismissPopup() {
        setIsModalVisible(false)
    }
    return (
        <View style={[layout.flex_1,backgrounds.purple100]}>
            <View style={[layout.justifyCenter, layout.itemsCenter, { marginTop: top + 20 }]}>
                <Text style={[fonts.size_16 , { letterSpacing: 6.1}]}>{t('auth:title')}</Text>
                <ImageVariant
                    source={AkinaLogo}
                    tintColor={c.content.primary.default}
                    style={ [layout.z10, layout.itemsCenter, gutters.marginTop_24]}
                />
            </View>
            <View style={[layout.flex_1,  layout.justifyEnd, layout.itemsCenter, gutters.marginHorizontal_0]}>
                <ImageVariant
                    source={AuthBg}
                    style={[layout.flex_1, layout.z1, layout.absolute]}
                />
                <View style={[layout.z10,layout.row, layout.itemsCenter, layout.justifyCenter ,gutters.marginHorizontal_24, backgrounds.white, {height: 58, borderRadius: 29, marginBottom: bottom + 30}]}>
                    <AKButton 
                        height={48}
                        title={t("auth:Login")}
                        textStyle={[fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: c.content.onBold.default.default}]}
                        onPress={loginButtonPressed}
                        viewStyle={gutters.marginLeft_4}
                    />
                    <AKButton 
                        height={48}
                        backgroundColor={c.custom.white}
                        title={t("auth:Signup")}
                        textStyle={[fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: c.content.default.default}]}
                        onPress={signUpButtonPressed}
                    />
                </View>
            </View>

            <Modal
                isVisible={isModalVisible}
                animationIn="slideInUp"
                style={[layout.justifyEnd, gutters.margin_0]}
                swipeDirection={['up', 'left', 'right', 'down']}
                onBackdropPress={() => {
                    setIsModalVisible(false)
                }}
                avoidKeyboard={true}
            >
                <View>
                    <KeyboardAvoidingView behavior="padding">
                        <ScrollView scrollEnabled>
                            <SignUp dismissPopup={onDismissPopup}/>
                        </ScrollView>
                    </KeyboardAvoidingView>
                </View>
            </Modal>

        </View>   
    )
    

}
export default Auth