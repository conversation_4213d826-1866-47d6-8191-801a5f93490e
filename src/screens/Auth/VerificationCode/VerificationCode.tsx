import { <PERSON><PERSON><PERSON><PERSON>, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import layout from "@/theme/layout";
import { VerificationCodeConstant } from "@/utils/constants";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { CodeField, useBlurOnFulfill, useClearByFocusCell, Cursor } from "react-native-confirmation-code-field";
import BackIcon from '@/theme/assets/images/BackIcon.png'
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import type { ApplicationScreenProps, ApplicationStackParamList } from '@/types/navigation';
import { useMutation } from "@tanstack/react-query";
import verify from "@/services/auth/verify";
import Spinner from "react-native-loading-spinner-overlay";
import resendVerification from "@/services/auth/resendVerification";
import StopwatchTimer, {
    StopwatchTimerMethods,
  } from 'react-native-animated-stopwatch-timer';

function VerificationCode({ navigation }: ApplicationScreenProps) {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'VerificationCode'>>();
    const stopwatchTimerRef = useRef<StopwatchTimerMethods>(null);

    const {
		colors,
		variant,
		changeTheme,
		layout,
		gutters,
		fonts,
		components,
		backgrounds,
        borders,
	} = useTheme();
    const { t } = useTranslation(['auth']);
    const [value, setValue] = useState('')
    const ref = useBlurOnFulfill({value, cellCount: VerificationCodeConstant.CELL_COUNT});
    const [props, getCellOnLayoutHandler] = useClearByFocusCell({
        value,
        setValue,
    });
    const [isResendDisabled, setIsResendDisabled] = useState(true)
    const [isLoading, setIsLoading] = useState(false)
    const verifyMutation = useMutation(
        {
            mutationFn: (data: Object) => verify(data),
            onSuccess: (response) => {
                setIsLoading(false)
                setValue('')
                //reset navigation
                navigation.navigate('LoginSuccess')
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );
    useEffect(() => {
        stopwatchTimerRef.current?.play()
    }, [])
    const resendMutation = useMutation(
        {
            mutationFn: (data: Object) => resendVerification(data),
            onSuccess: (response) => {
                setIsLoading(false)
                stopwatchTimerRef.current?.play()
                Alert.alert("Success", response.message)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );

    function confirmButtonTapped() {
        let email = route.params.email
        let data = {
            otp: value,
            email: email
        }
        setIsLoading(true)
        verifyMutation.mutate(data)

    }
    function backButtonTapped() {
        navigation.goBack()
    }
    function resendOTPTapped() {
        setIsResendDisabled(true)
        stopwatchTimerRef.current?.reset()

        let email = route.params.email
        let data = {
            email: email
        }
        setIsLoading(true)
        resendMutation.mutate(data)

    }
    function handleTimerComplete() {
        setIsResendDisabled(false)
    }
    return (
        <View style={layout.flex_1}>
            <Spinner
                visible={isLoading}
            />
            <TouchableOpacity onPress={backButtonTapped}>
                <View style={[gutters.marginLeft_4, layout.justifyCenter, layout.itemsCenter, gutters.marginTop_40 ,{width: 50, height: 50}]}>
                    <ImageVariant
                        source={BackIcon}
                    />
                </View>
            </TouchableOpacity>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_16]}>
                <Text style={[fonts.size_24, fonts.bold]}>{t('auth:VerificationCode')}</Text>
                <Text>{t('auth:CheckVerificationCode')}</Text>
            </View>
            <View style={[gutters.marginHorizontal_16]}>
                <CodeField
                ref={ref}
                {...props}
                    // Use `caretHidden={false}` when users can't paste a text value, because context menu doesn't appear
                    value={value}
                    onChangeText={setValue}
                    cellCount={VerificationCodeConstant.CELL_COUNT}
                    rootStyle={[gutters.marginTop_24]}
                    keyboardType="number-pad"
                    textContentType="oneTimeCode"
                    testID="my-code-input"
                    renderCell={({index, symbol, isFocused}) => (
                    <Text
                        key={index}
                        style={[styles.cell, isFocused && borders.green50]}
                        onLayout={getCellOnLayoutHandler(index)}>
                        {symbol || (isFocused ? <Cursor/> : null)}
                    </Text>
                    )}
                
                />
            </View>
            <View style={[ layout.justifyCenter, layout.itemsCenter, gutters.marginTop_16]}>
                <TouchableOpacity disabled={isResendDisabled} onPress={resendOTPTapped}>
                    <Text style={[ isResendDisabled ? fonts.gray400 : fonts.green50]}>{t('auth:ResendOTP')}</Text>
                </TouchableOpacity>
            </View>
            <View style={[layout.itemsCenter, layout.justifyCenter]}>
                <StopwatchTimer ref={stopwatchTimerRef} 
                    mode="timer" 
                    initialTimeInMs={120000} 
                    trailingZeros={0} 
                    leadingZeros={2}
                    decimalSeparator=":"
                    onFinish={handleTimerComplete}
                />
                {/* <Timer totalDuration={59000} msecs start={startTimer}
                    reset={resetTimer}
                    options={{text: {
                        fontSize: 30,
                        color: colors.black,
                        marginLeft: 7,
                      }}}
                    handleFinish={handleTimerComplete}
                    
                    // getTime={this.x} 
                    /> */}
            </View>
            <View style={[layout.justifyEnd ,gutters.marginHorizontal_32, gutters.marginTop_24]}>
                <AKButton 
                    height={48}
                    backgroundColor={backgrounds.green50}
                    title={t("auth:Confirm")}
                    textStyle={[fonts.gray50]}
                    onPress={confirmButtonTapped}
                    borderRadius={10}
                    disabled={value.length != VerificationCodeConstant.CELL_COUNT}
                    
                />
            </View>
            
           
        </View>
    )
}

const styles = StyleSheet.create({
    root: {flex: 1, padding: 20},
    title: {textAlign: 'center', fontSize: 30},
    codeFieldRoot: {marginTop: 20},
    cell: {
      width: 60,
      height: 60,
      lineHeight: 58,
      fontSize: 24,
      borderWidth: 1,
      borderRadius: 10,
      borderColor: '#00000030',
      textAlign: 'center',
    },
    focusCell: {
      borderColor: '#000',
    },
  });
export default VerificationCode