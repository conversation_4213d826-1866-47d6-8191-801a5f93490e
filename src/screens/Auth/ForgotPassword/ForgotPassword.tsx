import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>TextField } from "@/components/atoms";
import { useTheme } from "@/theme";
import { Alert, Text, View } from "react-native"
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { isValidEmail } from "@/utils";
import { useMutation } from "@tanstack/react-query";
import forgotPassword from "@/services/auth/forgotPassword";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import { useNavigation } from "@react-navigation/native";
import Spinner from "react-native-loading-spinner-overlay";
import { colorTokens } from "@/theme/colorTokens";
export type ForgotScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'ResetPassword'
>;
type Props = {
    dismissPopup?: () => void
}
function ForgotPassword({dismissPopup}: Props) {
    const {
		colors,
		variant,
		changeTheme,
		layout,
		gutters,
		fonts,
		components,
		backgrounds,
        borders,
	} = useTheme();

    const c = colorTokens();

    const { t } = useTranslation(['auth']);
    const [email, setEmail] = useState('')
    const [error, setError] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const navigation = useNavigation<ForgotScreenNavigationProp>()

    const forgotMutation = useMutation(
        {
            mutationFn: (data: Object) => forgotPassword(data),
            onSuccess: (response) => {
                setIsLoading(false)
                //reset navigation
                if (dismissPopup != undefined) {
                    dismissPopup!()
                }
                navigation.navigate("ResetPassword", {email: email})
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    )
   
    function onValueChange(name: string, text: string) {
        setEmail(text)
    }
    function submitButtonPressed() {
        if (email == '') {
            setError(t("auth:EnterEmailError"))
        } else if (isValidEmail(email) == false) {
            setError(t("CorrectEmailError"))
        } else {
            setError(null)
            let data = {
                email: email
            }
            setIsLoading(true)
            forgotMutation.mutate(data)
        }
    }
    return (
        <View 
            style={[borders.rounded_16, backgrounds.white, {height: 400}]}
        >
            <Spinner
                visible={isLoading}
            />
            <Text style={[gutters.marginHorizontal_24, gutters.marginTop_24, fonts.Medium, { color: c.content.default.default}  ,fonts.size_24]} >{t("auth:ForgotPasswordTitle")}</Text>
            <Text style={[gutters.marginHorizontal_24, gutters.marginTop_24, fonts.gray200, fonts.Medium ]}>{t("auth:ForgotPasswordDescription")}</Text>
            <View style={[gutters.marginTop_24, gutters.marginHorizontal_24]}>
                <AKTextField autoCapitalize="none" keyboardType="email-address"  error={error} label={t("auth:Email")} baseColor={colors.gray200} onChangeValue={onValueChange} name="email"/>
            </View>
            <View style={[layout.flex_1, layout.justifyAround]}>
                <View style={[layout.justifyEnd ,gutters.marginHorizontal_32, gutters.marginBottom_32]}>
                    <AKButton 
                        height={48}
                        title={t("auth:Reset")}
                        onPress={submitButtonPressed}
                        borderRadius={10}
                    />
                </View>
            </View>
        </View>
    )
}

export default ForgotPassword