import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON>tField, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { Alert, Text, TouchableOpacity, View } from "react-native"
import { useTranslation } from "react-i18next";
import { useRef, useState } from "react";
import _ from "lodash";
import { isValidEmail } from "@/utils";
import {useMutation} from '@tanstack/react-query';
import register, { NewUser } from "@/services/auth/register";
import Spinner from 'react-native-loading-spinner-overlay';
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import EyeIcon from "@/theme/assets/images/EyeIcon.png"
type Props = {
    dismissPopup?: () => void
}
export type SignUpScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'VerificationCode'
>;
function SignUp({dismissPopup}: Props) {
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();
    const navigation = useNavigation<SignUpScreenNavigationProp>()
    const [isLoading, setIsLoading] = useState(false)
    const firstNameField = useRef()
    const lastNameField = useRef()
    const userNameField = useRef()
    const affiliateCodeFieldRef = useRef()
    const emailField = useRef()
    const passwordField = useRef()


    const registerMutation = useMutation(
        {
            mutationFn: (newUser: NewUser) => register(newUser),
            onSuccess: (response) => {
                setIsLoading(false)
                resetForm();
                if (dismissPopup != undefined) {
                    dismissPopup!()
                }
                navigation.navigate('VerificationCode', {email: response.user.email})
                dismissPopup!()
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );
    const [isEyeSelected, setIsEyeSelected] = useState(false)

    const [form, setForm] = useState({
        firstName: '',
        lastName: '',
        affiliateCode: '',
        password: '',
        email: '',
        userName: ''
    })
    const [errors, setErrors] = useState({
        firstName: null,
        lastName: null,
        affiliateCode: null,
        password: null,
        email: null,
        userName: null
    })

    const { t } = useTranslation(['auth']);

    function resetForm() {
        setForm({
            firstName: '',
            lastName: '',
            affiliateCode: '',
            password: '',
            email: '',
            userName: ''
        })
    }
    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});
    }
    function validateForm() {
        let isValid = true
        if(form.firstName == '') {
            setErrors({..._.set(errors, 'firstName', t('auth:EnterFirstNameError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'firstName', null)});
        }
        if(form.lastName == '') {
            setErrors({..._.set(errors, 'lastName', t('auth:EnterLastNameError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'lastName', null)});
        }
        if(form.userName == '') {
            setErrors({..._.set(errors, 'userName', t('auth:EnterUserNameError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'userName', null)});
        }
        if (form.email == '') {
            setErrors({..._.set(errors, 'email', t('auth:EnterEmailError'))});
            isValid = false
        } else if (isValidEmail(form.email) == false){
            setErrors({..._.set(errors, 'email', t('auth:CorrectEmailError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'email', null)});
        }
        if (form .password == '') {
            setErrors({..._.set(errors, 'password', t('auth:EnterPasswordError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'password', null)});
        }
        return isValid
    }
    function registerButtonPressed() {
        if (validateForm()) {
            setIsLoading(true)
            let data: any = {
                first_name: form.firstName,
                last_name: form.lastName,
                email: form.email,
                password: form.password,
                user_name: form.userName
            }
            if (form.affiliateCode != '') {
                data['affiliate_code'] = form.affiliateCode
            }
            registerMutation.mutate(data)
        }
    }
    function renderRightEyeIcon() {
        return (
            <TouchableOpacity onPress={() => setIsEyeSelected(!isEyeSelected)}>
                <ImageVariant
                    source={EyeIcon}
                    style={[{tintColor: isEyeSelected ? colors.green50 : colors.gray200}]}
                />
            </TouchableOpacity>
        )
    }
    return (
        <View 
            style={[borders.rounded_16, backgrounds.gray50, {height: 700}]}
        >
            <Spinner
                visible={isLoading}
            />
            <View style={[gutters.marginHorizontal_24, gutters.marginTop_24]}>
                <Text style={[fonts.size_32, fonts.bold, fonts.green50]}>{t("auth:Signup")}</Text>

            </View>

            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField 
                    inputRef={firstNameField}
                    name='firstName' 
                    error={errors.firstName} 
                    value={form.firstName} 
                    label={t("auth:FirstName")} 
                    tintColor={colors.green50} 
                    baseColor={colors.gray200} 
                    onChangeValue={onValueChange}
                    returnKeyType='next'
                    blurOnSubmit={false}
                    onSubmitEditing={() => lastNameField?.current?.focus()}
                />
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField 
                    inputRef={lastNameField}
                    name='lastName' 
                    error={errors.lastName} 
                    value={form.lastName} 
                    label={t("auth:LastName")} 
                    tintColor={colors.green50} 
                    baseColor={colors.gray200} 
                    onChangeValue={onValueChange}
                    returnKeyType='next'
                    blurOnSubmit={false}
                    onSubmitEditing={() => userNameField?.current?.focus()}
                />
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField 
                    inputRef={userNameField}
                    name='userName' 
                    error={errors.userName} 
                    autoCapitalize="none"
                    value={form.userName} 
                    label={t("auth:Username")} 
                    tintColor={colors.green50} 
                    baseColor={colors.gray200} 
                    onChangeValue={onValueChange}
                    returnKeyType='next'
                    blurOnSubmit={false}
                    onSubmitEditing={() => affiliateCodeFieldRef?.current?.focus()}
                />
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField 
                    inputRef={affiliateCodeFieldRef}
                    name='affiliateCode' 
                    error={errors.affiliateCode} 
                    value={form.affiliateCode} 
                    label={t("auth:AffiliateCode")} 
                    tintColor={colors.green50} 
                    baseColor={colors.gray200} 
                    onChangeValue={onValueChange}
                    returnKeyType='next'
                    blurOnSubmit={false}
                    onSubmitEditing={() => emailField?.current?.focus()}
                />
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField 
                    inputRef={emailField}
                    autoCapitalize="none" 
                    keyboardType="email-address"  
                    name='email'  
                    error={errors.email}  
                    value={form.email} 
                    label={t("auth:Email")} 
                    tintColor={colors.green50} 
                    baseColor={colors.gray200} 
                    onChangeValue={onValueChange}
                    returnKeyType='next'
                    blurOnSubmit={false}
                    onSubmitEditing={() => passwordField?.current?.focus()}
                />
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField 
                    inputRef={passwordField}
                    rightAccessory={() => renderRightEyeIcon()} 
                    name='password' 
                    isSecure={!isEyeSelected} 
                    error={errors.password} 
                    value={form.password} 
                    label={t("auth:Password")} 
                    tintColor={colors.green50} 
                    baseColor={colors.gray200} 
                    onChangeValue={onValueChange}
                />
            </View>
            <View style={[layout.flex_1, layout.justifyAround]}>
                

                <View style={[layout.justifyEnd ,gutters.marginHorizontal_32, gutters.marginBottom_0]}>
                    <AKButton 
                        height={48}
                        backgroundColor={backgrounds.green50}
                        title={t("auth:Signup")}
                        textStyle={[fonts.gray50]}
                        onPress={registerButtonPressed}
                        borderRadius={10}
                    />
                </View>

            </View>
            
            
            
            

        </View>
    )
}

export default SignUp