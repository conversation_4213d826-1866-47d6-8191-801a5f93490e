import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { Alert, Linking, TouchableOpacity, View } from "react-native"
import TextLogo from '@/theme/assets/images/TextLogo.png'
import { useTranslation } from "react-i18next";
import { useNavigation } from "@react-navigation/native";
import { useState } from "react";
import _ from "lodash";
import { isValidEmail } from "@/utils";
import { useMutation } from "@tanstack/react-query";
import login, { LoginModel } from "@/services/auth/login";
import Spinner from "react-native-loading-spinner-overlay";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import EyeIcon from "@/theme/assets/images/EyeIcon.png"
import BiometricIcon from "@/theme/assets/images/BiometricIcon.png"
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';
import { verifySchema } from "@/types/schemas/verify";
import { z } from "zod";
import registerBiometric from "@/services/auth/registerBiometric";
import verifyBiometric from "@/services/auth/verifyBiometric";
import { getAndClearPendingDeeplink } from "@/navigators/utility";
import modules from '@/services/home/<USER>';
import { mapUniversalLinkToDeepLink, parseModules } from "@/utils/utility";
import { useHandleTokenUpdate } from "@/hooks/useHandleTokenUpdate";
import { registerNotificationForegroundHandler } from "@/integrations/fireabase/messaging";
import { colorTokens } from "@/theme/colorTokens";

export type LoginScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'VerificationCode'
>;
type Props = {
    onForgotPassword?: () => void
    dismissPopup?: () => void
}
function Login({onForgotPassword, dismissPopup}: Props) {
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();

    const c = colorTokens();

    const { t } = useTranslation(['auth']);
    const navigation = useNavigation<LoginScreenNavigationProp>()
    const [isLoading, setIsLoading] = useState(false)
    const [isEyeSelected, setIsEyeSelected] = useState(false)
    const handleTokenUpdate = useHandleTokenUpdate()
    const [form, setForm] = useState({
        password: '',
        email: ''
    })
    const [errors, setErrors] = useState({
        password: null,
        email: null
    })

    async function resetNavigation() {
        handleTokenUpdate();
        registerNotificationForegroundHandler();
        navigation.reset({
            index: 0,
            routes: [{name: 'Main'} as never]
        });
        const pendingLink = getAndClearPendingDeeplink();
        if (pendingLink) {
            const response = await modules();
            const modulesMapping = parseModules(response?.modules);
            const processedUrl = mapUniversalLinkToDeepLink(pendingLink, modulesMapping);
            Linking.openURL(processedUrl);
        }
    }

    const updateBiometricMutation = useMutation({
        mutationFn: (data: any) => registerBiometric(data.userId, data.data),
        onSuccess: (response) => {
            setIsLoading(false)
            if (dismissPopup != undefined) {
                dismissPopup!()
            }
            resetNavigation();
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert(t('Error'), error.message)
            console.log("Error:::", error)
        }
    })
    const verifyBiometricMutation = useMutation({
        mutationFn: (data: any) => verifyBiometric(data.userId, data.data),
        onSuccess: (response) => {
            setIsLoading(false)
            if (response.is_verified == false) {
                if (dismissPopup != undefined) {
                    dismissPopup!()
                }
                navigation.navigate('VerificationCode', {email: form.email} )
                resetForm();
                return
            }
            if (dismissPopup != undefined) {
                dismissPopup!()
            }
            resetForm();
            resetNavigation();
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert("Error!", "You need to first enable biometric on this device after login, then you can login with biometric.")
            console.log("Error:::", error)
        }
    })
    const loginMutation = useMutation(
        {
            mutationFn: (newUser: LoginModel) => login(newUser),
            onSuccess: (response) => {
                setIsLoading(false)
                if (response.is_verified == false) {
                    if (dismissPopup != undefined) {
                        dismissPopup!()
                    }
                    navigation.navigate('VerificationCode', {email: form.email} )
                    resetForm();
                    return
                } else {
                    // resetForm();
                    askForFaceId(response)
                }
                

                // if (dismissPopup != undefined) {
                //     dismissPopup!()
                // }

                // navigation.reset({
                //     index: 0,
                //     routes: [{name: 'Main'} as never]
                // })
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );
    async function askForFaceId(response: z.infer<typeof verifySchema>) {
        const userId = response.user.id
        const storedUserId = storage.getNumber(AUTH_STORRAGE_KEYS.USERID)

        if (userId == storedUserId) {
            if (dismissPopup != undefined) {
                dismissPopup!()
            }

            resetNavigation();
            resetForm()
            return 
        }
        const rnBiometrics = new ReactNativeBiometrics();
        
        const { available, biometryType } =
          await rnBiometrics.isSensorAvailable();
        
        if (available ) {
            let typeText = biometryType == BiometryTypes.FaceID ? "Face ID" : "Touch ID"
          Alert.alert(
            `${typeText}`,
            `Would you like to enable ${typeText} authentication for the next time?`,
            [
              {
                text: 'Yes please',
                onPress: async () => {
                  const { publicKey } = await rnBiometrics.createKeys();
    
                  // `publicKey` has to be saved on the user's entity in the database
                console.log("Public Keys:::   ", publicKey)
                //   await sendPublicKeyToServer({ userId, publicKey });
                let data = {
                    data: {
                        key: publicKey
                    },
                    userId: userId
                }
                setIsLoading(true)
                updateBiometricMutation.mutate(data)
                console.log("User Id:::   ", userId)

                storage.set(AUTH_STORRAGE_KEYS.USERID, userId)

                  // save `userId` in the local storage to use it during Face ID authentication
                //   await AsyncStorage.setItem('userId', userId);
                },
              },
              { text: 'Cancel', style: 'cancel', onPress: () => {
                if (dismissPopup != undefined) {
                    dismissPopup!()
                }
                resetForm()
                resetNavigation();
              } },
            ],
          );
        } else {
            if (dismissPopup != undefined) {
                dismissPopup!()
            }
            resetForm()
            resetNavigation();
        }
    
    }
    function renderRightEyeIcon() {
        return (
            <TouchableOpacity onPress={() => setIsEyeSelected(!isEyeSelected)}>
                <ImageVariant
                    source={EyeIcon}
                    style={[{tintColor: isEyeSelected ? c.content.primary.default : colors.gray200}]}
                />
            </TouchableOpacity>
        )
    }
    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});
    }
    function resetForm() {
        setForm({
            password: '',
            email: ''
        })
    }
    function loginButtonPressed() {
        if (validateForm()) {
            setIsLoading(true)
            loginMutation.mutate(form)
        }
    }
    function validateForm() {
        let isValid = true
        if (form.email == '') {
            setErrors({..._.set(errors, 'email', t('auth:EnterEmailError'))});
            isValid = false
        } else if (isValidEmail(form.email) == false){
            setErrors({..._.set(errors, 'email', t('auth:CorrectEmailError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'email', null)});
        }
        if (form .password == '') {
            setErrors({..._.set(errors, 'password', t('auth:EnterPasswordError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'password', null)});
        }
        return isValid
    }
    async function verifyBiometricOnServer() {
        const rnBiometrics = new ReactNativeBiometrics();

        const { available, biometryType, error } =
          await rnBiometrics.isSensorAvailable();
      
        if (!available ) {
            let typeText = biometryType == BiometryTypes.FaceID ? 'Face ID' : 'Touch ID'
          Alert.alert(
            'Oops!',
            `${typeText} is not available on this device. Please visit settings to enable it`,
            [{
                text: 'Settings',
                onPress: () => Linking.openSettings()
            }]
          );
          return;
        }
      
        const userId = storage.getNumber(AUTH_STORRAGE_KEYS.USERID)
        console.log("Storage User Id:::   ", userId)

        if (!userId) {
          Alert.alert(
            'Oops!',
            'You have to sign in using your credentials first to enable Face ID.',
          );
          return;
        }
      
        const timestamp = Math.round(
          new Date().getTime() / 1000,
        ).toString();
        const payload = `${userId}__${timestamp}`;
      
        const { success, signature } = await rnBiometrics.createSignature(
          {
            promptMessage: 'Sign in',
            payload,
          },
        );
      
        if (!success) {
          Alert.alert(
            'Oops!',
            'Something went wrong during authentication with Face ID. Please try again.',
          );
          return;
        }
        console.log("payload::::: ", payload)
        console.log("Signature::::: ", signature)
        let data = {
            data: {
                signature: signature,
                payload: payload
            },
            userId: userId
        }
        setIsLoading(true)
        verifyBiometricMutation.mutate(data)
        // const { status, message } = await verifySignatureWithServer({
        //   signature,
        //   payload,
        // });
      
        // if (status !== 'success') {
        //   Alert.alert('Oops!', message);
        //   return;
        // }
      
        // Alert.alert('Success!', 'You are successfully authenticated!');
    }
    return (
        <View 
            style={[borders.rounded_16, backgrounds.white, {height: 600}]}
        >
            <Spinner
                visible={isLoading}
            />
            <View style={[gutters.marginTop_32, gutters.marginHorizontal_24]}>
                <AKTextField error={errors.email} autoCapitalize="none" keyboardType="email-address"  name='email' label={t("auth:Email")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
            </View>
            <View style={[gutters.marginTop_16, gutters.marginHorizontal_24]}>
                <AKTextField rightAccessory={() => renderRightEyeIcon()} error={errors.password} isSecure={!isEyeSelected} name='password' label={t("auth:Password")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
            </View>
            <View style={[gutters.marginHorizontal_24, layout.itemsEnd, {height: 30} ]}>
                <AKButton
                    title={t("auth:ForgotPasswordButtonTitle")}
                    textStyle={[fonts.bold, fonts.black]}
                    backgroundColor={c.custom.white}
                    height={30}
                    onPress={onForgotPassword}
                />

            </View>
            <TouchableOpacity onPress={verifyBiometricOnServer}>
                <View style={[ layout.justifyCenter, layout.itemsCenter]}>
                    <ImageVariant
                        source={BiometricIcon}
                        tintColor={c.content.primary.default}
                        style={ [layout.z10 ,layout.itemsCenter, gutters.marginTop_24, {width: 50, height: 50}]}
                    />

                </View>
            </TouchableOpacity>
            
            <View style={[layout.flex_1, layout.justifyAround]}>
                <View style={[ layout.justifyCenter, layout.itemsCenter]}>
                    <ImageVariant
                        source={TextLogo}
                        tintColor={c.content.primary.default}
                        style={ [layout.z10, layout.itemsCenter, gutters.marginTop_24]}
                    />

                </View>
                <View style={[layout.justifyEnd ,gutters.marginHorizontal_32, gutters.marginBottom_24]}>
                    <AKButton 
                        height={48}
                        title={t("auth:Login")}
                        onPress={loginButtonPressed}
                        borderRadius={10}
                    />
                </View>
            </View>
        </View>
    )
}

export default Login