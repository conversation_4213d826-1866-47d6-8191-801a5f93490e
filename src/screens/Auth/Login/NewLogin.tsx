import { storage } from "@/App";
import { AKTextInput, ImageVariant } from "@/components/atoms";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { getAndClearPendingDeeplink } from "@/navigators/utility";
import { useTheme } from "@/theme";
import NavBarLogo from "@/theme/assets/images/NavBarLogo.png"
import { colorTokens } from "@/theme/colorTokens";
import { REFERRAL_CODE_STORAGE_KEY } from "@/utils/constants";
import { useNavigation } from "@react-navigation/native";
import { useState } from "react";
import { Alert, Linking, Text, TouchableOpacity, View } from "react-native";
import Modal from "react-native-modal";
import ForgotPassword from "@/screens/Auth/ForgotPassword/ForgotPassword";
import _ from "lodash";
import { isValidEmail } from "@/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import login, { LoginModel } from "@/services/auth/login";
import Spinner from "react-native-loading-spinner-overlay";
import { useHandleTokenUpdate } from "@/hooks/useHandleTokenUpdate";
import { registerNotificationForegroundHandler } from "@/integrations/fireabase/messaging";
import modules from "@/services/home/<USER>";
import { mapUniversalLinkToDeepLink, parseModules } from "@/utils/utility";
import { verifySchema } from "@/types/schemas/verify";
import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';
import { z } from "zod";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import registerBiometric from "@/services/auth/registerBiometric";
import { useTranslation } from "react-i18next";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import FaceIDIcon from "@/theme/assets/images/FaceIDIcon.png";
import verifyBiometric from "@/services/auth/verifyBiometric";

function NewLogin() {

    const { t } = useTranslation(['auth']);
    const c = colorTokens();
    const { layout, gutters, fonts, borders } = useTheme();

    const handleTokenUpdate = useHandleTokenUpdate();
    const queryClient = useQueryClient()

    const navigation = useNavigation();

    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [form, setForm] = useState({
        password: '',
        email: ''
    });

    const loginMutation = useMutation(
        {
            mutationFn: (newUser: LoginModel) => login(newUser),
            onSuccess: (response) => {
                setIsLoading(false)
                if (response.is_verified == false) {
                    navigation.navigate('VerificationCode', {email: form.email} )
                    resetForm();
                    return
                } else {
                    askForFaceId(response)
                }
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );

    const updateBiometricMutation = useMutation({
        mutationFn: (data: any) => registerBiometric(data.userId, data.data),
        onSuccess: () => {
            setIsLoading(false)
            resetNavigation();
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert(t('Error'), error.message)
            console.log("Error:::", error)
        }
    })

    const verifyBiometricMutation = useMutation({
        mutationFn: (data: any) => verifyBiometric(data.userId, data.data),
        onSuccess: (response) => {
            setIsLoading(false)
            if (response.is_verified == false) {
                navigation.navigate('VerificationCode', {email: form.email} )
                resetForm();
                return
            }
            resetForm();
            resetNavigation();
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert("Error!", "You need to first enable biometric on this device after login, then you can login with biometric.")
            console.log("Error:::", error)
        }
    })

    function resetForm() {
        setForm({
            password: '',
            email: ''
        })
    }

    async function resetNavigation() {
        handleTokenUpdate();
        registerNotificationForegroundHandler();
        queryClient.setDefaultOptions({
            queries: {
                enabled: true, // Disable all queries
                retry: false,
                refetchOnMount: true,
                refetchOnWindowFocus: true,
                refetchOnReconnect: true,
            },
        })
        navigation.reset({
            index: 0,
            routes: [{name: 'Main'} as never]
        });
        const pendingLink = getAndClearPendingDeeplink();
        if (pendingLink) {
            const response = await modules();
            const modulesMapping = parseModules(response?.modules);
            const processedUrl = mapUniversalLinkToDeepLink(pendingLink, modulesMapping);
            Linking.openURL(processedUrl);
        }
    }

    async function askForFaceId(response: z.infer<typeof verifySchema>) {
        const userId = response.user.id
        const storedUserId = storage.getNumber(AUTH_STORRAGE_KEYS.USERID)

        if (userId == storedUserId) {
            resetNavigation();
            resetForm()
            return 
        }
        const rnBiometrics = new ReactNativeBiometrics();
        
        const { available, biometryType } =
            await rnBiometrics.isSensorAvailable();
        
        if (available ) {
            let typeText = biometryType == BiometryTypes.FaceID ? "Face ID" : "Touch ID"
            Alert.alert(
                `${typeText}`,
                `Would you like to enable ${typeText} authentication for the next time?`,
                [
                    {
                        text: 'Yes please',
                        onPress: async () => {
                            const { publicKey } = await rnBiometrics.createKeys();
                            let data = {
                                data: {
                                    key: publicKey
                                },
                                userId: userId
                            }
                            setIsLoading(true)
                            updateBiometricMutation.mutate(data)
                            storage.set(AUTH_STORRAGE_KEYS.USERID, userId)
                        },
                    },
                    {
                        text: 'Cancel', style: 'cancel', onPress: () => {
                            resetForm()
                            resetNavigation();
                        }
                    },
                ],
            );
        } else {
            resetForm()
            resetNavigation();
        }
    
    }

    function onPressSignUp() {
        let registerURL = `${process.env.WEB_BASE_URL}register`; 
        const pendingLink = getAndClearPendingDeeplink();
        if (pendingLink) {
            const refCode = pendingLink?.split('ref=')[1].split('&')[0];
            registerURL = `${registerURL}?ref=${refCode}&np=1`;
        }

        const savedRefCode = storage.getString(REFERRAL_CODE_STORAGE_KEY);
        if (savedRefCode) {
            registerURL = `${registerURL}?ref=${savedRefCode}&np=1`;
            storage.delete(REFERRAL_CODE_STORAGE_KEY);
        }

        navigation.navigate('AKWebView', {source: registerURL, isFromSignup: true})
    }

    function onPressForgotPassword() {
        setIsModalVisible(true)
    }

    function onCloseBottomSheet() {
        setIsModalVisible(false)
    }

    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});
    }

    function onPressLogin() {
        console.log(form)
        setIsLoading(true)
        loginMutation.mutate(form)
    }

    async function verifyBiometricOnServer() {
        const rnBiometrics = new ReactNativeBiometrics();

        const { available, biometryType, error } =
            await rnBiometrics.isSensorAvailable();
        
        if (!available ) {
            let typeText = biometryType == BiometryTypes.FaceID ? 'Face ID' : 'Touch ID'
            Alert.alert(
            'Oops!',
            `${typeText} is not available on this device. Please visit settings to enable it`,
            [{
                text: 'Settings',
                onPress: () => Linking.openSettings()
            }]
            );
            return;
        }
        
        const userId = storage.getNumber(AUTH_STORRAGE_KEYS.USERID)
        console.log("Storage User Id:::   ", userId)

        if (!userId) {
            Alert.alert(
            'Oops!',
            'You have to sign in using your credentials first to enable Face ID.',
            );
            return;
        }
        
        const timestamp = Math.round(new Date().getTime() / 1000,).toString();
        const payload = `${userId}__${timestamp}`;
        
        const { success, signature } = await rnBiometrics.createSignature({
            promptMessage: 'Sign in',
            payload,
        });
        
        if (!success) {
            Alert.alert('Oops!', 'Something went wrong during authentication with Face ID. Please try again.');
            return;
        }
        let data = {
            data: {
                signature: signature,
                payload: payload
            },
            userId: userId
        }
        setIsLoading(true)
        verifyBiometricMutation.mutate(data)
    }

    const isLoginDisabled = !form.email || !form.password || isValidEmail(form.email) == false;

    return (
        <AKSafeAreaView edges={['top', 'bottom']} style={[layout.itemsCenter]}>
            <Spinner
                visible={isLoading}
            />
            <ImageVariant tintColor={c.content.primary.default} source={NavBarLogo} style={[gutters.marginTop_32, {height: 25, resizeMode: 'contain'}]}/>
            <Text style={[fonts.fontSizes.headings.H4, fonts.lineHeight.headings.H4, fonts.Bold, {color: c.content.default.default, marginVertical: 48}]}>Log in to your account</Text>
            <View style={[layout.flex_1, gutters.paddingHorizontal_16, { width: '100%' }]}>
                <View style={{ gap: 8}}>
                    <AKTextInput label="Email" autoCapitalize="none" keyboardType="email-address" onChangeValue={onValueChange} name="email" />
                    <AKTextInput label="Password" isPassword={true} onChangeValue={onValueChange} name="password" />
                    <View style={[layout.itemsEnd]}>
                        <TouchableOpacity onPress={onPressForgotPassword} style={[gutters.paddingVertical_8]}>
                            <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, {color: c.content.default.default}]}>Forgot Password?</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <TouchableOpacity disabled={isLoginDisabled} onPress={onPressLogin} style={[layout.justifyCenter, layout.itemsCenter, gutters.marginTop_20, gutters.paddingVertical_12, borders.rounded_8, {backgroundColor: isLoginDisabled ? 'lightgray' : c.fill.bold.neutrals.rest}]}>
                    <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, {color: c.content.onBold.default.default}]}>Login</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={verifyBiometricOnServer} style={[layout.row, layout.justifyCenter, layout.itemsCenter, gutters.marginTop_16, gutters.paddingVertical_12, borders.rounded_8, { borderWidth: 1, borderColor: c.stoke.default.default }]}>
                    <AKIcon source={FaceIDIcon} styles={[gutters.marginRight_8]}/>
                    <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, {color: c.content.default.default}]}>Login with Face ID</Text>
                </TouchableOpacity>
            </View>
            <View style={[layout.row, layout.justifyCenter, gutters.marginBottom_16]}>
                <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Medium, {color: c.content.default.default}]}>Are you new on Akina?</Text>
                <TouchableOpacity onPress={onPressSignUp} style={[gutters.marginLeft_4]}>
                    <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, {color: c.content.info.default, textDecorationLine: 'underline'}]}>Sign Up</Text>
                </TouchableOpacity>
            </View>
            <Modal
                isVisible={isModalVisible}
                animationIn="slideInUp"
                style={[layout.justifyEnd, gutters.margin_0]}
                swipeDirection={['up', 'left', 'right', 'down']}
                onBackdropPress={() => {
                    setIsModalVisible(false)
                }}
                avoidKeyboard={true}
            >
                <View>
                    <ForgotPassword dismissPopup={onCloseBottomSheet}/>
                </View>
            </Modal>
        </AKSafeAreaView>
    )
}

export default NewLogin