import getUserNotifications from "@/services/notifications/getUserNotifications";
import { useTheme } from "@/theme";
import { notificationSchema } from "@/types/schemas/notificationSchema";
import { timeSince } from "@/utils";
import { useInfiniteQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActivityIndicator, FlatList, Linking, RefreshControl, Text, TouchableOpacity, View } from "react-native";
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
import _Icon from 'react-native-vector-icons/MaterialIcons';
import { getDeeplinkingScheme, isEmptyObjectOrArray, updateBadgeCount } from "@/utils/utility";

function Notifications() {
    const { t } = useTranslation(['sideTabs']);
    const queryClient = useQueryClient();
    const notificationListingResponse = useInfiniteQuery({
        queryKey: [`notifications`],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            return getUserNotifications(pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,

    });
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [isLoading, setIsLoading] = useState(false)
    
    const flattenData = useMemo(() => {
        return notificationListingResponse.data?.pages.flatMap(page => page.data) || [];
    }, [notificationListingResponse.data, notificationListingResponse.isRefetching]);

    useEffect(() => {
        if (notificationListingResponse.isRefetching) {
            return;
        }
        let unreadCount = 0;
        notificationListingResponse.data?.pages?.forEach(page => {
            page.data.forEach(notif => {
                if (!notif.is_read) {
                    unreadCount += 1;
                }
            })
        });
        if (unreadCount > 0) {
            queryClient.setQueryData(['get_profile'], (cacheData: any) => {
                const countDifference = cacheData?.user_profile?.notifications_count - unreadCount;
                const notifCount = countDifference < 0 ? 0 : countDifference;
                return {
                    ...cacheData,
                    user_profile: {
                        ...cacheData.user_profile,
                        notifications_count: notifCount,
                    }
                }
            });
            updateBadgeCount(unreadCount);
        }
    }, [notificationListingResponse.data]);

    const {
        layout,
        gutters,
        borders,
        colors,
        backgrounds,
        fonts
    } = useTheme();
    const loadNext = useCallback(() => {
        notificationListingResponse.hasNextPage && notificationListingResponse.fetchNextPage();
    }, [notificationListingResponse.fetchNextPage, notificationListingResponse.hasNextPage]);
    const onRefresh = useCallback(() => {
        if (!isRefreshing) {
            setIsRefreshing(true);
            notificationListingResponse.refetch()
            .then(() => setIsRefreshing(false))
            .catch(() => setIsRefreshing(false));
        }
    }, [isRefreshing, notificationListingResponse.refetch]); 
    function renderItem({item}: any) {
        let currentItem = item as z.infer<typeof notificationSchema>

        function onPress() {
            let data = currentItem?.data;
            if (typeof data == 'string') {
                data = JSON.parse(data)
            }
            if (isEmptyObjectOrArray(data)){
                return;
            }
            let url = data?.url;
            if (data.comment_id != null) {
                url = url + '/true'
            }
            if (url) {
                Linking.openURL(getDeeplinkingScheme(url));
            }
            queryClient.setQueryData(['notifications'], (cacheData: any) => {
                return {
                    ...cacheData,
                    pages: cacheData?.pages?.map(page => ({
                            ...page,
                            data: page?.data?.map(item =>
                                item.id === currentItem?.id ? {
                                    ...item,
                                    is_read: true 
                                } : item
                            )
                        })
                    ),
                };
            })
        }

        return (
            <TouchableOpacity onPress={onPress} style={[gutters.marginVertical_12, gutters.marginHorizontal_24]}>
                <View style={[layout.row, layout.itemsCenter, layout.justifyBetween, gutters.marginBottom_4]}>
                    <View style={[layout.flex_1]}>
                        <Text style={[fonts.size_24, fonts.bold]}>{currentItem.title}</Text>
                    </View>
                    {
                        !currentItem?.is_read && (
                            <_Icon style={[gutters.marginLeft_4]} name='mark-as-unread' size={20} color={colors.gray800}/>
                        )
                    }
                </View>
                <Text style={[gutters.marginBottom_8]}>{currentItem.body}</Text>
                <View style={[layout.flex_1, layout.itemsEnd, gutters.marginBottom_8]}>
                    <Text style={[fonts.gray400]}>{timeSince(new Date(currentItem.created_at))}</Text>
                </View>
                <View style={[backgrounds.gray200, {height: 1}]} />
            </TouchableOpacity>)
    }
    // NoNotifications
    return (
        <View style={[layout.flex_1]}>
            <Spinner
                visible={notificationListingResponse.isFetching }
            />
            
            <View style={[layout.flex_1]}>
            {(notificationListingResponse?.data?.pages[0].total == 0 || notificationListingResponse?.data?.pages[0].total == null) && notificationListingResponse.isFetching == false && 
                <View style={[layout.flex_1,layout.justifyCenter, layout.itemsCenter]}>
                    <Text>{t('sideTabs:NoNotifications')}</Text>
                </View>
            }
                <FlatList 
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{paddingBottom: 20}} 
                    data={notificationListingResponse.data?.pages[0].total != 0 ? flattenData : []}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                    onEndReached={loadNext}
                    removeClippedSubviews={true}
                    ListFooterComponent={
                        <View style={[layout.row,layout.justifyCenter, layout.itemsCenter ,{height: 100}]}>
                        {notificationListingResponse.isFetchingNextPage && <ActivityIndicator />}
                        </View>
                    }
                />
            </View>
        </View>
    )
}
export default Notifications

