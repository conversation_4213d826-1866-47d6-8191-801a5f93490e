export function getNotificationsSectionData(notificationListing) {
    const data = notificationListing.flatMap(page => page.data) || [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const sections = {
        today: [],
        last7: [],
        last30: [],
        older: [],
    };

    data.forEach(item => {
        const itemDate = new Date(item.created_at);
        const itemDateStart = new Date(itemDate);
        itemDateStart.setHours(0, 0, 0, 0);

        const diffDays = Math.floor((today - itemDateStart) / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
        sections.today.push(item);
        } else if (diffDays <= 6) {
        sections.last7.push(item);
        } else if (diffDays <= 29) {
        sections.last30.push(item);
        } else {
        sections.older.push(item);
        }
    });

    const result = [];

    if (sections.today.length)
        result.push({ title: 'Today', data: sections.today });

    if (sections.last7.length)
        result.push({ title: 'Last 7 Days', data: sections.last7 });

    if (sections.last30.length)
        result.push({ title: 'Last 30 Days', data: sections.last30 });

    if (sections.older.length)
        result.push({ title: 'Older', data: sections.older });

    return result;
};
