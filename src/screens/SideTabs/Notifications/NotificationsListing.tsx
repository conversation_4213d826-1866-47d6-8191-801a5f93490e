import getUserNotifications from "@/services/notifications/getUserNotifications";
import { useTheme } from "@/theme";
import { useInfiniteQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActivityIndicator, RefreshControl, Text, View, SectionList } from "react-native";
import Spinner from "react-native-loading-spinner-overlay";
import _Icon from 'react-native-vector-icons/MaterialIcons';
import { updateBadgeCount } from "@/utils/utility";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import AKScreenHeader from "@/components/atoms/AKScreenHeader/AKScreenHeader";
import { getNotificationsSectionData } from "./utility";
import NotificationItem from "@/components/molecules/NotificationItem/NotificationItem";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import NoNotificationsPlaceholder from "@/theme/assets/images/SocialConnect/NoNotificationsPlaceholder.png";

function NotificationsListing() {
    const { t } = useTranslation(['sideTabs']);
    const queryClient = useQueryClient();
    const notificationListingResponse = useInfiniteQuery({
        queryKey: [`notifications`],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            return getUserNotifications(pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,

    });
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [initialNextCalled, setInitialNextCalled] = useState(false);
    
    const flattenSectionData = useMemo(() => {
        return getNotificationsSectionData(notificationListingResponse.data?.pages || []);
    }, [notificationListingResponse.data, notificationListingResponse.isRefetching]);

    useEffect(() => {
        if (notificationListingResponse.isRefetching) {
            return;
        }
        let unreadCount = 0;
        notificationListingResponse.data?.pages?.forEach(page => {
            page.data.forEach(notif => {
                if (!notif.is_read) {
                    unreadCount += 1;
                }
            })
        });
        if (unreadCount > 0) {
            queryClient.setQueryData(['get_profile'], (cacheData: any) => {
                const countDifference = cacheData?.user_profile?.notifications_count - unreadCount;
                const notifCount = countDifference < 0 ? 0 : countDifference;
                return {
                    ...cacheData,
                    user_profile: {
                        ...cacheData.user_profile,
                        notifications_count: notifCount,
                    }
                }
            });
            updateBadgeCount(unreadCount);
        }
        if (
            !notificationListingResponse.isFetching &&
            notificationListingResponse.data?.pages.length === 1 &&
            notificationListingResponse.hasNextPage &&
            !initialNextCalled
        ) {
            setInitialNextCalled(true);
            notificationListingResponse.fetchNextPage();
        }
    }, [notificationListingResponse.data]);

    const {
        layout,
        gutters,
        fonts
    } = useTheme();

    const loadNext = useCallback(() => {
        notificationListingResponse.hasNextPage && notificationListingResponse.fetchNextPage();
    }, [notificationListingResponse.fetchNextPage, notificationListingResponse.hasNextPage]);

    const onRefresh = useCallback(() => {
        if (!isRefreshing) {
            setIsRefreshing(true);
            notificationListingResponse.refetch()
            .then(() => setIsRefreshing(false))
            .catch(() => setIsRefreshing(false));
        }
    }, [isRefreshing, notificationListingResponse.refetch]); 

    function renderItem({item}: any) {
        return (
            <NotificationItem item={item} />
        )
    }

    function ListEmptyComponent() {
        return (
            <View style={[layout.flex_1, { marginBottom: 150 }]}>
                <EmptyDataView
                    heading="All clear for now!"
                    desc="No notifications right now. Check back later for updates and activity."
                    image={NoNotificationsPlaceholder}
                />
            </View>
        )
    }

    return (
        <AKSafeAreaView edges={['top', 'bottom']}>
            <Spinner
                visible={notificationListingResponse.isLoading }
            />
            <AKScreenHeader text="Notifications" />
            <View style={[layout.flex_1]}>
                <SectionList 
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={[gutters.marginHorizontal_12, { flexGrow: 1 }]} 
                    sections={flattenSectionData}
                    // sections={[]}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    renderSectionHeader={({ section: { title } }) => (
                        <View style={[gutters.marginBottom_24]}>
                            <Text style={[fonts.fontSizes.headings.H5, fonts.lineHeight.headings.H5, fonts.Bold]}>{title}</Text>
                        </View>
                      )}
                    refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                    onEndReached={loadNext}
                    removeClippedSubviews={true}
                    ListEmptyComponent={ListEmptyComponent}
                    ListFooterComponent={
                        <View style={[layout.row,layout.justifyCenter, layout.itemsCenter]}>
                            {notificationListingResponse.isFetchingNextPage && (
                                <View style={[gutters.marginBottom_12]}>
                                    <ActivityIndicator />
                                </View>
                            )}
                        </View>
                    }
                    stickySectionHeadersEnabled={false}
                />
            </View>
        </AKSafeAreaView>
    )
}
export default NotificationsListing;
