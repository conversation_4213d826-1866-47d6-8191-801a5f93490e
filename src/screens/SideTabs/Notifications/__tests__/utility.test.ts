 import { getNotificationsSectionData } from '../utility';

describe('Notifications Utility Functions', () => {
  describe('getNotificationsSectionData', () => {
    // Mock the current date to ensure consistent test results
    const realDate = Date;
    const mockDate = new Date(2023, 0, 15); // January 15, 2023

    beforeAll(() => {
      global.Date = class extends Date {
        constructor(date) {
          if (date) {
            return super(date);
          }
          return mockDate;
        }
        static now() {
          return mockDate.getTime();
        }
      } as any;
    });

    afterAll(() => {
      global.Date = realDate;
    });

    it('should return empty array when notification listing is empty', () => {
      const result = getNotificationsSectionData([]);
      expect(result).toEqual([]);
    });

    it('should return empty array when notification listing has no data', () => {
      const result = getNotificationsSectionData([{ data: [] }]);
      expect(result).toEqual([]);
    });

    it('should categorize today\'s notifications correctly', () => {
      const notifications = [
        {
          data: [
            { id: '1', created_at: '2023-01-15T10:00:00Z' },
            { id: '2', created_at: '2023-01-15T14:30:00Z' }
          ]
        }
      ];

      const result = getNotificationsSectionData(notifications);
      
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Today');
      expect(result[0].data).toHaveLength(2);
      expect(result[0].data.map(item => item.id)).toEqual(['1', '2']);
    });

    it('should categorize last 7 days notifications correctly', () => {
      const notifications = [
        {
          data: [
            { id: '1', created_at: '2023-01-10T10:00:00Z' }, // 5 days ago
            { id: '2', created_at: '2023-01-12T14:30:00Z' }  // 3 days ago
          ]
        }
      ];

      const result = getNotificationsSectionData(notifications);
      
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Last 7 Days');
      expect(result[0].data).toHaveLength(2);
      expect(result[0].data.map(item => item.id)).toEqual(['1', '2']);
    });

    it('should categorize last 30 days notifications correctly', () => {
      const notifications = [
        {
          data: [
            { id: '1', created_at: '2022-12-20T10:00:00Z' }, // 26 days ago
            { id: '2', created_at: '2022-12-25T14:30:00Z' }  // 21 days ago
          ]
        }
      ];

      const result = getNotificationsSectionData(notifications);
      
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Last 30 Days');
      expect(result[0].data).toHaveLength(2);
      expect(result[0].data.map(item => item.id)).toEqual(['1', '2']);
    });

    it('should categorize older notifications correctly', () => {
      const notifications = [
        {
          data: [
            { id: '1', created_at: '2022-12-10T10:00:00Z' }, // 36 days ago
            { id: '2', created_at: '2022-11-15T14:30:00Z' }  // 61 days ago
          ]
        }
      ];

      const result = getNotificationsSectionData(notifications);
      
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Older');
      expect(result[0].data).toHaveLength(2);
      expect(result[0].data.map(item => item.id)).toEqual(['1', '2']);
    });

    it('should handle notifications from multiple pages', () => {
      const notifications = [
        {
          data: [
            { id: '1', created_at: '2023-01-15T10:00:00Z' }, // Today
            { id: '2', created_at: '2023-01-10T14:30:00Z' }  // Last 7 days
          ]
        },
        {
          data: [
            { id: '3', created_at: '2022-12-20T10:00:00Z' }, // Last 30 days
            { id: '4', created_at: '2022-12-10T14:30:00Z' }  // Older
          ]
        }
      ];

      const result = getNotificationsSectionData(notifications);
      
      expect(result).toHaveLength(4);
      expect(result[0].title).toBe('Today');
      expect(result[0].data).toHaveLength(1);
      expect(result[0].data[0].id).toBe('1');
      
      expect(result[1].title).toBe('Last 7 Days');
      expect(result[1].data).toHaveLength(1);
      expect(result[1].data[0].id).toBe('2');
      
      expect(result[2].title).toBe('Last 30 Days');
      expect(result[2].data).toHaveLength(1);
      expect(result[2].data[0].id).toBe('3');
      
      expect(result[3].title).toBe('Older');
      expect(result[3].data).toHaveLength(1);
      expect(result[3].data[0].id).toBe('4');
    });

    it('should only include sections that have notifications', () => {
      const notifications = [
        {
          data: [
            { id: '1', created_at: '2023-01-15T10:00:00Z' }, // Today
            { id: '2', created_at: '2022-12-10T14:30:00Z' }  // Older
          ]
        }
      ];

      const result = getNotificationsSectionData(notifications);
      
      expect(result).toHaveLength(2);
      expect(result[0].title).toBe('Today');
      expect(result[1].title).toBe('Older');
      // No 'Last 7 Days' or 'Last 30 Days' sections
    });
  });
});