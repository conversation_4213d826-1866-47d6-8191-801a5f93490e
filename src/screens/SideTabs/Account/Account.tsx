import AccountSection, { ProfileItem } from "@/components/molecules/AccountSection/AccountSection";
import { deleteUser, getProfile } from "@/services/users";
import updateProfile from "@/services/users/updateProfile";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { useActionSheet } from "@expo/react-native-action-sheet";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Image, Platform, ScrollView, Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import Spinner from "react-native-loading-spinner-overlay";
import { getAccountSectionItems } from "./utility";
import tempAuth from "@/services/users/tempAuth";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { storage } from "@/App";
import { revokeFcmToken } from "@/integrations/fireabase/messaging";
import { useQueryClient } from "@tanstack/react-query";
import { AKFastImage } from "@/components/atoms";

export type AccountNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'OnBoarding',
    'ChangePassword'
>;
function Account() {
    const { t } = useTranslation(['account', 'home']);
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();
    const queryClient = useQueryClient()

    const navigation = useNavigation<AccountNavigationProp>()
    const {data, refetch } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const { showActionSheetWithOptions } = useActionSheet();
    const [isLoading, setIsLoading] = useState(false)
    const [profileLink, setProfileLink] = useState<string | undefined | null>(null)
    const updateMutation = useMutation(
        {
            mutationFn: (data: any) => updateProfile(data),
            onSuccess: (response) => {
                setIsLoading(false)
                refetch()
                
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );
    const tempAuthMutation = useMutation({
        mutationFn: () => tempAuth(),
        onSuccess: (response) => {
            setIsLoading(false)
            navigation.navigate('AKWebView', {source: `${response.web_url}?paymentKey=${response.key}`})
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert("Error!", error.message)
        }
    })
    
    const accountSectionItems = getAccountSectionItems(navigation, data?.user_profile?.id);

    useEffect(() => {
        setProfileLink(data?.user_profile.profile_photo)
    }, [data?.user_profile.profile_photo])
    function onItemPressed(item: ProfileItem) {
        if (item.section == 0 && item.index == 3) {
            setIsLoading(true)
            tempAuthMutation.mutate()
        } else if (item.section == 1 && item.index == 1) {
            showDeleteAlert()
        }
        else if (item.onPressAction != null ) {
            item.onPressAction();
        }
    }
        const deleteAccountMutation = useMutation(
        {
            mutationFn: () => deleteUser(),
            onSuccess: async () => {
                let keys = storage.getAllKeys()
                keys.map(key => {
                    if (key != AUTH_STORRAGE_KEYS.USERID) {
                        storage.delete(key)
                    }
                } )
                // storage.clearAll()
                await queryClient.cancelQueries()
                queryClient.removeQueries()
                revokeFcmToken()
                navigation.reset({
                    index: 0,
                    routes: [{name: 'NewLogin'}]
                })
                setIsLoading(false)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t('home:Error'), error.message)
            }
        },
    );
    function showDeleteAlert() {
        let options = [
            {
                text: 'Cancel',
            },
            {
                text: 'Delete',
                onPress: () => {
                    setIsLoading(true)
                    deleteAccountMutation.mutate()
                },
                style: 'destructive',
            },
        ]
        Alert.alert('Delete Account?', 'Are you sure you want to delete the account? On deleting all of your data will be permanently deleted.', options)
                
    }
    function createFormData(photo, body = {}) {
        const data = new FormData();
        data.append('profile_photo', {
          name: photo.fileName,
          type: photo.type,
          uri: Platform.OS === 'ios' ? photo.uri.replace('file://', '') : photo.uri,
        });
      
        Object.keys(body).forEach((key) => {
          data.append(key, body[key]);
        });
        return data;
    };
      
    async function onPickOptionPress (index: number) {
        if (index == 0) {
            const result = await launchImageLibrary({mediaType: "photo", quality: 0.1});
            if (result.assets != null && result.assets.length > 0) {
                let photo = result.assets[0]
                if (photo != null) {
                    setProfileLink(Platform.OS === 'ios' ? photo?.uri?.replace('file://', '') : photo.uri)
                }
                let formData = createFormData(result.assets[0], {_method: "PUT"})
                setIsLoading(true)
                updateMutation.mutate(formData)
            }
            console.log("Gallery picker response::", result)

        } else if (index == 1) {
            const result = await launchCamera({mediaType: "photo", quality: 0.1});
            if (result.assets != null && result.assets.length > 0) {
                let photo = result.assets[0]

                setProfileLink(Platform.OS === 'ios' ? photo?.uri?.replace('file://', '') : photo.uri)

                let formData = createFormData(result.assets[0], {_method: "PUT"})
                setIsLoading(true)
                updateMutation.mutate(formData)
            }
            

            console.log("Camer picker response::", result)
            

        }
    }
    function onEditPhotoButtonTapped(): void {
        let options = [t("account:ChoosePhoto"), t("account:Camera"), t("account:Cancel")];
        showActionSheetWithOptions({
            options: options,
            cancelButtonIndex: 2,
            destructiveButtonIndex: 2,
            showSeparators: true,
            textStyle: fonts.green50,
            }, (i?: number) => {
                onPickOptionPress(i ?? 0)
        }); 
        
    }
    return (
        <AKSafeAreaView>
            <ScrollView style={[layout.flex_1]}>

                <View style={[gutters.marginBottom_16]}>
                <Spinner
                    visible={isLoading}
                />
                    <View style={layout.flex_1}>
                        <View style={[layout.itemsCenter, gutters.marginHorizontal_0]}>
                            <View style={[gutters.marginTop_24]}>
                                <AKFastImage
                                    uri={profileLink}
                                    style={{height: 150, width: 150, borderRadius: 75}}
                                />
                                <View>
                                    <TouchableOpacity onPress={onEditPhotoButtonTapped}>
                                        <Image source={require('@/theme/assets/images/Account/EditIcon.png')} style={[layout.absolute, layout.right0, layout.bottom0 ,{width: 46, height: 46}]}/>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={[layout.itemsCenter, gutters.marginTop_16]}>
                                <Text>{(data?.user_profile.first_name ?? '') + ' ' +  (data?.user_profile.last_name ?? '')}</Text>
                                <Text>{(data?.user_profile.email ?? '') + ' | ' + (data?.user_profile?.phone_number ?? '')}</Text>
                            </View>
                        </View>
                    {
                        accountSectionItems.map((sectionItem, index) => (
                            <View key={index} style={[gutters.marginTop_16]}>
                                <AccountSection items={sectionItem} onItemPressed={onItemPressed}/>
                            </View>
                        ))
                    }
                    </View>
                </View>
            </ScrollView>

        </AKSafeAreaView>
    )
}
export default Account