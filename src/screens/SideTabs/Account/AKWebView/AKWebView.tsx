import { ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { useTranslation } from "react-i18next";
import { TouchableOpacity, View } from "react-native";
import WebView from "react-native-webview";
import CrossIcon from "@/theme/assets/images/CrossIcon.png"
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { ApplicationStackParamList } from "@/types/navigation";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";

export type AKWebViewNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList
>;
function AKWebView() {
    let baseURL: string | null = ''
    const queryClient = useQueryClient()
    const { t } = useTranslation(['profile']);
    const navigation = useNavigation<AKWebViewNavigationProp>()
    const route = useRoute<RouteProp<ApplicationStackParamList, 'AKWebView'>>()
    const webViewRef = useRef<WebView>(null);

    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();
    function onCrossButtonTapped() {
        navigation.goBack()
    }

    function getBaseUrl(urlString: string): string | null {
        var pathArray = urlString.split( '/' );
        var protocol = pathArray[0];
        var host = pathArray[2];
        var url = protocol + '//' + host + '/';
        return url
    }
    useEffect(() => {
        baseURL = getBaseUrl(route.params.source)

    }, [route.params.source]) 

    // useEffect(() => {
    //     clearWebViewCache()
    // }, []) 


    return (
        <View style={[layout.flex_1]}>
            <TouchableOpacity onPress={onCrossButtonTapped}>
                <View style={[gutters.marginHorizontal_0,{height: 60, width: 60, left: 20, top: 20}]}>
                    <ImageVariant
                        source={CrossIcon}
                        tintColor={colors.black}
                        style={ [ ,layout.itemsCenter]}
                    />
                </View>
            </TouchableOpacity>
            <WebView
                ref={webViewRef}
                incognito={route.params.isFromSignup ? true : false}
                style={[layout.flex_1]} source={{uri: route.params.source}}
                onNavigationStateChange={(state) => {
                    if (state.url === baseURL) {
                        queryClient.invalidateQueries(['get_profile']);
                        navigation.goBack()
                    }                  
                }}
                  
                />
        </View>
    )
}
export default AKWebView