import { getAccountSectionItems } from '../utility';
import { Linking } from 'react-native';

// Mock the Linking module
jest.mock('react-native', () => ({
  Linking: {
    openURL: jest.fn(),
  },
}));

// Mock the image requires
jest.mock('@/theme/assets/images/Account/EditProfileIcon.png', () => 'mocked-edit-profile-icon');
jest.mock('@/theme/assets/images/Account/NotificationIcon.png', () => 'mocked-notification-icon');
jest.mock('@/theme/assets/images/Account/CardIcon.png', () => 'mocked-card-icon');
jest.mock('@/theme/assets/images/Account/SecurityIcon.png', () => 'mocked-security-icon');
jest.mock('@/theme/assets/images/Account/SupportIcon.png', () => 'mocked-support-icon');
jest.mock('@/theme/assets/images/Account/ContactUsIcon.png', () => 'mocked-contact-us-icon');
jest.mock('@/theme/assets/images/Account/PrivacyIcon.png', () => 'mocked-privacy-icon');

describe('Account Utility Functions', () => {
  const mockNavigation = {
    navigate: jest.fn(),
  };
  const mockUserId = 123;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAccountSectionItems', () => {
    it('should return the correct number of sections', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      expect(sections).toHaveLength(3);
    });

    it('should return the correct number of items in each section', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      expect(sections[0]).toHaveLength(5); // First section
      expect(sections[1]).toHaveLength(2); // Second section
      expect(sections[2]).toHaveLength(3); // Third section
    });

    it('should have correct labels for each item', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      
      // First section
      expect(sections[0][0].label).toBe('Edit Profile Information');
      expect(sections[0][1].label).toBe('Notifications');
      expect(sections[0][2].label).toBe('Social Connect Profile');
      expect(sections[0][3].label).toBe('Manage your Membership');
      
      // Second section
      expect(sections[1][0].label).toBe('Security');
      
      // Third section
      expect(sections[2][0].label).toBe('Ask Akina AI Help & Support');
      expect(sections[2][1].label).toBe('Contact Us');
      expect(sections[2][2].label).toBe('Privacy Policy');
    });

    it('should have correct icons for each item', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      
      // Check a few icons
      expect(sections[0][0].icon).toBe('mocked-edit-profile-icon');
      expect(sections[0][1].icon).toBe('mocked-notification-icon');
      expect(sections[1][0].icon).toBe('mocked-security-icon');
      expect(sections[2][0].icon).toBe('mocked-support-icon');
    });

    it('should have correct section and index values', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      
      // Check section and index for a few items
      expect(sections[0][0].section).toBe(0);
      expect(sections[0][0].index).toBe(0);
      
      expect(sections[1][0].section).toBe(1);
      expect(sections[1][0].index).toBe(0);
      
      expect(sections[2][2].section).toBe(2);
      expect(sections[2][2].index).toBe(2);
    });

    it('should navigate to OnBoarding when Edit Profile Information is pressed', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[0][0].onPressAction();
      
      expect(mockNavigation.navigate).toHaveBeenCalledWith('OnBoarding');
    });

    it('should navigate to NotificationPreference when Notifications is pressed', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[0][1].onPressAction();
      
      expect(mockNavigation.navigate).toHaveBeenCalledWith('NotificationPreference');
    });

    it('should navigate to Profile with userId when Social Connect Profile is pressed', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[0][2].onPressAction();
      
      expect(mockNavigation.navigate).toHaveBeenCalledWith('UserProfile', { userId: mockUserId });
    });

    it('should have an empty function for Manage your Membership', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[0][3].onPressAction();
      
      expect(mockNavigation.navigate).not.toHaveBeenCalled();
    });

    it('should navigate to ChangePassword when Security is pressed', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[1][0].onPressAction();
      
      expect(mockNavigation.navigate).toHaveBeenCalledWith('ChangePassword');
    });

    it('should open support email when Ask Akina AI Help & Support is pressed', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[2][0].onPressAction();
      
      expect(Linking.openURL).toHaveBeenCalledWith('mailto:<EMAIL>');
    });

    it('should open support email when Contact Us is pressed', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[2][1].onPressAction();
      
      expect(Linking.openURL).toHaveBeenCalledWith('mailto:<EMAIL>');
    });

    it('should navigate to PrivacyPolicy when Privacy Policy is pressed', () => {
      const sections = getAccountSectionItems(mockNavigation, mockUserId);
      sections[2][2].onPressAction();
      
      expect(mockNavigation.navigate).toHaveBeenCalledWith('PrivacyPolicy');
    });
  });
});