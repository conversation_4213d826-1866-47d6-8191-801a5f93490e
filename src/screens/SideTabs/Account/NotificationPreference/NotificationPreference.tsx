import { getProfile } from "@/services/users";
import notificationPreferenceListing from "@/services/users/notificationPreferenceListing";
import updateNotificationPreference from "@/services/users/updateNotificationPreference";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { notificationPreferenceSchema, notificationResponseSchema, notificationTypesSchema } from "@/types/schemas/notificationPreference";
import { useIsFocused } from "@react-navigation/native";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import _ from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Image, ScrollView, Switch, Text, View } from "react-native"
import Spinner from "react-native-loading-spinner-overlay";
import { ZodMap, z } from "zod";

function NotificationPreference() {
    const { t } = useTranslation(['account', 'sideMenu']);
    const isFocused = useIsFocused();
    const queryClient = useQueryClient();
    const [isLoading, setIsLoading] = useState(false)
    const userData = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const notificationData = useQuery({
        queryKey: ['notificationPreference'],
        queryFn: () => notificationPreferenceListing(userData.data?.user_profile.id),
        
    });

    useEffect(() => {
        notificationData.refetch()
    },[])
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();

    const c = colorTokens();
    
    const changePreferenceMutation = useMutation(
        {
            mutationFn: (data: any) => updateNotificationPreference(userData.data?.user_profile.id, data),
            onSuccess: (response) => {
                setIsLoading(false)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t('accont:Error'), error.message)
            }
        }
    )

    function onValueChange(item: z.infer<typeof notificationPreferenceSchema>, key: string) {
        setIsLoading(true)
        let dataDict = {
            [item.name]: !item.selected
        }
        changePreferenceMutation.mutate(dataDict)
        queryClient.setQueryData(['notificationPreference'], (notif: any) => {
            if (key == 'common') {
                const preferences = notif.user_notification_preferences.common!.map((obj: z.infer<typeof notificationPreferenceSchema>) => {
                    if (obj.name == item.name) {
                        return {
                            ...obj,
                            selected: !item.selected
                        }
                    }
                    return obj
                })
                notif.user_notification_preferences[key] = preferences
                return {...notif, key: preferences}

            }
            if (key == 'system_&_services_update') {
                const preferences = notif.user_notification_preferences['system_&_services_update']!.map((obj: z.infer<typeof notificationPreferenceSchema>) => {
                    if (obj.name == item.name) {
                        return {
                            ...obj,
                            selected: !item.selected
                        }
                    }
                    return obj
                })
                notif.user_notification_preferences[key] = preferences
                return {...notif, key: preferences}

            }
            if (key == 'others') {
                const preferences = notif.user_notification_preferences.others!.map((obj: z.infer<typeof notificationPreferenceSchema>) => {
                    if (obj.name == item.name) {
                        return {
                            ...obj,
                            selected: !item.selected
                        }
                    }
                    return obj
                })
                notif.user_notification_preferences[key] = preferences
                return {...notif, key: preferences}
            }
            
        })
        




    }
    function sectionUI(key: string, items: any) {
        return (
            <View>
                <Spinner
                    visible={isLoading}
                />
                <Text style={[fonts.size_16, fonts.bold, gutters.marginBottom_16]}>{key.replaceAll('_', ' ').toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')}</Text>

                {items.map((item: any) => {
                    return (
                        <View>
                            <View style={[layout.row, layout.justifyBetween]}>
                                <Text>{item.value}</Text>
                                <Switch 
                                    value={item.selected}
                                    onValueChange={() => onValueChange(item, key)}
                                    trackColor={{true: c.content.primary.default}}
                                    style={{transform:[{scale: 0.5}]}}
                                />
                            </View>
                        </View>
                    )
                })}
                <View style={[gutters.marginHorizontal_0, backgrounds.gray100, gutters.marginVertical_16 ,{height: 1}]}></View>
                
            </View>
            
        )
    }
    return (
        <View style={[layout.flex_1, gutters.marginHorizontal_0, backgrounds.white ]}>
            <Text style={[fonts.size_32, gutters.marginHorizontal_16, gutters.marginTop_16, fonts.Medium]}>{t('sideMenu:Notifications')}</Text>
            <ScrollView style={[  gutters.marginHorizontal_16 ,gutters.marginTop_16, {height: 400}]}>
                <View style={[ gutters.marginHorizontal_0]}>

                    {notificationData.data?.user_notification_preferences?.common && sectionUI('common',notificationData.data?.user_notification_preferences?.common)}
                    {notificationData.data?.user_notification_preferences?.["system_&_services_update"] != null && sectionUI('system_&_services_update',notificationData.data?.user_notification_preferences!["system_&_services_update"])}
                    {notificationData.data?.user_notification_preferences?.others && sectionUI('others',notificationData.data?.user_notification_preferences?.others)}
                </View>
            </ScrollView>
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter, {height: 200}]}>
                <Image
                    source={require('@/theme/assets/images/Account/LogoBottom.png')}
                    style={[layout.bottom0]}
                    tintColor={c.content.primary.default}
                />
            </View>
        </View>

    )

}


export default NotificationPreference