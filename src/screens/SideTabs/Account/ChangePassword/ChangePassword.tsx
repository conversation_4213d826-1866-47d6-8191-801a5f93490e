import { <PERSON><PERSON><PERSON><PERSON>, AKTextField, ImageVariant } from "@/components/atoms";
import changePassword from "@/services/users/changePassword";
import { useTheme } from "@/theme";
import { useNavigation } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import _ from "lodash";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Image, Text, TouchableOpacity, View } from "react-native"
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Spinner from "react-native-loading-spinner-overlay";
import EyeIcon from "@/theme/assets/images/EyeIcon.png"
import {colorTokens} from "@/theme/colorTokens";

function ChangePassword() {
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();
    const { t } = useTranslation(['auth', 'account', 'sideMenu']);

    const c = colorTokens();

    const [form, setForm] = useState({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: '',
    })
    const [errors, setErrors] = useState({
        currentPassword: null,
        newPassword: null,
        confirmNewPassword: null,
    })
    const [isLoading, setIsLoading] = useState(false)
    const [isCurrentPasswordEyeSelected, setIsCurrentPasswordEyeSelected] = useState(false)
    const [isNewPasswordEyeSelected, setIsNewPasswordEyeSelected] = useState(false)
    const [isConfirmPasswordEyeSelected, setIsConfirmPasswordEyeSelected] = useState(false)
    const navigation = useNavigation()
    const changePasswordMutation = useMutation(
        {
            mutationFn: (data: any) => changePassword(data),
            onSuccess: (response) => {
                setIsLoading(false)
                if (response.success == false) {
                    Alert.alert(t('account:Failure'), response.message)
                } else {
                    let options = [{text: t("auth:Ok"), onPress: async () => {
                        navigation.goBack()
                    }}]
                    Alert.alert(t("auth:Success"), t('account:PasswordChangedSuccessfully'), options)
                }
                
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t("account:Error"), error.message)
            }
        },
    );
    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});

    }
    function validateForm() {
        let isValid = true
        if(form.currentPassword == '') {
            setErrors({..._.set(errors, 'currentPassword', t('account:EnterCurrentPasswordError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'currentPassword', null)});
        }
        if(form.newPassword == '') {
            setErrors({..._.set(errors, 'newPassword', t('account:EnterNewPasswordError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'newPassword', null)});
        }
        if (form.confirmNewPassword == '') {
            setErrors({..._.set(errors, 'confirmNewPassword', t('account:EnterConfirmNewPasswordError'))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'confirmNewPassword', null)});
        }
        if (form.newPassword != form.confirmNewPassword) {
            setErrors({..._.set(errors, 'newPassword', null)});
            setErrors({..._.set(errors, 'confirmNewPassword', t("auth:PasswordNotMatchError"))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'confirmNewPassword', null)});
            setErrors({..._.set(errors, 'newPassword', null)});

        }
        if (form.newPassword == form.currentPassword) {
            setErrors({..._.set(errors, 'newPassword', t("auth:PasswordMatchError"))});
            isValid = false
        } else {
            setErrors({..._.set(errors, 'newPassword', null)});
        }
        return isValid
    }
    function updatePasswordTapped() {
        if (validateForm() == true) {
            let data = {
                current_password: form.currentPassword,
                password: form.newPassword,
                password_confirmation: form.confirmNewPassword
            }
            console.log("Data:::", data)
            setIsLoading(true)
            changePasswordMutation.mutate(data)
        }

    }
    function eyeIconPressed(index: number) {
        switch (index) {
            case 0:
                setIsCurrentPasswordEyeSelected(!isCurrentPasswordEyeSelected)
                return
            case 1:
                setIsNewPasswordEyeSelected(!isNewPasswordEyeSelected)
                return
            case 2:
                setIsConfirmPasswordEyeSelected(!isConfirmPasswordEyeSelected)
                return
            default:
                return
        }
    }
    function renderRightEyeIcon(index: number) {
        return (
            <TouchableOpacity onPress={() => eyeIconPressed(index)}>
                <ImageVariant
                    source={EyeIcon}
                    style={[{tintColor: colors.gray200
                    }]}
                />
            </TouchableOpacity>
        )
    }
    return (
        <KeyboardAwareScrollView contentContainerStyle={{flexGrow: 1, backgroundColor: colors.white}}>
            <View style={[layout.flex_1]}>
                <Spinner
                    visible={isLoading}
                />
                <Text style={[gutters.marginHorizontal_0, fonts.alignCenter, gutters.marginTop_12,fonts.bold, fonts.size_24]}>{t('sideMenu:Security')}</Text>
                <Text style={[gutters.marginHorizontal_24, gutters.marginTop_12, fonts.bold ,fonts.size_16]}>{t('account:SetNewPassword')}</Text>
                <Text style={[gutters.marginHorizontal_24, gutters.marginTop_12 ,fonts.size_16]}>{t('account:CreateNewPassword')}</Text>
                <View style={[gutters.marginTop_24, gutters.marginHorizontal_24]}>
                    <AKTextField rightAccessory={() => renderRightEyeIcon(0)} isSecure={!isCurrentPasswordEyeSelected} autoCapitalize="none" error={errors.currentPassword} label={t('account:CurrentPassword')} baseColor={colors.gray200} onChangeValue={onValueChange} name="currentPassword"/>
                </View>
                <View style={[gutters.marginTop_24, gutters.marginHorizontal_24]}>
                    <AKTextField rightAccessory={() => renderRightEyeIcon(1)} isSecure={!isNewPasswordEyeSelected} autoCapitalize="none" error={errors.newPassword} label={t('account:NewPassword')} baseColor={colors.gray200} onChangeValue={onValueChange} name="newPassword"/>
                </View>
                <View style={[gutters.marginTop_24, gutters.marginHorizontal_24]}>
                    <AKTextField rightAccessory={() => renderRightEyeIcon(2)} isSecure={!isConfirmPasswordEyeSelected} autoCapitalize="none" error={errors.confirmNewPassword} label={t('account:ConfirmNewPassword')} baseColor={colors.gray200} onChangeValue={onValueChange} name="confirmNewPassword"/>
                </View>
                <View style={[gutters.marginHorizontal_32, gutters.marginTop_32]}>
                    <AKButton 
                        height={48}
                        title={t('account:UpdatePassword')}
                        onPress={updatePasswordTapped}
                        borderRadius={10}
                        disabled={false}
                    />
                </View>
                <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
                    <Image
                        source={require('@/theme/assets/images/Account/LogoBottom.png')}
                        style={[layout.bottom0]}
                        tintColor={c.content.primary.default}
                    />
                </View>
            </View>
        </KeyboardAwareScrollView>
    )
}
export default ChangePassword