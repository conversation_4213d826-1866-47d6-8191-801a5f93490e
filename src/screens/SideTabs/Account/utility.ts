import { Linking } from 'react-native';

export function getAccountSectionItems(navigation: any, userId: number) {
	return [
		[
			{
				label: 'Edit Profile Information',
				icon: require('@/theme/assets/images/Account/EditProfileIcon.png'),
				section: 0,
				index: 0,
				onPressAction: () => navigation.navigate('OnBoarding'),
			},
			{
				label: 'Notifications',
				icon: require('@/theme/assets/images/Account/NotificationIcon.png'),
				section: 0,
				index: 1,
				onPressAction: () => navigation.navigate('NotificationPreference'),
			},
			{
				label: 'Social Connect Profile',
				icon: require('@/theme/assets/images/Account/EditProfileIcon.png'),
				section: 0,
				index: 2,
				onPressAction: () => navigation.navigate('UserProfile', { userId }),
			},
			{
				label: 'Manage your Membership',
				icon: require('@/theme/assets/images/Account/CardIcon.png'),
				section: 0,
				index: 3,
				onPressAction: () => {},
			},
			{
				label: 'Blocked',
				icon: require('@/theme/assets/images/Account/BlockedIcon.png'),
				section: 0,
				index: 4,
				onPressAction: () => navigation.navigate("BlockedAccounts"),
			},
		],
		[
			{
				label: 'Security',
				icon: require('@/theme/assets/images/Account/SecurityIcon.png'),
				section: 1,
				index: 0,
				onPressAction: () => navigation.navigate('ChangePassword'),
			},
			{
				label: 'Delete Account',
				icon: require('@/theme/assets/images/SocialConnect/BinIcon.png'),
				section: 1,
				index: 1,
				onPressAction: () => {},
			},
		],
		[
			{
				label: 'Ask Akina AI Help & Support',
				icon: require('@/theme/assets/images/Account/SupportIcon.png'),
				section: 2,
				index: 0,
				onPressAction: () => Linking.openURL('mailto:<EMAIL>'),
			},
			{
				label: 'Contact Us',
				icon: require('@/theme/assets/images/Account/ContactUsIcon.png'),
				section: 2,
				index: 1,
				onPressAction: () => Linking.openURL('mailto:<EMAIL>'),
			},
			{
				label: 'Privacy Policy',
				icon: require('@/theme/assets/images/Account/PrivacyIcon.png'),
				section: 2,
				index: 2,
				onPressAction: () => navigation.navigate('PrivacyPolicy'),
			},
		],
	];
}
