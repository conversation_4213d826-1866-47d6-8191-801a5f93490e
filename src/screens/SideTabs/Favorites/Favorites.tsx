import modules from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { QueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { Al<PERSON>, FlatList, Text, View } from "react-native";
import HeartFilled from "@/theme/assets/images/Home/HeartFilled.png"
import { CategoryHorizontalList, SavedItem } from "@/components/atoms";
import userLikes from "@/services/users/userLikes";
import { z } from "zod";
import { likeSchema } from "@/types/schemas/userLikes";
import { MODULE_TYPES } from "@/utils/constants";
import postDetail from "@/services/home/<USER>";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import { useIsFocused, useNavigation } from "@react-navigation/native";
import videoDetail from "@/services/home/<USER>";
import { useEffect, useState } from "react";
import Spinner from "react-native-loading-spinner-overlay";
import musicDetail from "@/services/home/<USER>";
import newsDetail from "@/services/home/<USER>";
import { moduleSchema } from "@/types/schemas/module";
import likeableModules from "@/services/home/<USER>";
import { useTranslation } from "react-i18next";
import podcastDetail from "@/services/home/<USER>";
import { postDetailSchema } from '@/types/schemas/postDetail';
import { videoSchema } from '@/types/schemas/videoListing';
import { musicSchema } from '@/types/schemas/musicListing';
import { newsSchema } from '@/types/schemas/newsListing';

export type FavoritesNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList
>;

type PostDetailSchema = z.infer<typeof postDetailSchema>;
type VideoSchema = z.infer<typeof videoSchema>;
type MusicSchema = z.infer<typeof musicSchema>;
type NewsSchema = z.infer<typeof newsSchema>;

function Favorites() {
    const navigation = useNavigation<FavoritesNavigationProp>()
    const isFocused = useIsFocused();
    const { t } = useTranslation(['sideTabs']);
    const {data } = useQuery({
        queryKey: ['homeItems'],
        queryFn: likeableModules
    });
    const userLikesResponse = useQuery({
        queryKey: ['user-likes'],
        queryFn: userLikes
    });
    const { layout, backgrounds } = useTheme();

    const [isLoading, setIsLoading] = useState(false)
    const queryClient = new QueryClient()
    const [likeListing, setLikeListing] = useState(userLikesResponse.data?.likes)
    const [selectedCategory, setSelectedCategory] = useState<z.infer<typeof moduleSchema> | null>(null)
    const [selectedModuleTitle, setSelectedModuleTitle] = useState('')
    const [selectedSourceTitle, setSelectedSourceTitle] = useState('')
    useEffect(() => {
        if (isFocused == true) {
            setSelectedCategory(null)
            setLikeListing(userLikesResponse.data?.likes)
            userLikesResponse.refetch()
        }
    },[isFocused])
    useEffect(() => {
        setLikeListing(userLikesResponse.data?.likes)
    },[userLikesResponse.data])
    function categoryItemPressed(item: z.infer<typeof moduleSchema>) {
        if (selectedCategory?.id == item.id) {
            setSelectedCategory(null)
            setLikeListing(userLikesResponse.data?.likes)
        } else {
            setSelectedCategory(item)
            setLikeListing(userLikesResponse.data?.likes.filter(likeItem => likeItem.module_id ==item.id))
        }
    }

    function onErrorMutation(error: any) {
        setIsLoading(false)
        Alert.alert(t('sideTabs:Error'), error.message)
    }

    function onSuccessMutation(response: PostDetailSchema | VideoSchema | MusicSchema | NewsSchema, idPrefix: string, navigationPath: string, params: any) {
        setIsLoading(false)
        queryClient.setQueryData([`${idPrefix}${response.id}`], () => response)
        navigation.navigate(navigationPath, {item: response, ...params})
    }

    const blogDetailMutation = useMutation(
        {
            mutationFn: (data: any) => postDetail(data.module_id, data.id),
            onSuccess: (response: PostDetailSchema) => onSuccessMutation(
                response, 'postDetail', 'BlogDetail', {moduleTitle: selectedModuleTitle}
            ),
            onError: onErrorMutation,
        },
    );
    const videoDetailMutation = useMutation(
        {
            mutationFn: (data: any) => videoDetail(data.module_id, data.id),
            onSuccess: (response: VideoSchema) => onSuccessMutation(
                response, 'videoDetail', 'VideoDetail', {}
            ),
            onError: onErrorMutation,
        },
    );
    const musicDetailMutation = useMutation(
        {
            mutationFn: (data: any) => musicDetail(data.module_id, data.id),
            onSuccess: (response: MusicSchema) => onSuccessMutation(
                response, 'musicDetail', 'MusicDetail', {}
            ),
            onError: onErrorMutation,
        },
    );
    const podcastDetailMutation = useMutation(
        {
            mutationFn: (data: any) => podcastDetail(data.module_id, data.id),
            onSuccess: (response) => onSuccessMutation(
                response, 'podcastDetail', 'PodcastDetail', {}
            ),
            onError: onErrorMutation,
        },
    );
    const newsDetailMutation = useMutation(
        {
            mutationFn: (data: any) => newsDetail(data.module_id, data.sourceId, data.id),
            onSuccess: (response: NewsSchema) => onSuccessMutation(
                response, 'newsDetail', 'NewsDetail', {sourceName: selectedSourceTitle}
            ),
            onError: onErrorMutation,
        },
    );

    function fetchDetail(item: z.infer<typeof likeSchema>, mutation: any, params?: any) {
        setIsLoading(true);
        setSelectedModuleTitle(item.module_title);
        mutation.mutate({module_id: item.module_id, id: item.id, ...params})
    }

    function itemPressed(item: z.infer<typeof likeSchema>){
        switch (item.type) {
            case MODULE_TYPES.BLOG:
                fetchDetail(item, blogDetailMutation)
                return
            case MODULE_TYPES.VIDEO:
                fetchDetail(item, videoDetailMutation)
                return
            case MODULE_TYPES.NEWS:
                setSelectedSourceTitle(item.source_name ?? '')
                fetchDetail(item, newsDetailMutation, { sourceId: item.news_source_id })
                return
            case MODULE_TYPES.MUSIC:
                fetchDetail(item, musicDetailMutation)
                return
            case MODULE_TYPES.PODCAST:
                fetchDetail(item, podcastDetailMutation)
                return
            default:
                return
        }

    }
    function renderItem({item}: any) {
        return (
            <SavedItem onItemPressed={itemPressed} item={item} icon={HeartFilled} />
        );
    }
    return (
        <View style={[layout.flex_1, backgrounds.white]}>
            <Spinner
                visible={isLoading}
            />
            <CategoryHorizontalList
                categories={data?.modules}
                onCategoryPressed={categoryItemPressed}
                selectedCategory={selectedCategory}
            />
            {likeListing?.length == 0 && isLoading == false && 
                <View style={[layout.flex_1, layout.justifyCenter, layout.itemsCenter]}>
                    <Text>{t('sideTabs:PostNotFavoriteYet')}</Text>
                </View>
            }
            <View >
                <FlatList 
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 80}} 
                    data={likeListing}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    numColumns={3}
                    columnWrapperStyle={[{flex:1, justifyContent: 'space-evenly'}]}
                />
            </View>
        </View>
    )
}
export default Favorites