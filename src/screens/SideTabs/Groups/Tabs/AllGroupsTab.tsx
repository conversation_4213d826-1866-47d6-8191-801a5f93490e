import { FlatList, Text, TouchableOpacity, View } from "react-native";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { GroupsListingItem } from "@/components/molecules";
import { useState } from "react";

const data = [
    { id: 1, name: 'The Sisterhood', members: 3, type: 'public' },
    { id: 2, name: 'The Brotherhood', members: 535, type: 'most_visited' },
    { id: 3, name: 'The Family', members: 93, type: 'private', newPosts: 2 },
    { id: 4, name: 'The Friends', members: 29, type: 'recently_joined' },
    { id: 5, name: 'The Enemies', members: 6, type: 'pending', newPosts: 6 },
];

const filterOptions = [
    { id: 'all', label: 'All', type: null },
    { id: 'public', label: 'Public', type: 'public' },
    { id: 'private', label: 'Private', type: 'private' },
    { id: 'most_visited', label: 'Most Visited', type: 'most_visited' },
    { id: 'recently_joined', label: 'Recently Joined', type: 'recently_joined' },
    { id: 'pending', label: 'Pending', type: 'pending' },
];

function AllGroupsTab() {

    const { layout, gutters, colors } = useTheme();
    const c = colorTokens();
    const [selectedFilter, setSelectedFilter] = useState(filterOptions[0]);

    // Filter data based on selected filter
    const filteredData = selectedFilter.type
        ? data.filter(item => item.type === selectedFilter.type)
        : data;

    function renderFilterChip({ item }: { item: typeof filterOptions[0] }) {
        const isSelected = item.id === selectedFilter.id;
        return (
            <TouchableOpacity onPress={() => setSelectedFilter(item)}>
                <View
                    style={[
                        layout.justifyCenter,
                        layout.itemsCenter,
                        {
                            height: 32,
                            borderRadius: 16,
                            backgroundColor: isSelected
                                ? c.content.primary.default
                                : colors.gray100,
                        },
                    ]}
                >
                    <Text
                        style={[
                            gutters.marginHorizontal_12,
                            {
                                color: isSelected
                                    ? colors.white
                                    : colors.black,
                            },
                        ]}
                    >
                        {item.label}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    }

    function renderItem({ item }: { item: { id: number, name: string } }) {
        return (
            <GroupsListingItem item={item} />
        )
    }

    return (
        <View style={[layout.flex_1]}>
            {/* Filter Chips */}
            <View style={[gutters.marginTop_12]}>
                <FlatList
                    horizontal
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={[gutters.paddingHorizontal_16, { gap: 12 }]}
                    data={filterOptions}
                    keyExtractor={(item) => item.id}
                    renderItem={renderFilterChip}
                />
            </View>

            {/* Groups List */}
            <FlatList
                data={filteredData}
                renderItem={renderItem}
                contentContainerStyle={[gutters.marginHorizontal_16, gutters.marginTop_16, { gap: 8 }]}
            />
        </View>
    )
}

export default AllGroupsTab;
