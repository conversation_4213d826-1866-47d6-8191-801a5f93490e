import { FlatList, ScrollView, Text, View } from "react-native";
import { useTheme } from "@/theme";
import { GroupsListingItem } from "@/components/molecules";

const data = [
    { id: 1, name: 'The Sisterhood', members: 3, type: 'public' },
    { id: 2, name: 'The Brotherhood', members: 535, type: 'most_visited' },
    { id: 3, name: 'The Family', members: 93, type: 'private', newPosts: 2 },
    { id: 4, name: 'The Friends', members: 29, type: 'recently_joined' },
    { id: 5, name: 'The Enemies', members: 6, type: 'pending', newPosts: 6 },
];

function AllGroupsTab() {

    const { layout, gutters } = useTheme();

    function renderItem({ item }: { item: { id: number, name: string } }) {
        return (
            <GroupsListingItem item={item} />
        )
    }

    return (
        <View style={[layout.flex_1]}>
            <ScrollView>
                
            </ScrollView>
            <FlatList
                data={data}
                renderItem={renderItem}
                contentContainerStyle={[gutters.marginHorizontal_16, { gap: 8 }]}
            />
        </View>
    )
}

export default AllGroupsTab;
