import { FlatList, Text, View } from "react-native";
import { useTheme } from "@/theme";
import { GroupsListingCard } from "@/components/molecules";

const data = [
    { id: 1, name: 'Shows Recommendations' },
    { id: 2, name: 'Group 2' },
    { id: 3, name: 'Group 3' },
    { id: 4, name: 'Group 4' },
    { id: 5, name: 'Group 5' },
    { id: 6, name: 'Group 6' },
    { id: 7, name: 'Group 7' },
    { id: 8, name: 'Group 8' },
    { id: 9, name: 'Group 9' },
    { id: 10, name: 'Group 10' },
];

function ExploreTab() {

    const { layout, gutters } = useTheme();

    function renderItem({ item }: { item: { id: number, name: string } }) {
        return (
            <GroupsListingCard item={item} />
        )
    }

    return (
        <View style={[layout.flex_1]}>
            <FlatList
                data={data}
                numColumns={2}
                renderItem={renderItem}
                showsVerticalScrollIndicator={false}
                columnWrapperStyle={[{ gap: 8 }]}
                contentContainerStyle={[gutters.marginHorizontal_16, gutters.marginTop_16, { gap: 8 }]}
            />
        </View>
    )
}

export default ExploreTab;
