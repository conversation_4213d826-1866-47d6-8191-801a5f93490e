import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { useNavBar } from "@/hooks/useNavBar";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useEffect, useState } from "react";
import { Dimensions, Text, TouchableOpacity, View } from "react-native";
import AddIcon from "@/theme/assets/images/AddIcon.png";
import SearchIcon from "@/theme/assets/images/Search.png";
import { TabView, TabBar, TabBarProps, SceneMap, SceneRendererProps } from 'react-native-tab-view';
import { useNavigation } from "@react-navigation/native";
import { AllGroupsTab, ExploreTab, FavouritesTab, ManageTab } from "./Tabs";

type RouteType = {
    key: 'all_groups' | 'explore' | 'pending' | 'manage';
    title: string;
};

type RenderSceneProps = SceneRendererProps & {
    route: RouteType;
};

const routes: RouteType[] = [
    { key: 'all_groups', title: 'All Groups' },
    { key: 'explore', title: 'Explore' },
    { key: 'pending', title: 'Pending' },
    { key: 'manage', title: 'Manage' },
];

function Groups() {

    const { setupNavBar, updateCustomRightButton } = useNavBar('Groups', true);
    const c = colorTokens();
    const { layout, fonts, gutters } = useTheme();
    const navigation = useNavigation();

    const { width } = Dimensions.get('window');

    function onPressAdd() {
        navigation.navigate('NewGroup');
    }

    function getHeaderRightComponent() {
        return (
            <View style={[layout.row, layout.itemsCenter, gutters.marginRight_12, { gap: 10 }]}>
                <TouchableOpacity onPress={onPressAdd} style={[gutters.padding_4, { borderWidth: 1, borderColor: c.stoke.default.default, borderRadius: 4 }]}>
                    <AKIcon source={AddIcon} tintColor={c.content.default.default} size={24} />
                </TouchableOpacity>
                <TouchableOpacity style={[gutters.padding_4]}>
                    <AKIcon source={SearchIcon} />
                </TouchableOpacity>
            </View>
        )
    }

    useEffect(() => {
        setupNavBar();
        updateCustomRightButton(getHeaderRightComponent());
    }, []);

    const [index, setIndex] = useState(0);
    
    const renderTabBar = (props: TabBarProps<RouteType>) => (
        <TabBar
            {...props}
            indicatorStyle={{ backgroundColor: c.stoke.primary.default, height: 3, borderRadius: 10 }}
            style={{ backgroundColor: c.custom.white, borderBottomWidth: 1, borderBottomColor: c.stoke.default.default }}
        />
    );

    const renderScene = ({ route }: RenderSceneProps) => {
        switch (route.key) {
            case 'all_groups':
                return <AllGroupsTab />;
            case 'explore':
                return <ExploreTab />;
            case 'pending':
                return <FavouritesTab />;
            case 'manage':
                return <ManageTab />;
            default:
                return null;
        }
    };

    return (
        <AKSafeAreaView>
            <View style={[layout.flex_1]}>
                <View style={[layout.flex_1]}>
                    <TabView
                        navigationState={{ index, routes }}
                        renderScene={renderScene}
                        onIndexChange={setIndex}
                        initialLayout={{ width: width}}
                        renderTabBar={renderTabBar}
                        commonOptions={{
                            label: ({ route, labelText, focused, color }) => (
                                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium,{
                                    color: focused ? c.content.default.default : c.content.default.subdued
                                }]}>{labelText}</Text>
                            )
                        }}
                    />
                </View>
            </View>
        </AKSafeAreaView>
    )
}

export default Groups;
