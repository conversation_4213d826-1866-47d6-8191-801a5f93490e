import { AKTextInput } from "@/components/atoms";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { useNavBar } from "@/hooks/useNavBar";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useEffect, useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import PrivacyEyeIcon from "@/theme/assets/images/PrivacyEyeIcon.png";
import { PrivacyUpdatorBottomSheet, ScreenLoader } from "@/components/molecules";
import _ from "lodash";
import { useNavigation } from "@react-navigation/native";

function NewGroup() {

    const { setupNavBar } = useNavBar('New Group', true, true);
    const { layout, fonts, gutters } = useTheme();
    const c = colorTokens();
    const navigation = useNavigation();

    const [currentPrivacy, setCurrentPrivacy] = useState('Members Only');
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [form, setForm] = useState({
        name: '',
        desc: '',
    })

    useEffect(() => {
        setupNavBar();
        navigation.setOptions({
            headerRight: headerRightButton,
        })
    }, []);

    function onPressPrivacy() {
        setIsModalVisible(true);
    }

    function onSubmit() {
        console.log("Form:::", form)
        setIsLoading(true);
        setTimeout(() => {
            setIsLoading(false);
            navigation.goBack()
        }, 2000)
    }

    function onValueChange(name: string, text: string): void {
        setForm({..._.set(form, name, text)});
        navigation.setOptions({
            headerRight: headerRightButton,
        })
    }

    function headerRightButton() {
        const isDisabled = form.name == '' || form.desc == '';
        return (
            <TouchableOpacity disabled={isDisabled} onPress={onSubmit}>
                <View style={[gutters.marginRight_12, gutters.paddingVertical_8, gutters.paddingHorizontal_16, layout.itemsCenter, layout.justifyCenter,{ backgroundColor: c.fill.bold.neutrals.rest, opacity: isDisabled ? 0.45 : 1, borderRadius: 8}]}>
                    <Text style={[ fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: c.content.onBold.default.default}]}>Create</Text>
                </View>
            </TouchableOpacity>
        );
    }

    return (
        <AKSafeAreaView>
            <View style={[layout.flex_1, gutters.paddingHorizontal_16, gutters.paddingTop_24, gutters.marginTop_8, { gap: 24, borderTopWidth: 1, borderTopColor: c.stoke.default.default }]}>
                <AKTextInput label="Group Name" name="name" value={form.name} onChangeValue={onValueChange} />
                <AKTextInput label="Description" name="desc" isTextArea value={form.desc} onChangeValue={onValueChange} />
                <View style={[{gap: 12}]}>
                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, gutters.marginBottom_4, { color: c.content.default.emphasis }]}>Privacy</Text>
                    <TouchableOpacity onPress={onPressPrivacy} style={[layout.row, layout.itemsCenter, { gap: 4, backgroundColor: c.background.default.neutrals.secondary, alignSelf: 'flex-start', padding: 6, borderRadius: 12 }]}>
                        <AKIcon source={PrivacyEyeIcon} />
                        <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, fonts.Medium, { color: c.content.default.subdued }]}>{currentPrivacy}</Text>
                    </TouchableOpacity>
                </View>
            </View>
            <PrivacyUpdatorBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} currentPrivacy={currentPrivacy} setCurrentPrivacy={setCurrentPrivacy} />
            {
                isLoading && (
                    <ScreenLoader loadingText="Creating Group" />
                )
            }
        </AKSafeAreaView>
    )
}

export default NewGroup;
