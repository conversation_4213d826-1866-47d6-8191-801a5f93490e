import AccountSection, { ProfileItem } from "@/components/molecules/AccountSection/AccountSection";
import { deleteUser, getProfile } from "@/services/users";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Text, TouchableOpacity, View } from "react-native";
import Spinner from "react-native-loading-spinner-overlay";
import { getAccountSectionItems } from "./utility";
import tempAuth from "@/services/users/tempAuth";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { AUTH_STORRAGE_KEYS } from "@/utils/constants";
import { storage } from "@/App";
import { revokeFcmToken } from "@/integrations/fireabase/messaging";
import { useQueryClient } from "@tanstack/react-query";
import { useNavBar } from "@/hooks/useNavBar";
import { colorTokens } from "@/theme/colorTokens";

export type AccountNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'OnBoarding',
    'ChangePassword'
>;
function Settings() {
    const { t } = useTranslation(['account', 'home']);
    const {
        layout,
        gutters,
        fonts,
    } = useTheme();
    const queryClient = useQueryClient()

    const c = colorTokens();

    const navigation = useNavigation<AccountNavigationProp>();
    const { setupNavBar } = useNavBar('Settings')

    const { data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
    });

    useEffect(() => {
        setupNavBar()
    },[])

    const [isLoading, setIsLoading] = useState(false)

    const tempAuthMutation = useMutation({
        mutationFn: () => tempAuth(),
        onSuccess: (response) => {
            setIsLoading(false)
            navigation.navigate('AKWebView', {source: `${response.web_url}?paymentKey=${response.key}`})
        },
        onError: (error) => {
            setIsLoading(false)
            Alert.alert("Error!", error.message)
        }
    })
    
    const accountSectionItems = getAccountSectionItems(navigation, data?.user_profile?.id);

    function onItemPressed(item: ProfileItem) {
        if (item.section == 0 && item.index == 1) {
            setIsLoading(true)
            tempAuthMutation.mutate()
        }
        else if (item.onPressAction != null ) {
            item.onPressAction();
        }
    }
    const deleteAccountMutation = useMutation(
        {
            mutationFn: () => deleteUser(),
            onSuccess: async () => {
                let keys = storage.getAllKeys()
                keys.map(key => {
                    if (key != AUTH_STORRAGE_KEYS.USERID) {
                        storage.delete(key)
                    }
                } )
                // storage.clearAll()
                await queryClient.cancelQueries()
                queryClient.removeQueries()
                revokeFcmToken()
                navigation.reset({
                    index: 0,
                    routes: [{name: 'NewLogin'}]
                })
                setIsLoading(false)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t('home:Error'), error.message)
            }
        },
    );
    function showDeleteAlert() {
        let options = [
            {
                text: 'Cancel',
            },
            {
                text: 'Delete',
                onPress: () => {
                    setIsLoading(true)
                    deleteAccountMutation.mutate()
                },
                style: 'destructive',
            },
        ]
        Alert.alert('Delete Account?', 'Are you sure you want to delete the account? On deleting all of your data will be permanently deleted.', options)
                
    }

    return (
        <AKSafeAreaView>
            <View style={[layout.flex_1, gutters.marginBottom_16, gutters.marginHorizontal_16, ]}>
                <Spinner
                    visible={isLoading}
                />
                <View>
                {
                    accountSectionItems.map((sectionItem, index) => (
                        <View key={index} style={[gutters.marginTop_16]}>
                            <AccountSection items={sectionItem} onItemPressed={onItemPressed}/>
                        </View>
                    ))
                }
                </View>
                <TouchableOpacity style={[gutters.paddingVertical_8, gutters.paddingHorizontal_16]} onPress={showDeleteAlert}>
                    <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, gutters.marginTop_16, {color: c.content.error.default}]}>Delete Account</Text>
                </TouchableOpacity>
            </View>
        </AKSafeAreaView>
    )
}
export default Settings;
