type NotificationItem = {
	name: string;
	selected: boolean;
	value: string;
};

type NotificationSection = {
	data: NotificationItem[];
	title: string;
};

export function getAccountSectionItems(navigation: any) {
	return [
		[
			{
				label: 'Account',
				icon: require('@/theme/assets/images/ProfileIcon.png'),
				section: 0,
				index: 0,
				onPressAction: () => navigation.navigate('AccountSettings'),
			},
			{
				label: 'Payment',
				icon: require('@/theme/assets/images/CreditCardIcon.png'),
				section: 0,
				index: 1,
				onPressAction: () => navigation.navigate('NotificationPreference'),
			},
			{
				label: 'Referrals',
				icon: require('@/theme/assets/images/ReferralIcon.png'),
				section: 0,
				index: 2,
				onPressAction: () => navigation.navigate('Referrals'),
			},
		],
		[
			{
				label: 'Blocked',
				icon: require('@/theme/assets/images/Account/BlockedIcon.png'),
				section: 1,
				index: 1,
				onPressAction: () => navigation.navigate('BlockedAccounts'),
			},
		],
		[
			{
				label: 'Notifications',
				icon: require('@/theme/assets/images/Account/NotificationIcon.png'),
				section: 2,
				index: 0,
				onPressAction: () => navigation.navigate('ManageNotifications'),
			}
		],
	];
}

export function getNotificationsPreferenceSectionData(preferenceData: any) {
	const result: NotificationSection[] = [];
	Object.keys(preferenceData).forEach((key) => {
		result.push({
			title: key.replaceAll('_', ' ').toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
			data: preferenceData[key] || [],
		})
	});
	return result;
}
