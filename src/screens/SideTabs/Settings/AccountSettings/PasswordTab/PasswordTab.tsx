import { AKTextInput } from "@/components/atoms";
import { useTheme } from "@/theme";
import { useEffect, useState } from "react";
import { View } from "react-native";
import _ from "lodash";
import { useMutation } from "@tanstack/react-query";
import changePassword from "@/services/users/changePassword";
import { useTranslation } from "react-i18next";
import { Alert } from "react-native";
import { useNavBar } from "@/hooks/useNavBar";
import { useCustomToast } from "@/hooks/useCustomToast";
import Spinner from "react-native-loading-spinner-overlay";

type Props = {
    isVisible: boolean;
}

function PasswordTab({ isVisible }: Props) {

    const { gutters } = useTheme();

    const { t } = useTranslation(['auth', 'account', 'sideMenu']);
    const { updateRightButton } = useNavBar();

    const [isLoading, setIsLoading] = useState(false)

    const showToast = useCustomToast();

    const [isSaveButtonEnabled, setIsSaveButtonEnabled] = useState(true);
    const [form, setForm] = useState({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: '',
    })
    const [info, setInfo] = useState({
        currentPassword: {
            message: '',
            isValid: true
        },
        confirmNewPassword: {
            message: '',
            isValid: true
        },
    });

    const [newPasswordRules, setNewPasswordRules] = useState([
        { name: 'have at least 8 characters', value: false },
        { name: 'does not have spaces', value: true },
    ]);

    const changePasswordMutation = useMutation(
        {
            mutationFn: (data: any) => changePassword(data),
            onSuccess: (response) => {
                setIsLoading(false)
                if (response.success == false) {
                    setInfo({..._.set(info, 'currentPassword', {
                        message: 'Entered password is incorrect! Try again.', 
                        isValid: false
                    })});
                } else {
                    resetState();
                    showToast("Password Changed Successfully");
                }
                
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t("account:Error"), error.message)
            }
        },
    );

    function resetState() {
        setForm({
            currentPassword: '',
            newPassword: '',
            confirmNewPassword: '',
        })
        setInfo({
            currentPassword: {
                message: '',
                isValid: true
            },
            confirmNewPassword: {
                message: '',
                isValid: true
            },
        });
        setNewPasswordRules([
            { name: 'have at least 8 characters', value: false },
            { name: 'does not have spaces', value: true },
        ]);
        setIsSaveButtonEnabled(true)
    }

    function shouldEnableSaveButton(): boolean {
        return form.currentPassword != '' && form.newPassword != '' && form.confirmNewPassword == form.newPassword && newPasswordRules.every((rule) => rule.value)
    }

    function onValueChange(name: string, text: string): void {
        if (name == 'newPassword') {
            let rules = [...newPasswordRules]
            rules[0].value = text.length >= 8
            rules[1].value = !text.includes(' ')
            setNewPasswordRules(rules)
            if(form.confirmNewPassword != '') {
                setInfo({..._.set(info, 'confirmNewPassword', {
                    message: text != form.confirmNewPassword ? 'Passwords do not match' : "It's a match!", 
                    isValid: text == form.confirmNewPassword 
                })});
            }
        }
        if (name == 'confirmNewPassword') {
            setInfo({..._.set(info, 'confirmNewPassword', {
                message: text == '' ? '' : text != form.newPassword ? 'Passwords do not match' : "It's a match!", 
                isValid: text == '' ? false : text == form.newPassword 
            })});
        }
        setForm({..._.set(form, name, text)});
        updateRightButton({
            disabled: !shouldEnableSaveButton(),
            onPress: onUpdatePassword
        });
    }

    function onUpdatePassword(): void {
        let data = {
            current_password: form.currentPassword,
            password: form.newPassword,
            password_confirmation: form.confirmNewPassword
        }
        setIsLoading(true)
        changePasswordMutation.mutate(data)
    }

    function onFocusCurrentPassword(): void {
        setInfo({..._.set(info, 'currentPassword', {
            message: '', 
            isValid: true
        })});
    }

    useEffect(() => {
        if (isVisible) {
            updateRightButton({
                disabled: isSaveButtonEnabled,
                onPress: onUpdatePassword
            });
        }
    }, [isVisible, isSaveButtonEnabled]);

    return (
        <View style={[gutters.marginHorizontal_16, gutters.marginTop_16]}>
            <Spinner
                visible={isLoading}
            />
            <View style={[{ gap: 12 }]}>
                <View>
                    <AKTextInput label="Current Password" isPassword={true} onChangeValue={onValueChange} name="currentPassword" value={form.currentPassword} info={info.currentPassword} onFocus={onFocusCurrentPassword} />
                </View>
                <View>
                    <AKTextInput label="New Password" isPassword={true} onChangeValue={onValueChange} name="newPassword" value={form.newPassword} rules={newPasswordRules} />
                </View>
                <View>
                    <AKTextInput label="Confirm New Password" isPassword={true} onChangeValue={onValueChange} name="confirmNewPassword" value={form.confirmNewPassword} info={info.confirmNewPassword} />
                </View>
            </View>
        </View>
    )
}
export default PasswordTab;
