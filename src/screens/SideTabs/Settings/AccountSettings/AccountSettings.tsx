import { useTheme } from "@/theme";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Dimensions, Image, Platform, ScrollView, Text, TouchableOpacity, View } from "react-native";
import Spinner from "react-native-loading-spinner-overlay";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { useNavBar } from "@/hooks/useNavBar";
import { colorTokens } from "@/theme/colorTokens";
import { TabView, TabBar, TabBarProps, SceneMap, SceneRendererProps } from 'react-native-tab-view';
import ProfileTab from "./ProfileTab/ProfileTab";
import PersonalDetailsTab from "./PersonalDetailsTab/PersonalDetailsTab";
import PasswordTab from "./PasswordTab/PasswordTab";

type RouteType = {
    key: 'profile' | 'personalDetails' | 'password';
    title: string;
};

type RenderSceneProps = SceneRendererProps & {
    route: RouteType;
};

const routes: RouteType[] = [
    { key: 'profile', title: 'Profile' },
    { key: 'personalDetails', title: 'Personal Details' },
    { key: 'password', title: 'Password' },
];

function AccountSettings() {
    const { t } = useTranslation(['account', 'home']);
    const {
        colors,
        layout,
        gutters,
        fonts,
        backgrounds,
        borders,
    } = useTheme();

    const route = useRoute();

    const { width } = Dimensions.get('window');

    const c = colorTokens();

    const navigation = useNavigation();
    const { setupNavBar } = useNavBar('Account Settings', true, true);

    useEffect(() => {
        setupNavBar()
    },[])

    const [isLoading, setIsLoading] = useState(false);
    const [index, setIndex] = useState(route.params?.tabIndex ?? 0);

    const renderTabBar = (props: TabBarProps<RouteType>) => (
        <TabBar
            {...props}
            indicatorStyle={{ backgroundColor: c.stoke.primary.default, height: 3, borderRadius: 10 }}
            style={{ backgroundColor: c.custom.white, borderBottomWidth: 1, borderBottomColor: c.stoke.default.default }}
        />
    );

    const renderScene = ({ route }: RenderSceneProps) => {
        switch (route.key) {
            case 'profile':
                return <ProfileTab isVisible={index == 0} />;
            case 'personalDetails':
                return <PersonalDetailsTab isVisible={index == 1} />;
            case 'password':
                return <PasswordTab isVisible={index == 2} />;
            default:
                return null;
        }
    };

    return (
        <AKSafeAreaView>
            <View style={[layout.flex_1]}>
                <Spinner
                    visible={isLoading}
                />
                <View style={[layout.flex_1]}>
                    <TabView
                        navigationState={{ index, routes }}
                        renderScene={renderScene}
                        onIndexChange={setIndex}
                        initialLayout={{ width: width}}
                        renderTabBar={renderTabBar}
                        commonOptions={{
                            label: ({ route, labelText, focused, color }) => (
                                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium,{
                                    color: focused ? c.content.default.default : c.content.default.subdued
                                }]}>{labelText}</Text>
                            )
                        }}
                    />
                </View>
            </View>
        </AKSafeAreaView>
    )
}
export default AccountSettings;
