import { StageTracker } from "@/components/molecules";
import { View } from "react-native";
import { useTheme } from "@/theme";
import BioDetails from "./BioDetails/BioDetails";
import AboutYouDetails from "./AboutYouDetails/AboutYouDetails";
import InterestsDetails from "./InterestsDetails/InterestsDetails";
import { useEffect, useState } from "react";
import { useNavBar } from "@/hooks/useNavBar";
import { Stage } from "../../constants";
import Animated, { SlideInLeft, SlideInRight, SlideOutLeft, SlideOutRight } from "react-native-reanimated";

type Props = {
    isVisible: boolean;
}

function PersonalDetailsTab({ isVisible }: Props) {

    const { gutters, layout } = useTheme();
    const { updateRightButton } = useNavBar();

    const [index, setIndex] = useState(0);
    const [isViewable, setIsViewable] = useState(true);
    const [isRightDirection, setIsRightDirection] = useState(true);

    function onChangeStage(updateIndex: number) {
        setIsRightDirection(updateIndex > index);
        setTimeout(() => {
            setIsViewable(false);
        }, 50);
        setTimeout(() => {
            setIndex(updateIndex);
            setIsViewable(true);
        }, 250);
    }

    const stages: Stage[] = [{
        label: 'Bio',
        index: 0,
    }, {
        label: 'About You',
        index: 1,
    }, {
        label: 'Interests',
        index: 2,
    }];

    useEffect(() => {
        if (isVisible) {
            updateRightButton({}, false);
        }
    }, [isVisible]);

    return (
        <View style={[layout.flex_1, gutters.marginHorizontal_16, gutters.marginTop_16]}>
            <View style={[gutters.paddingHorizontal_40, gutters.marginBottom_16]}>
                <StageTracker stages={stages} activeStage={stages[index]} />
            </View>
            {
                !isViewable ? null : index == 0 ? (
                    <Animated.View style={[layout.flex_1]} exiting={isRightDirection ? SlideOutLeft : SlideOutRight} entering={isRightDirection ? SlideInRight : SlideInLeft}>
                        <BioDetails onChangeStage={onChangeStage} />
                    </Animated.View>
                ) : index == 1 ? (
                    <Animated.View exiting={isRightDirection ? SlideOutLeft : SlideOutRight} entering={isRightDirection ? SlideInRight : SlideInLeft}>
                        <AboutYouDetails onChangeStage={onChangeStage} />
                    </Animated.View>
                ) : (
                    <Animated.View exiting={isRightDirection ? SlideOutLeft : SlideOutRight} entering={isRightDirection ? SlideInRight : SlideInLeft}>
                        <InterestsDetails onChangeStage={onChangeStage} />
                    </Animated.View>
                )
            }
        </View>
    )
}
export default PersonalDetailsTab;
