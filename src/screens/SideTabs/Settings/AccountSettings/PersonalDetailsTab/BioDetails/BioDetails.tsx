import { AKTextInput } from "@/components/atoms";
import { useEffect, useState } from "react";
import { View } from "react-native";
import CalendarIcon from "@/theme/assets/images/CalendarIcon.png";
import DatePicker from "react-native-date-picker"
import moment from "moment";
import _ from "lodash";
import { PersonalDetailsFooter } from "@/components/molecules";
import { getProfile } from "@/services/users";
import { useQuery } from "@tanstack/react-query";
import { usePersonalDetailsUpdator } from "@/hooks/usePersonalDetailsUpdator";
import Spinner from "react-native-loading-spinner-overlay";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

type Props = {
    onChangeStage: (index: number) => void;
}

type ProfileData = {
    date_of_birth?: string;
    nick_name?: string;
    address?: string;
    phone_number?: string;
}

function BioDetails({ onChangeStage }: Props) {

    const { data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });

    const [openDatePicker, setOpenDatePicker] = useState(false)
    const [form, setForm] = useState({
        birthdate: '',
        nickname: '',
        address: '',
        phoneNumber: '',
    });

    function onSuccess(): void {
        onChangeStage(1)
    }

    const {isLoading, onUpdateProfile} = usePersonalDetailsUpdator(onSuccess);

    useEffect(() => {
        setForm({..._.set(form, 'birthdate', data?.user_profile?.date_of_birth)});
        setForm({..._.set(form, 'nickname', data?.user_profile?.nick_name)});
        setForm({..._.set(form, 'address', data?.user_profile?.address)});
        setForm({..._.set(form, 'phoneNumber', data?.user_profile?.phone_number)});
    }, [data])

    function showDatePicker(): void {
        setOpenDatePicker(true)
    }

    function onValueChange(name: string, text: string): void {
        setForm({..._.set(form, name, text)});
    }

    function onPressNext(): void {
        let profileData: ProfileData = {}
        if (form.birthdate != '' && form.birthdate != null) {
            profileData.date_of_birth = form.birthdate
        }
        if (form.nickname != '' && form.nickname != null) {
            profileData.nick_name = form.nickname
        }
        if (form.address != '' && form.address != null) {
            profileData.address = form.address
        }
        if (form.phoneNumber != '' && form.phoneNumber != null) {
            profileData.phone_number = form.phoneNumber
        }
        if (profileData == null) {
            onChangeStage(1)
        } else {
            onUpdateProfile(profileData)
        }
    }

    return (
        <KeyboardAwareScrollView contentContainerStyle={{flexGrow: 1}}>
            <Spinner
                visible={isLoading}
            />
            <View style={[{ gap: 12 }]}>
                <AKTextInput
                    label="Date of birth"
                    name="birthdate"
                    rightIcon={CalendarIcon}
                    onPressInput={showDatePicker}
                    value={form.birthdate}
                />
                <AKTextInput label="Nick Name" onChangeValue={onValueChange} name="nickname" value={form.nickname} />
                <AKTextInput label="Address" onChangeValue={onValueChange} name="address" value={form.address} />
                <AKTextInput label="Phone Number" onChangeValue={onValueChange} name="phoneNumber" value={form.phoneNumber} keyboardType="numeric" />
            </View>
            <DatePicker
                modal
                mode="date"
                open={openDatePicker}
                date={new Date()}
                onConfirm={(date) => {
                    setOpenDatePicker(false)
                    setForm({..._.set(form,  'birthdate', moment(date).format('YYYY-MM-DD').toString())});
                }}
                onCancel={() => {
                    setOpenDatePicker(false)
                }}
            />
            <PersonalDetailsFooter onNext={onPressNext} />
        </KeyboardAwareScrollView>
    )
}

export default BioDetails;