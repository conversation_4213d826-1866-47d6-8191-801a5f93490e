import { AKTextInput } from "@/components/atoms";
import { useEffect, useState } from "react";
import { Alert, View } from "react-native";
import _ from "lodash";
import { PersonalDetailsDropdownBottomSheet, PersonalDetailsFooter } from "@/components/molecules";
import { useQuery } from "@tanstack/react-query";
import { getProfile } from "@/services/users";
import { findSeletedOption } from "@/utils/utility";
import ChevronDown from "@/theme/assets/images/SocialConnect/ChevronDown.png";
import { usePersonalDetailsUpdator } from "@/hooks/usePersonalDetailsUpdator";
import Spinner from "react-native-loading-spinner-overlay";
import { DropdownOption } from "../../../constants";
import { useCustomToast } from "@/hooks/useCustomToast";

type SelectedOption = {
    field: string;
    value: string;
}

type ProfileData = {
    joining_motivation_options?: string;
    interests_option?: string;
}

function InterestsDetails({ onChangeStage }: any) {

    const { data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });

    const showToast = useCustomToast();

    function onSuccess() {
        onChangeStage(0);
        showToast("Profile Updated Successfully");
    }

    const {isLoading, onUpdateProfile} = usePersonalDetailsUpdator(onSuccess);

    const [form, setForm] = useState({
        joining_motivation_options: {
            name: '',
            value: '',
        },
        interests_option: {
            name: '',
            value: '',
        },
    });
    const [isModalVisible, setIsModalVisible] = useState(false)
    const [options, setOptions] = useState<DropdownOption[] | undefined>([])
    const [selectedOption, setSelectedOption] = useState<SelectedOption>({
        field: '',
        value: '',
    })

    useEffect(() => {
        setForm({..._.set(form, 'joining_motivation_options', findSeletedOption(data?.user_profile.joining_motivation_options))});
        setForm({..._.set(form, 'interests_option', findSeletedOption(data?.user_profile.interests_option))});
    }, [data])

    function onFieldTapped(name: 'joining_motivation_options' | 'interests_option') {
        setIsModalVisible(true)
        setSelectedOption({
            field: name,
            value: form[name].value
        })
        setOptions(data?.user_profile[name])
    }

    function onPressOption(name: string, option: DropdownOption): void {
        setSelectedOption({
            field: name,
            value: option.value
        })
        setForm({..._.set(form, name, option)});
    }

    function onPressNext(): void {
        let data: ProfileData = {}
        if (form.joining_motivation_options.value != '') {
            data.joining_motivation_options = form.joining_motivation_options.name
        }
        if (form.interests_option.value != '') {
            data.interests_option = form.interests_option.name
        }
        if (data == null) {
            onSuccess();
        } else {
            onUpdateProfile(data)
        }
    }

    return (
        <View>
            <Spinner
                visible={isLoading}
            />
            <View style={[{ gap: 12 }]}>
                <AKTextInput label="Motivation to join" onPressInput={() => onFieldTapped('joining_motivation_options')} name="joining_motivation_options" value={form.joining_motivation_options.value} rightIcon={ChevronDown} />
                <AKTextInput label="Areas you’re most interested in" onPressInput={() => onFieldTapped('interests_option')} name="interests_option" value={form.interests_option.value} rightIcon={ChevronDown} />
            </View>
            <PersonalDetailsFooter onBack={() => onChangeStage(1)} onNext={onPressNext} isLast />
            <PersonalDetailsDropdownBottomSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                options={options}
                onPressOption={onPressOption}
                selectedOption={selectedOption}
            />
        </View>
    )
}
export default InterestsDetails;
