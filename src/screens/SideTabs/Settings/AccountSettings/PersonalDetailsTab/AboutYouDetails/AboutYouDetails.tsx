import { AKTextInput } from "@/components/atoms";
import { useEffect, useState } from "react";
import { View } from "react-native";
import _ from "lodash";
import { PersonalDetailsDropdownBottomSheet, PersonalDetailsFooter } from "@/components/molecules";
import { getProfile } from "@/services/users";
import { useQuery } from "@tanstack/react-query";
import { findSeletedOption } from "@/utils/utility";
import ChevronDown from "@/theme/assets/images/SocialConnect/ChevronDown.png";
import { usePersonalDetailsUpdator } from "@/hooks/usePersonalDetailsUpdator";
import Spinner from "react-native-loading-spinner-overlay";
import { DropdownOption } from "../../../constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

type Props = {
    onChangeStage: (index: number) => void;
}

type ProfileData = {
    education_level?: string;
    income_level?: string;
    marital_status?: string;
    children_option?: string;
}

function AboutYouDetails({ onChangeStage }: Props) {

    const [isModalVisible, setIsModalVisible] = useState(false)
    const [options, setOptions] = useState<DropdownOption[] | undefined>([])
    const [selectedOption, setSelectedOption] = useState({})
    const [form, setForm] = useState({
        education_level: {
            name: '',
            value: '',
        },
        income_level: {
            name: '',
            value: '',

        },
        marital_status: {
            name: '',
            value: '',

        },
        children_option: {
            name: '',
            value: '',
        }
    })

    function onSuccess(): void {
        onChangeStage(2)
    }

    const {isLoading, onUpdateProfile} = usePersonalDetailsUpdator(onSuccess);

    const { data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });

    useEffect(() => {
        setForm({..._.set(form, 'education_level', findSeletedOption(data?.user_profile.education_level))});
        setForm({..._.set(form, 'income_level', findSeletedOption(data?.user_profile.income_level))});
        setForm({..._.set(form, 'marital_status', findSeletedOption(data?.user_profile.marital_status))});
        setForm({..._.set(form, 'children_option', findSeletedOption(data?.user_profile.children_option))});
    }, [data])

    function onPressOption(name: string, option: DropdownOption): void {
        setSelectedOption({
            field: name,
            value: option.value
        })
        setForm({..._.set(form, name, option)});
    }

    function onFieldTapped(name: 'education_level' | 'income_level' | 'marital_status' | 'children_option'): void {
        setIsModalVisible(true)
        setSelectedOption({
            field: name,
            value: form[name].value
        })
        setOptions(data?.user_profile[name])
    }

    function onPressNext() {
        let data: ProfileData = {}
        if (form.education_level.value != '') {
            data.education_level = form.education_level.name
        }
        if (form.income_level.value != '') {
            data.income_level = form.income_level.name
        }
        if (form.marital_status.value != '') {
            data.marital_status = form.marital_status.name
        }
        if (form.children_option.value != '') {
            data.children_option = form.children_option.name
        }
        if (data == null) {
            onChangeStage(2)
        } else {
            onUpdateProfile(data)
        }
    }

    return (
        <KeyboardAwareScrollView>
            <Spinner
                visible={isLoading}
            />
            <View style={[{ gap: 12 }]}>
                <AKTextInput label="Education" onPressInput={() => onFieldTapped('education_level')} name="education_level" value={form.education_level.value} rightIcon={ChevronDown} />
                <AKTextInput label="Income Level" onPressInput={() => onFieldTapped('income_level')} name="income_level" value={form.income_level.value} rightIcon={ChevronDown} />
                <AKTextInput label="Marital Status" onPressInput={() => onFieldTapped('marital_status')} name="marital_status" value={form.marital_status.value} rightIcon={ChevronDown} />
                <AKTextInput label="Children?" onPressInput={() => onFieldTapped('children_option')} name="children_option" value={form.children_option.value} rightIcon={ChevronDown} />
            </View>
            <PersonalDetailsFooter onBack={() => onChangeStage(0)} onNext={onPressNext} />
            <PersonalDetailsDropdownBottomSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                options={options}
                onPressOption={onPressOption}
                selectedOption={selectedOption}
            />
        </KeyboardAwareScrollView>
    )
}
export default AboutYouDetails;
