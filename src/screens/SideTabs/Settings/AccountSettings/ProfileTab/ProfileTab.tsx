import { getProfile } from "@/services/users";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import { useNavBar } from "@/hooks/useNavBar";
import { AKFastImage, AKTextInput } from "@/components/atoms";
import _ from "lodash";
import { SocialConnectUserPictureUpdatorBottomSheet } from "@/components/molecules";
import { UserPictureType } from "@/utils/constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { usePersonalDetailsUpdator } from "@/hooks/usePersonalDetailsUpdator";
import Spinner from "react-native-loading-spinner-overlay";
import { useCustomToast } from "@/hooks/useCustomToast";

type Props = {
    isVisible: boolean;
}

type ProfileData = {
    first_name?: string;
    last_name?: string;
    bio?: string;
}

function ProfileTab({ isVisible }: Props) {

    const { layout, gutters, fonts } = useTheme();
    const c = colorTokens();

    const showToast = useCustomToast();
    const { updateRightButton } = useNavBar();

    const { data, refetch } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });

    function onSuccess() {
        showToast("Profile Updated Successfully");
        updateRightButton({
            disabled: true,
            onPress: () => {}
        });
        refetch();
    }

    const { isLoading, onUpdateProfile } = usePersonalDetailsUpdator(onSuccess);

    const [form, setForm] = useState({
        firstName: '',
        lastName: '',
        bio: '',
        email: '',
        userName: ''
    })
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [pictureType, setPictureType] = useState('');

    function updateSaveButton(isDisabled: boolean = false): void {
        updateRightButton({
            disabled: isDisabled,
            onPress: () => onSubmit()
        });
    }

    useEffect(() => {
        if (isVisible) {
            updateSaveButton(true);
        }
    }, [isVisible]);

    useEffect(() => {
        setForm({..._.set(form, 'firstName', data?.user_profile?.first_name)});
        setForm({..._.set(form, 'lastName', data?.user_profile?.last_name)});
        setForm({..._.set(form, 'bio', data?.user_profile?.bio)});
        setForm({..._.set(form, 'email', data?.user_profile?.email)});
        setForm({..._.set(form, 'userName', data?.user_profile?.user_name)});
    }, [data])

    function onSubmit() {
        let profileData: ProfileData = {}
        if (form.firstName != data?.user_profile?.first_name) {
            profileData.first_name = form.firstName
        }
        if (form.lastName != data?.user_profile?.last_name) {
            profileData.last_name = form.lastName
        }
        if (form.bio != data?.user_profile?.bio) {
            profileData.bio = form.bio
        }
        onUpdateProfile(profileData)
    }

    function onValueChange(name: string, text: string): void {
        setForm({..._.set(form, name, text)});
        let isDisabled = false;
        if (form.firstName == '' || form.lastName == '' || form.bio == '') {
            isDisabled = true;
        }
        updateSaveButton(isDisabled);
    }

    function onEditPress(type: string): void {
        setPictureType(type)
        setIsModalVisible(true);
    }

    return (
        <KeyboardAwareScrollView style={[gutters.marginHorizontal_16]} showsVerticalScrollIndicator={false} contentContainerStyle={{gap: 12}}>
            <Spinner
                visible={isLoading}
            />
            <View style={[layout.itemsCenter, gutters.paddingTop_12]}>
                <View style={[layout.row, layout.justifyBetween, layout.itemsCenter, { width: '100%', marginVertical: 5 }]}>
                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, { color: c.content.default.emphasis }]}>Profile Picture</Text>
                    <TouchableOpacity onPress={() => onEditPress(UserPictureType.PROFILE)}>
                        <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, { color: c.content.default.default }]}>Edit</Text>
                    </TouchableOpacity>
                </View>
                <AKFastImage
                    uri={data?.user_profile?.profile_photo}
                    style={{height: 120, width: 120, borderRadius: 120}}
                    resizeMode={FastImage.resizeMode.cover}
                />
            </View>
            <View style={[layout.itemsCenter]}>
                <View style={[layout.row, layout.justifyBetween, layout.itemsCenter, { width: '100%', marginVertical: 5 }]}>
                    <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, { color: c.content.default.emphasis }]}>Cover Picture</Text>
                    <TouchableOpacity onPress={() => onEditPress(UserPictureType.COVER)}>
                        <Text style={[fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, { color: c.content.default.default }]}>Edit</Text>
                    </TouchableOpacity>
                </View>
                <AKFastImage
                    placeholder={require('@/theme/assets/images/CoverPlaceholder.png')}
                    uri={data?.user_profile?.cover_photo}
                    style={{height: 200, width: '100%', borderRadius: 12}}
                    resizeMode={FastImage.resizeMode.cover}
                />
            </View>
            <AKTextInput label="First Name" onChangeValue={onValueChange} name="firstName" value={form.firstName} />
            <AKTextInput label="Last Name" onChangeValue={onValueChange} name="lastName" value={form.lastName} />
            <AKTextInput label="Bio" isTextArea onChangeValue={onValueChange} name="bio" value={form.bio} />
            <AKTextInput label="Email" isReadOnly onChangeValue={onValueChange} name="email" value={form.email} />
            <AKTextInput label="Username" isReadOnly onChangeValue={onValueChange} name="userName" value={form.userName} />
            <SocialConnectUserPictureUpdatorBottomSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                type={pictureType}
                successCallback={refetch}
            />
        </KeyboardAwareScrollView>
    )
}
export default ProfileTab;
