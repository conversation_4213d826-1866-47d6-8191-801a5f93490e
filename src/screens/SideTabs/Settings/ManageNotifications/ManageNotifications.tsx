import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { getProfile } from "@/services/users";
import notificationPreferenceListing from "@/services/users/notificationPreferenceListing";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useMemo, useState, useEffect } from "react";
import { Button, SectionList, Switch, Text, View } from "react-native";
import { getNotificationsPreferenceSectionData } from "../utility";
import { Alert } from "react-native";
import updateNotificationPreference from "@/services/users/updateNotificationPreference";
import { useTranslation } from "react-i18next";
import Spinner from "react-native-loading-spinner-overlay";
import { useQueryClient } from "@tanstack/react-query";
import { useNavBar } from "@/hooks/useNavBar";
import { notificationPreferenceSchema } from "@/types/schemas/notificationPreference";
import { z } from "zod";

type SwitchesState = {
    [key: string]: boolean;
};

function ManageNotifications() {

    const { layout, fonts, gutters } = useTheme();
    const c = colorTokens();

    const { t } = useTranslation(['account', 'sideMenu']);

    const [switchesState, setSwitchesState] = useState<SwitchesState>({});
    const [isLoading, setIsLoading] = useState(false)

    const queryClient = useQueryClient()

    const { setupNavBar, updateRightButton } = useNavBar('Manage Notifications', false, true);

    const userData = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const notificationData = useQuery({
        queryKey: ['notificationPreference'],
        queryFn: () => notificationPreferenceListing(userData.data?.user_profile.id),
    });

    const changePreferenceMutation = useMutation(
        {
            mutationFn: (data: any) => updateNotificationPreference(userData.data?.user_profile.id, data),
            onSuccess: (response) => {
                setIsLoading(false)
                updateRightButton({}, false);
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t('account:Error'), error.message)
            }
        }
    )

    useEffect(() => {
        setupNavBar();
    }, [])

    function updateSaveButton(switchesState: any): void {
        updateRightButton({
            disabled: false,
            onPress: () => onPressSave(switchesState)
        });
    }

    function onSwitchValueChange(item: z.infer<typeof notificationPreferenceSchema>): void {
        const updatedSwitchesState = {
            ...switchesState,
            [item.name]: switchesState[item.name] != undefined ? !switchesState[item.name] : !item.selected
        };
        setSwitchesState(updatedSwitchesState);
        updateSaveButton(updatedSwitchesState);
    }

    function renderItem({item}: any): JSX.Element {
        return (
            <View style={[layout.row, layout.justifyBetween, gutters.paddingBottom_8, gutters.marginBottom_16, { borderBottomWidth: 1, borderBottomColor: c.stoke.default.default }]}>
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: c.content.default.emphasis }]}>{item.value}</Text>
                <Switch
                    value={switchesState[item.name] ?? item.selected}
                    onValueChange={() => onSwitchValueChange(item)}
                    trackColor={{true: c.content.success.default}}
                    style={{transform:[{scale: 0.6}], marginRight: -8}}
                />
            </View>
        )
    }

    function renderSectionHeader({section}: any): JSX.Element {
        return (
            <View style={[gutters.marginBottom_24, gutters.marginTop_16]}>
                <Text style={[fonts.fontSizes.headings.H4, fonts.lineHeight.headings.H4, fonts.Bold, { color: c.content.default.emphasis }]}>{section.title}</Text>
            </View>
        )
    }

    function onPressSave(switchesState: any): void {
        setIsLoading(true);
        changePreferenceMutation.mutate(switchesState);
        queryClient.setQueryData(['notificationPreference'], (notif: any) => {
            const updates = switchesState;
            const preferences = notif.user_notification_preferences;
            for (const section in preferences) {
                preferences[section] = preferences[section].map((item: z.infer<typeof notificationPreferenceSchema>) => {
                    if (updates.hasOwnProperty(item.name)) {
                        return {
                            ...item,
                            selected: updates[item.name]
                        };
                    }
                    return item;
                });
            }
            return {
                ...notif,
                user_notification_preferences: preferences
            }
        })
    }

    const flattenSectionData = useMemo(() => {
        return getNotificationsPreferenceSectionData(notificationData.data?.user_notification_preferences || {});
    }, [notificationData.data]);

    return (
        <AKSafeAreaView>
            <Spinner
                visible={isLoading}
            />
            <View style={[layout.flex_1, gutters.marginHorizontal_16]}>
                <SectionList
                    sections={flattenSectionData}
                    showsVerticalScrollIndicator={false}
                    stickySectionHeadersEnabled={false}
                    contentContainerStyle={[]}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    renderSectionHeader={renderSectionHeader}
                />
            </View>
        </AKSafeAreaView>
    )
}

export default ManageNotifications;