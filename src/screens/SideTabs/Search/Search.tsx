import { UserCell } from "@/components/molecules";
import socialConnectUsers from "@/services/socialConnect/users/socialConnectUsers"
import { useTheme } from "@/theme";
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";
import { QueryClient, useInfiniteQuery, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react"
import { ActivityIndicator, Alert, FlatList, Image, RefreshControl, Text, TextInput, TouchableOpacity, View } from "react-native"
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
import _Icon from 'react-native-vector-icons/FontAwesome';
import followUnfollowUser from "@/services/socialConnect/users/followUnfollowUser";
import { isValidEmail } from "@/utils";

import { useTranslation } from "react-i18next";
import { backgroundUpload } from "react-native-compressor";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { colorTokens } from "@/theme/colorTokens";
const Icon = _Icon as React.ElementType
function Search() {

    const { t } = useTranslation(['sideTabs']);
    const [searchText, setSearchText] = useState('')
    const c = colorTokens();
    const userListingResponse = useInfiniteQuery({
        queryKey: [`social-connect-users`, searchText],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            console.log("Page Param: ;", pageParam)
            return socialConnectUsers(searchText == '' ? null : {
                search_key: (isValidEmail(searchText) == true) ? 'email' : 'name',
                search_value: searchText //(isValidEmail(searchText) == true) ? encodeURIComponent(searchText) : searchText
            },pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,

    });
    const queryClient = useQueryClient();
    const [isLoading, setIsLoading] = useState(false)
    const [searchData, setSearchData] = useState<any>(null)
    const {
		layout,
		gutters,
        borders,
        colors,
        backgrounds
	} = useTheme();
    const [isRefreshing, setIsRefreshing] = useState(false);
    const flattenData = useMemo(() => {
        console.log("User listing response Total::", userListingResponse?.data?.pages[0].total)
        return userListingResponse.data?.pages.flatMap(page => page.data) || [];
    }, [userListingResponse.data, userListingResponse.isRefetching]);
    
    function followUnfollowButtonTapped(item:  z.infer<typeof socialConnectUserSchema>) {
        let data = {
            user_id: item.id
        }
        setIsLoading(true)
        followUnFollowMutation.mutate(data)
    }
    function renderItem({item}) {
        return (
            <UserCell item={item} onPress={followUnfollowButtonTapped}/>
        )
    }
    useEffect(() => {
        console.log("refetch called")
        userListingResponse.refetch()
    }, [searchData])
    const followUnFollowMutation = useMutation(
        {
            mutationFn: (data: any) => followUnfollowUser(data),
            onSuccess: (response) => {
                setIsLoading(false)
                // setSearchText('')
                // setSearchData(null)
                queryClient.setQueriesData({ queryKey: ['social-connect-users'], exact: false }, (cacheData: any) => {
                    if (cacheData != null) {
                        let pages = cacheData?.pages?.map((page: any) => {
                            let pageData = page?.data?.map((user: any) => {
                                if (user.id === response.user.id) {
                                    return response.user;
                                } else {
                                    return user;
                                }
                            });
                            return {
                                ...page,
                                data: pageData,
                            };
                        });
                        return {
                            ...cacheData,
                            pages: pages,
                        };
                    }
                });
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        }
    );
    function onEndEditing() {
        if (searchText == '') {
            setSearchData(null)
        } else if (searchText.length <3 ){
            Alert.alert(t('sideTabs:Warning'), t('sideTabs:EnterCharactersError'));
        } else {
            let data = {
                search_key: 'name',
                search_value: searchText
            }
            setSearchData(data)
        }
    }
    const loadNext = useCallback(() => {
        userListingResponse.hasNextPage && userListingResponse.fetchNextPage();
    }, [userListingResponse.fetchNextPage, userListingResponse.hasNextPage]);
    
    const onRefresh = useCallback(() => {
    if (!isRefreshing) {
        setIsRefreshing(true);
        userListingResponse.refetch()
        .then(() => setIsRefreshing(false))
        .catch(() => setIsRefreshing(false));
    }
    }, [isRefreshing, userListingResponse.refetch]);
    return (
        <AKSafeAreaView>
            <Spinner
                visible={isLoading}
            />
            <View style={[layout.row, gutters.marginTop_16 ,layout.itemsCenter, gutters.marginHorizontal_16, borders.gray100, borders.rounded_4 ,{borderWidth: 1}]}>
                <Icon style={[gutters.margin_12]} color={colors.gray200} name='search' size={15}/>
                <TextInput 
                    style={[layout.flex_1 ,gutters.marginHorizontal_0, {height: 40}]}
                    placeholder={t('sideTabs:SearchHere')}
                    placeholderTextColor={c.content.default.subdued}
                    returnKeyType="search"
                    value={searchText}
                    onChangeText={setSearchText}
                    onEndEditing={onEndEditing}
                />
            </View>
            
            {userListingResponse?.data?.pages[0].total == 0 || userListingResponse?.data?.pages[0].total == null && 
                <View style={[layout.flex_1,layout.justifyCenter, layout.itemsCenter]}>
                    <Text>{t('sideTabs:NoSearchResult')}</Text>
                </View>
            }
            <View style={[layout.flex_1]}>
                <FlatList 
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{paddingBottom: 20}} 
                    data={userListingResponse.data?.pages[0].total != 0 ? flattenData : []}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                    onEndReached={loadNext}
                    removeClippedSubviews={true}
                    ListFooterComponent={
                        <View style={[layout.row,layout.justifyCenter, layout.itemsCenter ,{height: 100}]}>
                        {userListingResponse.isFetchingNextPage && <ActivityIndicator />}
                        </View>
                    }
                />
            </View>
        </AKSafeAreaView>
    )

}

export default Search