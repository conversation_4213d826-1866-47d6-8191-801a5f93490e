import { BlogDetailListHeader, CommentCell } from "@/components/molecules";
import { getProfile } from "@/services/users";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useRoute } from "@react-navigation/native";
import { useQuery } from "@tanstack/react-query";
import { Text, useWindowDimensions, View } from "react-native"
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import postDetail from "@/services/home/<USER>";
import { MutableRefObject, useEffect, useRef } from "react";
import { Comment } from "@/types/models/comment";
import { useTranslation } from "react-i18next";
import { withComments } from "@/components/hoc/withComments/withComments";
import { useCustomRoute } from "@/hooks/useCustomRoute";
import Spinner from "react-native-loading-spinner-overlay";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import NotFound from '@/theme/assets/images/NotFound.png'

type Props = { 
    setCurrentItemId: (id: number) => void
    commentRef: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>
    onDeleteComment: (comment: Comment) => void
    onCommentReply: (commentId: number, reply: string) => void
    onCommentSend: (text: string, isEdit: boolean, comment?: Comment, replyRef?: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>) => void
    onCommentButton: () => void
    onLikeButtonPressed: () => void
    onBookmarkButtonPressed: () => void
    onReportComment: (postId: number, commentID: number, replyID?: number, userID?: number) => void
}

function BlogDetail({ setCurrentItemId, commentRef, onDeleteComment, 
    onCommentReply, onCommentSend, onCommentButton,
    onLikeButtonPressed, onBookmarkButtonPressed, onReportComment }: Props) {
    const route = useCustomRoute(); // useRoute<RouteProp<ApplicationStackParamList, 'BlogDetail'>>();
    const flatListRef = useRef<KeyboardAwareFlatList>(null)
    const { t } = useTranslation(['home']);

    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });
    const postDetailResponse = useQuery({
        queryKey: [`postsDetail${route.params?.item?.id}`],
        queryFn: () => postDetail(route.params?.item?.module_id,route.params?.item?.id )
    })

    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
	} = useTheme();
    useEffect(() => {
        console.log(JSON.stringify(postDetailResponse.data, null, 4));
        console.log("Error::", postDetailResponse.error)
        setCurrentItemId(route.params?.item?.id);
    }, [postDetailResponse.data])

    function onEditComment(comment: Comment) {
        if (commentRef.current != null) {
            commentRef?.current.editComment(comment)
        }
        setTimeout(() => {
            if (commentRef.current != null) {
                commentRef.current.makeCommentFirstResponsder()
            }
        }, 200)
    }

    function renderItem({item}: any) {
        let comment: Comment = item
        return (
            <CommentCell 
                comment={comment} 
                onCommentReply={onCommentReply}
                onEditComment={onEditComment}
                onDeleteComment={onDeleteComment}
                onCommentSend={onCommentSend}
                onReportComment={onReportComment}
            />
        )
    }
    console.log("Parent refreshed")

    return (
        <View style={[layout.flex_1]}>
            <Spinner
                visible={postDetailResponse.isFetching}
            />
            <View style={[layout.itemsCenter,backgrounds.black, layout.justifyEnd ,{height: 116, borderBottomLeftRadius: 58, marginTop: -58}]}>
                <View style={[layout.justifyCenter, layout.itemsCenter, {height: 58}]}>
                    <Text style={[fonts.gray50, fonts.size_24,fonts.alignCenter, fonts.Medium]}>{route.params?.moduleTitle}</Text>
                </View>
            </View>
			<KeyboardAwareFlatList 
                ref={flatListRef}
                ListHeaderComponent={
                    <BlogDetailListHeader
                        postDetail={postDetailResponse?.data}
                        onLikeButtonPressed={onLikeButtonPressed}
                        onBookmarkButtonPressed={onBookmarkButtonPressed}
                        onCommentButton={onCommentButton}
                        onCommentSend={onCommentSend}
                        commentRef={commentRef}
                        profilePhoto={data?.user_profile.profile_photo}
                    />
                }
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{ paddingBottom: 80, flexGrow: 1}} 
                data={postDetailResponse.data?.comments}
                // enableAutomaticScroll={false}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
                extraHeight={150}
                keyboardShouldPersistTaps={'handled'}
                // style={[layout.flex_1]}
            />
            {postDetailResponse.data?.status == false && 
                <EmptyDataView
                    heading="Post not found"
                    desc="The post you are looking for does not exist or has been removed."
                    image={NotFound}
                    isFullScreen
                />
            }
        </View>
    )
}

export default withComments(BlogDetail, 'posts');
