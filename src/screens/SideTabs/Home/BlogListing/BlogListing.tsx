import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { ActivityIndicator, Alert, FlatList, Linking, Text, TouchableOpacity, useWindowDimensions, View } from "react-native";
import Video from 'react-native-video';
import { useRef, useState } from "react";
import { blogListingSchema } from "@/types/schemas/blogListing";
import { useQuery } from "@tanstack/react-query";
import blogListing from "@/services/home/<USER>";
import FastImage from "react-native-fast-image";
import Spinner from "react-native-loading-spinner-overlay";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import AutoHeightWebView from "react-native-autoheight-webview";
import { useTranslation } from "react-i18next";

export type BlogListingNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'BlogDetail'
>;
function BlogListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'BlogListing'>>();
    const { width } = useWindowDimensions();
    const { t } = useTranslation(['home']);
    const webRef = useRef<AutoHeightWebView>(null)

    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const [videoURL, setVideoURL] = useState(route.params.item.header_content_value)
    let currentItem = route.params.item
    const [isLoading, setIsLoading] = useState(false)
    const navigation = useNavigation<BlogListingNavigationProp>()
    const [videoLoading, setVideoLoading] = useState(false)
    const {data, isFetching } = useQuery({
        queryKey: [`blogs${route.params.item.id}`],
        queryFn: () =>  blogListing(route.params.item.id)
    });
    
    function itemPressed(item: typeof blogListingSchema['_output']) {
        console.log("Item::", item)
        navigation.navigate('BlogDetail', {item: item, moduleTitle: currentItem.title})

    }
    function renderItem({item}: any) {

        return (
            <TouchableOpacity style={[ gutters.marginVertical_12, gutters.marginHorizontal_16]} onPress={() => itemPressed(item)}>
                <View style={[{overflow: 'hidden', borderTopLeftRadius: 16, borderTopRightRadius: 16} ]}>
                    <FastImage style={[layout.flex_1, borders.rounded_4, backgrounds.green50, {width: '100%', height: 300} ]} source={{uri: item?.thumbnail}}/>
                    <View style={[gutters.marginBottom_12]}>
                        <Text numberOfLines={1} style={[ gutters.marginTop_16,fonts.size_16,fonts.bold, fonts.gray800,{alignSelf: 'flex-start'}]}>{item?.title}</Text>
                        <View style={[layout.flex_1,]}>
                            <AutoHeightWebView
                                ref={webRef}
                                style={{ width:  width-40, height: 40}}
                                source={{ html: item.content ?? '' }}
                                scalesPageToFit={false}
                                scrollEnabled={false}
                                onNavigationStateChange={(event) => {
                                    if (event.url != "about:blank") {
                                        webRef.current?.stopLoading()
                                        Linking.openURL(event.url)
                                    }
                                }}
                            />
                        </View>
                    </View>
                </View>  
            </TouchableOpacity>
        )
    }
    return (
        <View style={[layout.flex_1]}>
            <Spinner
                visible={isFetching}
            />
            <View style={[layout.itemsCenter,backgrounds.black, layout.justifyEnd ,{height: 116, borderBottomLeftRadius: 58, marginTop: -58}]}>
                <View style={[layout.justifyCenter, layout.itemsCenter, {height: 58}]}>
                    <Text style={[fonts.gray50, fonts.size_24,fonts.alignCenter, fonts.Medium]}>{currentItem.title}</Text>
                </View>
            </View>
            <View style={[, layout.flex_1]}>
                <FlatList 
                    ListHeaderComponent={() => (
                    <View style={[gutters.marginHorizontal_16,borders.rounded_16, gutters.marginTop_24,{height: 250}]}>
                        {videoLoading &&
                            <ActivityIndicator
                                animating
                                color={"gray"}
                                size="large"
                                style={{ flex: 1, position:"absolute", top:"50%", left:"45%" }}
                            />
                        }
                        <Video 
                            onLoadStart={() => setVideoLoading(true)}
                            poster={require('@/theme/assets/images/ProfilePlaceholder.png')} 
                            resizeMode="cover" 
                            repeat={true} 
                            style={[layout.flex_1,borders.rounded_16]} 
                            source={{uri: videoURL}} 
                        />
                    </View>)}
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 20}} 
                    data={data?.posts}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    numColumns={1}
                />

            </View>
            



        </View>
    )
}
export default BlogListing