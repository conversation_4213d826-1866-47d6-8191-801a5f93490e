import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useRoute } from "@react-navigation/native";
import { FlatList, ScrollView, Text, View, Linking, useWindowDimensions } from "react-native"
import FastImage from "react-native-fast-image";
import Akin<PERSON><PERSON>ogo from "@/theme/assets/images/AkinaLogo.png"
import { AKButton, AKFastImage, ImageVariant } from "@/components/atoms";
import empowerherPostDetail from "@/services/home/<USER>";
import Spinner from "react-native-loading-spinner-overlay";
import { useQuery } from "@tanstack/react-query";
import Slider from "@/theme/assets/images/Slider.png"
import { useTranslation } from "react-i18next";
import AutoHeightWebView from "react-native-autoheight-webview";
import { useRef } from "react";
function PlaceDetail() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'PlaceDetail'>>();
    const { width } = useWindowDimensions();
    const {data, isLoading } = useQuery({
        queryKey: [`empowerHer${route.params.item.module_id}/${route.params.item.empower_her_type_id}/${route.params.item.id}`],
        queryFn: () =>  empowerherPostDetail(route.params.item.module_id, route.params.item.empower_her_type_id, route.params.item.id)
    });
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const { t } = useTranslation(['home']);
    const webRef = useRef<AutoHeightWebView>(null)

    function renderItem({item}) {
        return (
            <View style={[{width: 304, height: 160}]}>
                <AKFastImage
                    uri={item.image_url}
                    style={[layout.flex_1, gutters.marginLeft_16 ,{borderRadius: 16}]}
                />
            </View>
        )
    }
    function onDetailButtonPress() {
        Linking.openURL(data?.button_link ?? '')
    }
    return (
        <ScrollView>
            <Spinner
                visible={isLoading}
            />
            <View style={[{height: 316}]}>
                <View style={[backgrounds.gray100, layout.flex_1, {borderBottomLeftRadius: 40}]}>
                    <AKFastImage
                        uri={route.params.item.thumbnail}
                        style={[layout.flex_1, {borderBottomLeftRadius: 45}]}
                    />
                    <View style={[layout.row, layout.itemsCenter ,{position: 'absolute' ,bottom: 8, left: 40, right: 0}]}>
                        <ImageVariant
                            source={AkinaLogo}
                            style={ [ {tintColor:  colors.yellow, resizeMode: 'contain' , width: 24, height: 40}]}
                        />
                        {/* <Text style={[gutters.marginLeft_12, gutters.marginRight_16 ,fonts.gray800, fonts.bold, fonts.size_16]}>{t("home:HotelsBenefitsHeading")}</Text> */}
                    </View>
                    

                </View>
                
            </View>
            <View style={[gutters.marginTop_16]}>
                <FlatList
                    data={data?.images}
                    renderItem={renderItem}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                />
            </View>
            <View style={[gutters.marginHorizontal_0, layout.row ,layout.itemsCenter, layout.justifyCenter, gutters.marginTop_16]}>
                <ImageVariant
                    source={Slider}
                    style={ [ { resizeMode: 'contain' }]}
                />
                
            </View>
            <View style={[gutters.marginHorizontal_16, gutters.marginTop_16]}>
                <Text style={[fonts.size_40, fonts.Bold]}>{data?.title}</Text>
                <Text style={[fonts.size_16, fonts.Medium]}>{data?.subtitle}</Text>
                <View style={[layout.flex_1,gutters.marginHorizontal_24]}>
                    <AutoHeightWebView
                        ref={webRef}
                        style={{ width:  width-40, marginTop: 35 }}
                        source={{ html: data?.content ?? '' }}
                        scalesPageToFit={false}
                        scrollEnabled={false}
                        onNavigationStateChange={(event) => {
                            if (event.url != "about:blank") {
                                webRef.current?.stopLoading()
                                Linking.openURL(event.url)
                            }
                        }}
                    />
                </View>

            </View>
            {data?.button_text && 
                <View>
                    <AKButton 
                        title={data.button_text} 
                        height={48}
                        backgroundColor={backgrounds.green50}
                        textStyle={[fonts.gray800, fonts.size_16, fonts.Medium]}
                        viewStyle={[gutters.marginLeft_16, gutters.marginVertical_24 ,{width: 200}]}
                        onPress={onDetailButtonPress}/>
                </View>
            
            }

        </ScrollView>
    )

}
export default PlaceDetail