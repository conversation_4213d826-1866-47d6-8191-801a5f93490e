import { AKFastImage, ImageVariant } from "@/components/atoms";
import EmpowerHerCell from "@/components/molecules/EmpowerHerCell/EmpowerHerCell";
import EmpowerHerPostCell from "@/components/molecules/EmpowerHerPostCell/EmpowerHerPostCell";
import empowerHerPosts from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { empowerHerPostSchema } from "@/types/schemas/empowerHerPostsListing";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useQuery } from "@tanstack/react-query";
import { FlatList, Text, View } from "react-native";
import FastImage from "react-native-fast-image";
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
import AkinaLogo from '@/theme/assets/images/AkinaLogo.png'
export type PlacesScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'PlaceListing'
>;
function PlaceListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'PlaceListing'>>();
    const navigation = useNavigation<PlacesScreenNavigationProp>()

    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const {data, isLoading } = useQuery({
        queryKey: [`empowerHer${route.params.item.module_id}/${route.params.item.id}`],
        queryFn: () =>  empowerHerPosts(route.params.item.module_id, route.params.item.id)
    });
    function headerContent() {
        return (
            <View style={[{height: 316}]}>
                <Spinner
                    visible={isLoading}
                />
                <View style={[backgrounds.gray100, layout.flex_1, {borderBottomLeftRadius: 40}]}>
                    <AKFastImage
                        uri={route.params.item.thumbnail}
                        style={[layout.flex_1, {borderBottomLeftRadius: 45}]}
                    />
                     <View style={[layout.absolute, backgrounds.black ,{opacity:0.3,left:0, right:0, top:0, bottom:0, borderBottomLeftRadius: 45}]}/>

                    <View style={[layout.absolute,, layout.itemsCenter,layout.row ,layout.bottom0, layout.left0]}>
                        <ImageVariant
                            source={AkinaLogo}
                            style={ [ gutters.marginLeft_32, gutters.marginBottom_16, {tintColor:  colors.yellow, resizeMode: 'contain'  , width: 24, height: 40}]}
                        />
                        <View style={[layout.flex_1, gutters.marginBottom_16,layout.justifyCenter]}>
                            <Text style={[fonts.gray50, gutters.marginLeft_4]}>{route.params.item.description}</Text>

                        </View>

                    </View>

                </View>
                
            </View>
        )
    }
    function onItemPressed(item: z.infer<typeof empowerHerPostSchema> ) {
        navigation.navigate("PlaceDetail", {item: item})
    }
    function renderItem({item}) {
        return (
            <EmpowerHerPostCell item={item} onItemPressed={onItemPressed}/>
        )
    }
    
    return (
        <View>
            
            <FlatList 
                ListHeaderComponent={headerContent}
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{ paddingBottom: 20}} 
                data={data?.posts}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
            />
        

        </View>
    )
}

export default PlaceListing
