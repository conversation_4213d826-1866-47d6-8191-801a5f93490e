import { AKFastImage, ImageVariant } from "@/components/atoms";
import EmpowerHerCell from "@/components/molecules/EmpowerHerCell/EmpowerHerCell";
import empowerHerListing from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useQuery } from "@tanstack/react-query";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
import Spinner from "react-native-loading-spinner-overlay";
import AkinaLogo from "@/theme/assets/images/AkinaLogo.png"
export type EventsScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'EmpowerHerListing'
>;
function EmpowerHerListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'EmpowerHerListing'>>();
    const navigation = useNavigation<EventsScreenNavigationProp>()

    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    const {data, isLoading } = useQuery({
        queryKey: [`empowerHer${route.params.item.id}`],
        queryFn: () =>  empowerHerListing(route.params.item.id)
    });
    function headerContent() {
        return (
            <View style={[{height: 316}]}>
                <Spinner
                    visible={isLoading}
                />
                <View style={[backgrounds.gray100, layout.flex_1, {borderBottomLeftRadius: 40}]}>
                    <AKFastImage
                        uri={route.params.item.thumbnail}
                        style={[layout.flex_1, {borderBottomLeftRadius: 45}]}
                    />
                    <View style={[layout.absolute, layout.bottom0, layout.left0]}>
                        <ImageVariant
                            source={AkinaLogo}
                            style={ [ gutters.marginLeft_32, gutters.marginBottom_16, {tintColor:  colors.yellow, resizeMode: 'contain'  , width: 24, height: 40}]}
                        />

                    </View>

                </View>
                
            </View>
        )
    }
    function onItemPressed(item: any) {
        navigation.navigate('PlaceListing', {item: item})
    }
    function renderItem({item}) {

        return (
            <EmpowerHerCell item={item}  onItemPressed={onItemPressed}/>
        )
    }
    
    return (
        <View>
            
            <FlatList 
                ListHeaderComponent={headerContent}
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{ paddingBottom: 20}} 
                data={data?.types}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
            />
        

        </View>
    )
}

export default EmpowerHerListing
