import { ImageVariant } from "@/components/atoms";
import { use<PERSON>allback, useRef, useState, useMemo, useEffect } from "react";
import { AkinaH<PERSON><PERSON><PERSON>, Akina<PERSON><PERSON><PERSON>eader, EmptyPostView } from "@/components/molecules";
import modules from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { moduleSchema } from "@/types/schemas/module";
import { useQuery, useInfiniteQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { ActivityIndicator, FlatList, Text, TouchableOpacity, View } from "react-native"
import { z } from "zod";
import Add from "@/theme/assets/images/AddIcon.png"
import { useTranslation } from "react-i18next";
import { MODULE_TYPES_OPTIONS } from "../constants";
import { useIsFocused, useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import SocialConnectPost from "@/components/molecules/SocialConnectPost/SocialConnectPost";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import SocialConnectPostCommentsSheet from "@/components/molecules/SocialConnectPostCommentsSheet/SocialConnectPostCommentsSheet";
import SocialConnectPostSettingsBottomSheet from "@/components/molecules/SocialConnectPostSettingsBottomSheet/SocialConnectPostSettingsBottomSheet";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { useLikeBookmarkToggleMutation } from "../SocialConnect/hooks/useLikeBookmarkToggleMutation";
import Spinner from "react-native-loading-spinner-overlay";
import { usePremiumMemberPopup } from "@/hooks/usePremiumMemberPopup";
import { getProfile } from "@/services/users";
import { UserTypes } from "@/utils/constants";
import getPostsFromFollowers from "@/services/socialConnect/getPostsFromFollowers";
import ReportPostBottomSheet from "@/components/molecules/ReportPostBottomSheet/ReportPostBottomSheet";
import getPosts from "@/services/socialConnect/getPosts";
import HeaderLogo from "@/theme/assets/images/HeaderLogo.png"
import MenuIcon from "@/theme/assets/images/MenuIcon.png"
import NotificationIcon from "@/theme/assets/images/NotificationIcon.png"

export type HomeScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'BlogListing',
    'EmpowerHerListing'
>;
function SocialHome() {
    const queryClient = useQueryClient();
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const isFocused = useIsFocused();
    const navigation = useNavigation<HomeScreenNavigationProp>()
    const { t } = useTranslation(['home']);
    const tokenColors = colorTokens();
    const flatListRef = useRef<FlatList>(null);

    const { onLikeButtonTapped, MUTATION_TYPE } = useLikeBookmarkToggleMutation();

    const showPremiumMemberPopup = usePremiumMemberPopup();

    const [visiblePostId, setVisiblePostId] = useState(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isSettingSheetVisible, setIsSettingSheetVisible] = useState(false);
    const [isLoadingNext, setIsLoadingNext] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [selectedPost, setSelectedPost] = useState(null);
    const [selectedCommentItem, setSelectedCommentItem] = useState<z.infer<typeof socialConnectPostSchema> | null>(null)
    const [reportingContent, setReportingContent] = useState(null);

    const { data: modulesList } = useQuery({
        queryKey: ['modules'],
        queryFn: modules,
        refetchOnMount: false
    });
    useEffect(() => {
        if (isFocused == true ) {
            console.log("refetched")
            postListingResponse.refetch()
        }
    },[isFocused])

    const profileResponse = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
    });

    const postListingResponse =  useInfiniteQuery({
        queryKey: ['social-connect-posts'],
        initialPageParam: 1,
        refetchOnWindowFocus: "always",
        queryFn:  ({pageParam}) =>  {
            return getPosts(pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page < lastPage.last_page ?  lastPage.current_page + 1 : undefined,
    })

    function itemPressed(item: z.infer<typeof moduleSchema>) {
        const { navigationPath, shouldNotTakeProps } =
            MODULE_TYPES_OPTIONS[item.type] || {};
        if (navigationPath) {
            navigation.navigate(navigationPath, !shouldNotTakeProps ? { item } : {});
        }
    }
    function addPostButtonTapped() {
        navigation.navigate('AddSocialPost', {})
    }
    
    function ListHeaderComponent() {
        return (
            <AkinaHubHeader
                moduleList={flattenModuleListData}
                itemPressed={itemPressed}
                user={profileResponse.data?.user_profile}
            />
        )
    
    }

    function ListEmptyComponent() {
        return ( flattenData.length === 0) ? (
            <EmptyPostView />
        ) : null;
    }

    function ListFooterComponent() {
        return postListingResponse.isFetchingNextPage ? <ActivityIndicator size="small" style={[gutters.marginVertical_4]} /> : null
    }

    function ItemSeparatorComponent() {
        return (
            <View style={[{ height: 2, backgroundColor: tokenColors.stoke.default.subdued }]} />
        )
    }
    
    const viewabilityConfig = {
        itemVisiblePercentThreshold: 80,
    };
    
    const onViewableItemsChanged = useCallback(({ viewableItems }) => {
        if (viewableItems.length > 0) {
            setVisiblePostId(viewableItems[0].item.id);
        } else {
            setVisiblePostId(null);
        }
    }, []);

    const viewabilityConfigCallbackPairs = useRef([
        { viewabilityConfig, onViewableItemsChanged },
    ]);

    function renderItem({ item }: any) {
        return (
            <SocialConnectPost
                item={item}
                isVisible={visiblePostId === item.id}
                onCommentButtonTapped={onCommentButtonTapped}
                onLikeButton={onLikeButton}
                onBookmarkButton={onBookmarkButton}
                onPressPostSettings={onPressPostSettings}
            />
        )
    }

    function onLikeButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        if (profileResponse.data?.user_profile.user_type == UserTypes.FREE) {
            showPremiumMemberPopup()
        } else {
            onLikeButtonTapped(tappedItem)
        }
    }

    function onBookmarkButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        if (profileResponse.data?.user_profile.user_type == UserTypes.FREE) {
            showPremiumMemberPopup()
        } else {
            onLikeButtonTapped(tappedItem, MUTATION_TYPE.BOOKMARK)
        }
    }

    function onCommentButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema> ) {
        setSelectedCommentItem(tappedItem);
        setIsModalVisible(true);
    }

    function onPressPostSettings(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        setSelectedPost(tappedItem);
        setIsSettingSheetVisible(true);
    }
    
    function removeUsersData(userId?: number) {
        queryClient.setQueriesData(
            {
                predicate: (query) => ['social-connect-posts-following', 'social-connect-posts'].includes(query?.queryKey[0] as string),
            }, 
            (cacheData: any) => {
                if (cacheData !=null) {
                    let pages = cacheData?.pages?.map((page: any) => {
                        let pageData = page.data.filter((post: z.infer<typeof socialConnectPostSchema>) => post?.user?.id != userId )
                        
                        if (pageData != null) {
                            return {
                                ...page,
                                data: pageData,
                            };
                        }
                        
                    });
                    let newPages = pages.filter((page) => page.data?.length !=0)
                    return {
                        ...cacheData,
                        pages: newPages,
                    };
                }
            
        });
    }

    function onPressReport() {
        setTimeout(() => {
            setReportingContent({
                postID: selectedPost?.id,
                userID: selectedPost?.user?.id,
            });
        }, 750)
    }

    function scrollToTop() {
        flatListRef.current?.scrollToOffset({
            offset: 0,
            animated: true,
        });
    };

    const loadNext = useCallback(() => {
        postListingResponse.hasNextPage && postListingResponse.fetchNextPage();
    }, [postListingResponse.fetchNextPage, postListingResponse.hasNextPage]);
    let flattenModuleListData = useMemo(() => {
        return modulesList?.modules?.flatMap(data => data) || [];
    }, [modulesList]);
    let flattenData = useMemo(() => {
        console.log("refetching",postListingResponse.data?.pages)
        return postListingResponse.data?.pages.flatMap(page => page.data) || [];
    }, [postListingResponse.data, postListingResponse.isRefetching]);

    return (
        <AKSafeAreaView edges={['top', 'bottom']} style={[{backgroundColor: tokenColors.background.default.neutrals.default}]}>
            <View style={[layout.row, layout.itemsCenter, gutters.paddingVertical_4, { borderBottomWidth: 1, borderBottomColor: tokenColors.stoke.default.default }]}>
                <TouchableOpacity 
                    onPress={() => navigation.toggleDrawer()}
                    style={{ marginLeft: 10 }}
                >
                    <ImageVariant  source={MenuIcon} style={{height: 32, width: 32, resizeMode: 'contain'}}/>
                </TouchableOpacity>
                <TouchableOpacity onPress={scrollToTop}>
                    <ImageVariant tintColor={tokenColors.content.primary.default}  source={HeaderLogo} style={{height: 24, width: 83, resizeMode: 'contain', marginLeft: 6, marginBottom: 5}}/>
                </TouchableOpacity>
                <View style={[layout.flex_1]}>
                    <TouchableOpacity 
                        onPress={() => navigation.navigate('NotificationsListing')}
                        style={{ marginRight: 10, alignSelf: 'flex-end' }}
                    >
                        <ImageVariant  source={NotificationIcon} style={{height: 32, width: 32, resizeMode: 'contain'}}/>
                    </TouchableOpacity>
                </View>
            </View>
            <FlatList
                ref={flatListRef}
                showsVerticalScrollIndicator={false}
                data={flattenData}
                keyExtractor={(_, index) => index.toString()}
                renderItem={renderItem}
                ListHeaderComponent={ListHeaderComponent()}
                ListEmptyComponent={ListEmptyComponent}
                ListFooterComponent={ListFooterComponent}
                ItemSeparatorComponent={ItemSeparatorComponent}
                onEndReached={loadNext}
                onEndReachedThreshold={0.4}
                viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
            />
            <TouchableOpacity onPress={addPostButtonTapped} style={[layout.absolute, layout.itemsCenter, layout.justifyCenter ,{bottom: 58, right: 16, width: 56, height: 56, backgroundColor: tokenColors.fill.bold.neutrals.rest, borderRadius: 28}]}>
                <View>
                    <ImageVariant source={Add} style={{height: 24, width: 24, resizeMode: 'contain'}}/>
                </View>
            </TouchableOpacity>
            <SocialConnectPostCommentsSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                item={selectedCommentItem}
            />
            <SocialConnectPostSettingsBottomSheet
                isModalVisible={isSettingSheetVisible}
                setIsModalVisible={setIsSettingSheetVisible}
                shouldUpdate={(userId) => removeUsersData(userId)}
                item={selectedPost}
                setActionStatus={setIsDeleting}
                isOwnPost={profileResponse?.data?.user_profile?.id === selectedPost?.user?.id}
                onPressReport={onPressReport}
            />
            <ReportPostBottomSheet
                isModalVisible={!!reportingContent}
                setReportingContent={setReportingContent}
                reportingContent={reportingContent}
            />
        </AKSafeAreaView>
    )
}

export default SocialHome