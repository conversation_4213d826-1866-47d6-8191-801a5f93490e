import { ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { Alert, Platform, Text, TouchableOpacity, View } from "react-native";
import CrossIconNav from "@/theme/assets/images/CrossIconNav.png"
import CameraIcon from "@/theme/assets/images/CameraIcon.png"
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { ApplicationStackParamList } from "@/types/navigation";
import { check, PERMISSIONS, request} from 'react-native-permissions';
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import { openCamera, showBlockedPermissionAlert } from "@/utils/utility";
import { size } from "lodash";
import ImagePicker from "react-native-image-crop-picker";


function CameraRoll() {
    const { t } = useTranslation(['home']);
    const navigation = useNavigation()
    const tokenColors = colorTokens()
    const route = useRoute<RouteProp<ApplicationStackParamList, 'CameraRoll'>>();
    
    const [hasGalleryPermissions, setHasGalleryPermissions ] = useState(false)
    const {
        fonts,
        gutters,
        layout
    } = useTheme();
    
   
    useEffect(() => {
        navigation.setOptions({
            title: t('home:CameraRoll'),
            headerStyle: {
               backgroundColor: tokenColors.background.default.neutrals.default
            } ,
            headerTitleStyle: [
               fonts.SemiBold,
               fonts.fontSizes.body.sm, 
               fonts.lineHeight.body.sm,
               {color: tokenColors.content.default.emphasis}] ,
            headerShown: true, 
            headerLeft: headerCrossButton, 
        })
        checkIfGalleryPermissionGranted()
    },[])
    async function checkIfGalleryPermissionGranted() {
        const permission =  Platform.OS === 'ios' ? PERMISSIONS.IOS.PHOTO_LIBRARY : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
        const status = await check(permission);
        console.log("Status:", status)
        if (status == 'granted') {
            setHasGalleryPermissions(true)
        }
    }

   function headerCrossButton() {
        return (
            <TouchableOpacity 
                onPress={() => navigation.goBack()}
                style={{ marginLeft: 10 }}
            >
                <ImageVariant  source={CrossIconNav} style={{height: 40, width: 40, resizeMode: 'contain'}}/>
            </TouchableOpacity>
        );
    }
    async function onCameraButton() {
        const permission = Platform.OS === 'ios' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA;
        const status = await check(permission);
        if (status == 'granted') {
            openCamera()
        } else {
            const result = await request(permission);
            if (result == 'granted') {
                openCamera()              
            }
            else {
                showBlockedPermissionAlert('Camera');
            }
        }
    }
    async function onAccessButton() {
        if (hasGalleryPermissions == false) {
            const permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
            const result = await request(permission);
            if (result !== 'granted') {
                showBlockedPermissionAlert('Gallery');
            }
            else if (result === 'granted') {
                setHasGalleryPermissions(true)
            }
        } else {
            openLibrary()
            // launchImageLibrary({mediaType: route.params.type, quality: 0.1, }, (result) => {
            //     if (result.assets != null && result.assets.length > 0) {
            //         let multimedia = result.assets[0]
            //         if (multimedia != null) {
            //             if (route.params.type == "photo") {
            //                 let photoLink = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri
            //                 // editSelectedImage(photoLink)
            //                 route.params.onMultimediaPicked(photoLink)
            //                 navigation.goBack()

            //             } else if (route.params.type == "video") {
            //                 let videoLink = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri
            //                 route.params.onMultimediaPicked(videoLink)  
            //                 navigation.goBack()
 
            //             }
            //         }   
            //     }
            // });
        }
    }
    function openCamera() {
        ImagePicker.openCamera({
            mediaType: route.params.type, 
            ...(Platform.OS === 'ios' && {
                width: route.params.type == 'photo' ? 375 : null,
                height: route.params.type == 'photo' ? 375: null,
            }),
            quality: 0.1,
            cropping: route.params.type == 'photo' ? true : false
        }).then((multimedia) => {
            console.log(multimedia)
            route.params.onMultimediaPicked(multimedia.path)  
            navigation.goBack()
        });
    }
    function openLibrary() {
        ImagePicker.openPicker({
            mediaType: route.params.type, 
            ...(Platform.OS === 'ios' && {
                width: route.params.type == 'photo' ? 375 : null,
                height: route.params.type == 'photo' ? 375: null,
            }),
            quality: 0.1,
            cropping: route.params.type == 'photo' ? true : false
        }).then((multimedia) => {
            console.log(multimedia)
            route.params.onMultimediaPicked(multimedia.path)  
            navigation.goBack()
        });

    }
    return (
        <View style={[layout.flex_1, {backgroundColor: tokenColors.background.default.neutrals.default}]}>
            <View style={[layout.justifyCenter, layout.itemsCenter, {height: 138, width: 148, backgroundColor: tokenColors.background.bold.neutral.default}]}>
                <TouchableOpacity onPress={onCameraButton}>
                    <View style={[layout.itemsCenter, layout.justifyCenter]}>
                        <ImageVariant  source={CameraIcon} style={{height: 24, width: 24, resizeMode: 'contain'}}/>
                        <Text style={[fonts.fontSizes.body.sm,, fonts.SemiBold, fonts.lineHeight.body.sm, {color: tokenColors.content.onBold.default.default, letterSpacing: 0.25}]}>{t("home:Camera")}</Text>
                    </View>
                </TouchableOpacity>
            </View>
            <View style={[layout.itemsCenter, gutters.marginHorizontal_32, {marginTop: 120}]}>
                <Text style={[fonts.fontSizes.headings.H5, fonts.Bold, fonts.lineHeight.headings.H5, {color: tokenColors.content.default.emphasis, letterSpacing: 0.25}]}>{hasGalleryPermissions ? t("home:AccessGranted") : t("home:AccessNeeded")}</Text>
                <Text style={[fonts.fontSizes.body.sm,, fonts.body, fonts.lineHeight.body.sm, {color: tokenColors.content.default.subdued, letterSpacing: 0.25, textAlign: 'center'}]}>{hasGalleryPermissions ? t("home:AccessGrantedDesc") : t("home:AccessNeededDesc")}</Text>
                <TouchableOpacity onPress={onAccessButton} style={[gutters.marginTop_24]}>
                    <View style={[gutters.marginBottom_8,gutters.marginRight_12 ,layout.itemsCenter, layout.justifyCenter,{ height: 36, borderColor:  tokenColors.stoke.default.default, borderRadius: 8, borderWidth: 1}]}>
                        <Text style={[ gutters.marginHorizontal_16,fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: tokenColors.content.default.default, letterSpacing: 0.25}]}>{hasGalleryPermissions ? t("home:Gallery") :t("home:EnableAccess")}</Text>
                    </View>
                </TouchableOpacity>
            </View>
        </View>
    )
}
export default CameraRoll