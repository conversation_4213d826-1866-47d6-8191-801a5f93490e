import { AKFastImage, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import { RouteProp, useIsFocused, useNavigation, useRoute } from "@react-navigation/native"
import { Alert, Animated, Keyboard, Platform, Text, TextInput, TouchableOpacity, View } from "react-native"
import CrossIconNav from "@/theme/assets/images/CrossIconNav.png"
import ImageIcon from "@/theme/assets/images/ImageIcon.png"
import VideoIcon from "@/theme/assets/images/VideoIcon.png"
import { useEffect, useRef, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getProfile } from "@/services/users";
import FastImage from "react-native-fast-image";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import { MediaType } from "react-native-image-picker";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Video, { VideoRef } from "react-native-video";
import addPost, { cancelUpload } from "@/services/socialConnect/addPost";
import editPost, { cancelUpload as cancelUploadEdit } from "@/services/socialConnect/editPost";
import { useTranslation } from "react-i18next";
import { useQueryCacheUpdate } from "../../SocialConnect/hooks/useQueryCacheUpdate";
import { z } from "zod";
import { getVideoMetaData,  Video as VideoCompressor } from "react-native-compressor";
import { socialConnectUserPost } from "@/types/schemas/socialConnectUserPost";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useCustomToast } from "@/hooks/useCustomToast";
import AnimatedProgressBar from "@/components/molecules/AnimatedProgressBar/AnimatedProgressBar";
import AKScreenBlocker from "@/components/atoms/AKScreenBlocker/AKScreenBlocker";
import { createThumbnail } from "react-native-create-thumbnail";

export type CameraRollNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'CameraRoll'
>;
type SuccessResponse = z.infer<typeof socialConnectUserPost>


function AddSocialPost() {    
    const { t } = useTranslation(['home']);
    const route = useRoute<RouteProp<ApplicationStackParamList, 'AddSocialPost'>>();
    
    const navigation = useNavigation<CameraRollNavigationProp>()
    const tokenColors = colorTokens()
    const {
        fonts,
        gutters,
        layout,
        borders,
	} = useTheme();
    const [shouldAddPost, setShouldAddPost] = useState(
        !!route?.params?.isUpdating ? false : !!route?.params?.photoLink || !!route?.params?.videoLink
    )
    const queryClient = useQueryClient()
    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const [isKeyboardVisible, setKeyboardVisible] = useState(false);
    const insets = useSafeAreaInsets();
    const profileResponse = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    
    const [text, setText] = useState(route?.params?.postText || '');
    const [photoLink, setPhotoLink] = useState(route?.params?.photoLink);
    const [videoLink, setVideoLink] = useState(route?.params?.videoLink);
    const animatedHeight = useState(new Animated.Value(0))[0];
    const [textViewHeight, setTextViewHeight] = useState(30)
    const isFocused = useIsFocused();
    const videoRef = useRef<VideoRef>(null)
    const { updateQueryCache, updateProfileCache } = useQueryCacheUpdate(route?.params?.postId);
    const isPostUpdating = !!route?.params?.isUpdating;
    const postType = route?.params?.postType;
    const hasPostText = useRef(!!route?.params?.postText);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const inputRef = useRef<TextInput>(null)
    const onPostButtonTappedRef = useRef<() => void>(() => {});
    const showToast = useCustomToast();

    useEffect(() => {
        if (isFocused == true) {
            const keyboardWillShowListener = Keyboard.addListener(
                Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
                (e) => {
                    setKeyboardVisible(true);
                    setKeyboardHeight(e.endCoordinates.height);
                    Animated.timing(animatedHeight, {
                    toValue: e.endCoordinates.height - insets.bottom,
                    duration: 250,
                    useNativeDriver: false,
                    }).start();
                }
            );
          
            const keyboardWillHideListener = Keyboard.addListener(
            Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
            () => {
                setKeyboardVisible(false);
                Animated.timing(animatedHeight, {
                toValue: 0,
                duration: 250,
                useNativeDriver: false,
                }).start();
            }
            );
            openKeyboardAfterDelay()

              // Clean up listeners
            return () => {
            keyboardWillShowListener.remove();
            keyboardWillHideListener.remove();
            };
        } else {
            if (isFocused == false) {
                videoRef.current?.pause()
            }
        }
        
    }, [isFocused]);
    function openKeyboardAfterDelay() {
        setTimeout(() => {
            inputRef.current?.focus();
        }, 500);
    }
    useEffect(() => {
        navigation.setOptions({
            title: isPostUpdating ? t("home:EditPost") : t("home:AddPost"),
            headerStyle: {
               backgroundColor: tokenColors.background.default.neutrals.default,
               height: insets.top + 48
            },
            headerTitleAlign: 'center',
            headerTitleStyle: [
               fonts.SemiBold,
               fonts.fontSizes.body.sm, 
               {color: tokenColors.content.default.emphasis}
            ],
            headerShown: true, 
            headerLeft: headerCrossButton, 
            headerRight: headerRightButton,
            headerBackground: headerBackground,
       })
    },[])

    useEffect(() => {
        navigation.setOptions({
            headerRight: headerRightButton
        })
    }, [shouldAddPost, isSuccess, isLoading, uploadProgress])

    useEffect(() => {
        navigation.setOptions({
            headerBackground: headerBackground
        })
    }, [uploadProgress])

    function refecthQueries() {
        queryClient.refetchQueries({ 
            queryKey: [`social-connect-user-posts-${profileResponse.data?.user_profile.id}`] 
        })
    }

    function onSuccessMutation(response: SuccessResponse): void {
        if (response.success == true) {
            setIsSuccess(true);
            setTimeout(() => {
                const { media_link: mediaLink, type } = response?.post_data || {};
                if (isPostUpdating) {
                    updateQueryCache(text, profileResponse?.data?.user_profile?.id, type, mediaLink);
                }
                setIsLoading(false)
                setUploadProgress(0);
                refecthQueries()
                updateProfileCache();
                setTimeout(() => {
                    showToast('Post Uploaded Successfully', true);
                }, 300)
                navigation.goBack()
            }, 1000);
        } else {
            showToast(response.message, false)
            setIsLoading(false)
            setUploadProgress(0);
        }
    }

    function onErrorMutation(error: any): void {
        setIsLoading(false)
        setUploadProgress(0);
        Alert.alert(t('home:Error'), error.message)
    }

    const addPostMutation = useMutation(
        {
            mutationFn: (data: any) => {
                return addPost(data, (progress: number) => {
                    setUploadProgress(progress);
                });
            },
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );

    const editPostMutation = useMutation(
        {
            mutationFn: (data: any) => {
                return editPost(data, route?.params?.postId, (progress: number) => {
                    setUploadProgress(progress);
                });
            },
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );

    const cancelUploadMutation = useMutation(
        {
            mutationFn: () => isPostUpdating ? cancelUploadEdit() : cancelUpload(),
            onSuccess: (response) => {
                if (response) {
                    setIsLoading(false)
                    setUploadProgress(0);
                }
            },
            onError: onErrorMutation,
        },
    );

    async function createFormData(multimedia?: string | null, body: Record<string, string> = {}) {
        const data = new FormData();
        if (!!photoLink && !photoLink.startsWith('https://')) {
            data.append('image_file', {
                name: "Image.png",
                type: "image/png",
                uri: multimedia,
              });
        }
        if (!!videoLink && !videoLink.startsWith('https://')) {
            data.append('video_file', {
                name: "video.mov",
                type: "video/mp4",
                uri: multimedia,
            });
            try {
                const thumbnail = await createThumbnail({
                    url: multimedia ?? '',
                    timeStamp: 100, // Get thumbnail from the very start (0 seconds)
                    format: 'png', // or 'png'
                });
                data.append('video_thumbnail', {
                    name: "thumb.png",
                    type: "image/png",
                    uri: thumbnail.path,
                });
                console.log('Thumbnail created:', thumbnail);
            } catch (error) {
                console.error('Error creating thumbnail:', error);
                Alert.alert('Error', 'Failed to create thumbnail from video');
            } finally {

            }
        }
        
        
      
        Object.keys(body).forEach((key) => {
          data.append(key, body[key]);
        });
        return data;
    };
    function headerCrossButton() {
		return (
			<TouchableOpacity 
				onPress={() => navigation.goBack()}
				style={{ marginLeft: 10 }}
			>
				<ImageVariant  source={CrossIconNav} style={{height: 40, width: 40, resizeMode: 'contain'}}/>
			</TouchableOpacity>
		);
	}

    useEffect(() => {
        onPostButtonTappedRef.current = onPostButtonTapped;
    }, [text, photoLink, videoLink])

    async function onPressCancel() {
        cancelUploadMutation.mutate();
    }

    async function onPostButtonTapped() {
        let multimediaLink = photoLink ? photoLink : videoLink;
        setIsLoading(true);
        Keyboard.dismiss();
        if (!!videoLink && !videoLink.startsWith('https://')) {
            const metaData = await getVideoMetaData(videoLink);
            console.log("MetaData before:", metaData)
            const result = await VideoCompressor.compress(
                videoLink,
                {
                    compressionMethod: 'auto',
                },
                (progress) => {
                    console.log('Compression Progress: ', progress);
                }
            );
            const metaDataAfter = await getVideoMetaData(result);
            console.log("MetaData After:", metaDataAfter)
            multimediaLink =  Platform.OS === 'ios' ? result?.replace('file://', '') : result
        }
        let type = 'text';
        if (photoLink || videoLink) {
            type = photoLink ? 'post' : 'story'
        } 
        let data: any = {
            type,
        };
        if (hasPostText.current || text) {
            data.text = text;
        }
        let mutateFunction = addPostMutation.mutate;
        if (isPostUpdating) {
            data["_method"] = 'PUT';
            mutateFunction = editPostMutation.mutate;
        }
        // multimediaLink = photoLink ? photoLink : videoLink
        let formData = await createFormData(multimediaLink, data)
        mutateFunction(formData);
    }

    function headerBackground() {
        return(
            <View style={[
                gutters.marginHorizontal_0, 
                {
                    width: '100%', height: '100%',
                    backgroundColor: tokenColors.background.default.neutrals.default, 
                    borderBottomWidth: uploadProgress > 0 ? 0 : 1, borderBottomColor: tokenColors.stoke.default.default
                }
            ]} />
        )       
    }

    function headerRightButton() {
        return uploadProgress > 0 && uploadProgress <=1 ? (
            <TouchableOpacity style={[gutters.paddingVertical_8, gutters.paddingHorizontal_16]} disabled={isSuccess} onPress={onPressCancel}>
                <Text style={[fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: isSuccess ? tokenColors.content.default.disabled : tokenColors.content.default.default}]}>Cancel</Text>
            </TouchableOpacity>
		) : (
            <TouchableOpacity disabled={!shouldAddPost || isLoading} onPress={() => onPostButtonTappedRef.current()}>
                <View style={[gutters.marginRight_12, gutters.paddingVertical_8, gutters.paddingHorizontal_8, layout.itemsCenter, layout.justifyCenter,{ backgroundColor:  tokenColors.fill.bold.neutrals.rest,  opacity: shouldAddPost || isLoading ? 1 : 0.45 , borderRadius: 8, borderCurve: 'continuous'}]}>
                    <Text style={[ gutters.marginHorizontal_16,fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: tokenColors.content.onBold.default.default, letterSpacing: 0.25}]}>{isPostUpdating ? 'Save' : 'Post'}</Text>
                </View>
            </TouchableOpacity>
        );
    }

    function onMultimediaSelection(type: MediaType, path?: string,) {
        console.log('Path:::',path)
        if (type == 'photo') {
            setPhotoLink(path)
        } else if (type == 'video') {
            setVideoLink(path)
        }
        setShouldAddPost(true)
    }
    
    function imageIconPressed() {
        navigation.navigate("CameraRoll",  {type: 'photo', onMultimediaPicked: (path) => onMultimediaSelection('photo', path)})
    }
    function videoIconPressed() {
        navigation.navigate("CameraRoll",  {type: 'video', onMultimediaPicked: (path) => onMultimediaSelection('video', path)})
    }
    function crossIconTapped() {
        let options = [
            {
                text: 'Keep Editing',
            },
            {
                text: 'Discard',
                onPress: () => {
                    if (photoLink) {
                        setPhotoLink('')
                    }
                    if (videoLink) {
                        setVideoLink('')
                    }
                    if (text.length <= 10) {
                        setShouldAddPost(false)
                    }
                },
                style: 'destructive',
            },
        ]
        Alert.alert('Discard Changes?', 'If you leave now, any unsaved changes you made will be lost.', options)
        
    }
    function onChangeText(text: string) {
        setText(text)
        console.log("Length:;", text.length)
        if (text.length >=10) {
            setShouldAddPost(true)
        } else {
            if (photoLink || videoLink) {
                setShouldAddPost(true)
            } else {
                setShouldAddPost(false)
            }
        }
    }
    
    return (
        <AKSafeAreaView>

            <KeyboardAwareScrollView contentContainerStyle={[layout.flex_1, {backgroundColor: tokenColors.background.default.neutrals.default}]}>
                <View style={[layout.flex_1]}>
                    <AnimatedProgressBar uploadProgress={uploadProgress} isSuccess={isSuccess} isEditing={isPostUpdating} />
                    <View style={[layout.row, layout.itemsCenter, gutters.marginTop_16, gutters.marginLeft_16 ]}>
                        <AKFastImage
                            uri={profileResponse.data?.user_profile.profile_photo}
                            style={[{height: 32, width: 32, borderRadius: 16 ,backgroundColor: 'transparent',} ]}
                            resizeMode={FastImage.resizeMode.cover}
                        />
                        <Text style={[gutters.marginLeft_8,fonts.fontSizes.body.sm,, fonts.Bold, fonts.lineHeight.body.sm, {color: tokenColors.content.default.emphasis, letterSpacing: 0.25}]}>
                            {`${profileResponse.data?.user_profile.first_name} ${profileResponse.data?.user_profile.last_name}`}
                        </Text>
                    </View>
                    <View>
                        <TextInput
                            ref={inputRef}
                            value={text}
                            multiline={true}
                            placeholder={t("home:AddPostTextPlaceholder")}
                            placeholderTextColor={tokenColors.content.default.subdued}
                            style={[gutters.marginTop_4,gutters.marginLeft_16,{minHeight: 30, maxHeight: 150, height: Math.min(150,textViewHeight), letterSpacing: 0.25}, fonts.body, fonts.fontSizes.body.sm, fonts.lineHeight.body.sm]}
                            onChangeText={onChangeText}
                            onContentSizeChange={(event) => {
                                setTextViewHeight(event.nativeEvent.contentSize.height)
                            }}
                    
                        />
                    </View>
                    <View style={[gutters.marginTop_24,]}>
                        { (photoLink || videoLink) && 
                        <TouchableOpacity onPress={crossIconTapped} style={[layout.z10, layout.absolute, borders.rounded_4, {right: 12, top: 12, backgroundColor: tokenColors.fill.default.white.rest}]}>
                            <ImageVariant  source={CrossIconNav} style={{height: 24, width: 24, resizeMode: 'contain'}}/>
                        </TouchableOpacity>
                        }
                        {photoLink && 
                            <FastImage
                                defaultSource={require('@/theme/assets/images/ProfilePlaceholder.png')}
                                source={photoLink != null ? {uri: photoLink} : require('@/theme/assets/images/ProfilePlaceholder.png')}
                                resizeMode={FastImage.resizeMode.cover}
                                style={[gutters.marginHorizontal_0, layout.z1 ,{height: 375}]}
                            />
                        }
                    
                        {videoLink && 
                            
                            // <View>
                                <Video
                                    ref={videoRef}
                                    source={{uri: videoLink}}
                                    resizeMode="contain"
                                    controls={false}
                                    paused={false}
                                    style={[ gutters.marginHorizontal_0,{ height: 300 ,overflow: 'hidden'}]}
                                    hideShutterView={false}
                                    allowsExternalPlayback={false}
                                    repeat={true}
                                    volume={0}
                                    playInBackground={false}
                                    disableFocus={false}
                                    onPlaybackStateChanged={(e) => {
                                    }}  
                                />
                            // </View>
                            }
                        
                    </View>
                    
                    <Animated.View style={[layout.absolute,layout.itemsCenter,layout.z10,layout.row,gutters.marginHorizontal_0 ,{height: 56, borderTopColor: tokenColors.stoke.default.default, borderTopWidth: 1, bottom: animatedHeight, right:0, left:0, backgroundColor: tokenColors.background.default.neutrals.default}]}>
                        <TouchableOpacity onPress={imageIconPressed} style={[gutters.marginLeft_16]}>
                            <ImageVariant  source={ImageIcon} style={{height: 24, width: 24, resizeMode: 'contain'}}/>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={videoIconPressed} style={[gutters.marginLeft_24]}>
                            <ImageVariant source={VideoIcon} style={{height: 24, width: 24, resizeMode: 'contain'}}/>
                        </TouchableOpacity>
                    </Animated.View>
                </View>
            </KeyboardAwareScrollView>
            <AKScreenBlocker isVisible={isLoading} />
        </AKSafeAreaView>
    )
}
export default AddSocialPost