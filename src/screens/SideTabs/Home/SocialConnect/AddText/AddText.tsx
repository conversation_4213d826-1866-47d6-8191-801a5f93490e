import { AKButton } from "@/components/atoms";
import addPost from "@/services/socialConnect/addPost";
import editPost from "@/services/socialConnect/editPost";
import { useTheme } from "@/theme";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, KeyboardAvoidingView, TextInput, View } from "react-native"
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Spinner from "react-native-loading-spinner-overlay";
import { useQueryCacheUpdate } from "../hooks/useQueryCacheUpdate";
import { ApplicationStackParamList } from "@/types/navigation";
import { z } from "zod";
import { basicSchema } from "@/types/schemas/basic";

type SuccessResponse = z.infer<typeof basicSchema>

function AddText() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'AddText'>>();
    const { t } = useTranslation(['home']);
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const [text, setText] = useState(route?.params?.postText || '')
    const navigation = useNavigation()
    const [isLoading, setIsLoading] = useState(false)

    const { updateQueryCache } = useQueryCacheUpdate(route?.params?.postId);

    function onSuccessMutation(response: SuccessResponse): void {
        setIsLoading(false)
        if (response.success == true) {
            let options = [{text: "Ok", onPress: async () => {
                if (route.params.onGoBack !=null) {
                    route.params.onGoBack(true)
                }
                navigation.goBack()
            }}]
            Alert.alert("Success!", `Post ${isPostUpdating ? 'Edited' : 'Added'} Successfully.`, options)
            if (isPostUpdating) {
                updateQueryCache(text);
            }
        } else {
            Alert.alert(t('home:Error'), response.message)
        }
    }

    function onErrorMutation(error: any): void {
        setIsLoading(false)
        Alert.alert(t('home:Error'), error.message)
    }

    const editPostMutation = useMutation(
        {
            mutationFn: (data: any) => editPost(data, route?.params?.postId),
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );

    const addPostMutation = useMutation(
        {
            mutationFn: (data: any) => addPost(data),
            onSuccess: onSuccessMutation,
            onError: onErrorMutation
        },
    );
    function submitButtonPressed(): void {
        if (text == '') {
            Alert.alert(t('home:Warning'), t('home:EnterTextError'))
        } else {
            let data = {
                text: text,
                type: 'text'
            }
            let mutateFunction = addPostMutation.mutate;
            if (isPostUpdating) {
                data["_method"] = 'PUT';
                mutateFunction = editPostMutation.mutate;
            }
            console.log(isPostUpdating, ' value')
            let formData = createFormData(data)
            setIsLoading(true)
            mutateFunction(formData);
        }
    }

    function createFormData(body = {}): FormData {
        const data = new FormData();
        Object.keys(body).forEach((key) => {
          data.append(key, body[key]);
        });
        return data;
    };

    const isPostUpdating = !!route?.params?.isUpdating;

    return (
        <KeyboardAwareScrollView style={[layout.flex_1]} contentContainerStyle={[layout.flex_1]}>
            <Spinner
                visible={isLoading}
            />
            <View style={[layout.flex_1 ,layout.justifyBetween, gutters.marginTop_32, gutters.marginHorizontal_16]}>
                <TextInput
                    value={text}
                    multiline={true}
                    placeholder={t('home:WriteText')}
                    placeholderTextColor={colors.gray200}
                    style={[{minHeight: 300}]}
                    onChangeText={(text) => setText(text)}
                />
                <View style={[gutters.marginBottom_80]}>
                    <AKButton 
                        height={48}
                        backgroundColor={backgrounds.green50}
                        title={isPostUpdating ? t("home:UpdatePost") : t("home:AddPost")}
                        textStyle={fonts.gray50}
                        onPress={submitButtonPressed}
                        viewStyle={gutters.marginHorizontal_32}
                    />
                </View>
                

            </View>
            
            
        </KeyboardAwareScrollView>
    )

}
export default AddText