import { ImageVariant } from "@/components/atoms"
import { ActivityIndicator, Alert, FlatList, Platform, RefreshControl, Text, TouchableOpacity, View } from "react-native"
import AddIcon from '@/theme/assets/images/Home/AddIcon.png'
import { useTheme } from "@/theme";
import Animated, {useSharedValue, withTiming} from "react-native-reanimated";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useIsFocused, useNavigation, useRoute } from "@react-navigation/native";
import { useHeaderHeight } from "@react-navigation/elements";
import { ApplicationStackParamList } from "@/types/navigation";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useActionSheet } from "@expo/react-native-action-sheet";
import { launchCamera, launchImageLibrary, MediaType } from "react-native-image-picker";
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import getPosts from "@/services/socialConnect/getPosts";
import { z } from "zod";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { SocialConnectCommentBottomSheet, SocialConnectContentCell } from "@/components/molecules";
import getPostsFromFollowers from "@/services/socialConnect/getPostsFromFollowers";
import { useTranslation } from "react-i18next";
import { onShareButtonTapped } from "./utility";
import { useLikeBookmarkToggleMutation } from "./hooks/useLikeBookmarkToggleMutation";
import { getProfile } from "@/services/users";
import { UserTypes } from "@/utils/constants";
import tempAuth from "@/services/users/tempAuth";
import Spinner from "react-native-loading-spinner-overlay";

export type AddTextNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'AddText',
    'AddMultimedia'
>;
var isFollowing = false

function SocialConnect() {
    const queryClient = useQueryClient()
    const profileResponse = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const flatListRef = useRef<FlatList>(null)
    const { showActionSheetWithOptions } = useActionSheet();
    const { t } = useTranslation(['home', 'sideTabs']);
    const [isPreloading, setIsPreloading] = useState(false)
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const Icon = _Icon as React.ElementType
    const [isFollowingSelected, setIsFollowingSelected] = useState(false)
    const navigation = useNavigation<AddTextNavigationProp>()
    const [isAddOptionsVisible, setIsAddOptionsVisible] = useState(false)
    const addOptionsWidth = useSharedValue(0)
    const [isRefreshing, setIsRefreshing] = useState(false)
    const [isModalVisible, setIsModalVisible] = useState(false)
    const [selectedCommentItem, setSelectedCommentItem] = useState<z.infer<typeof socialConnectPostSchema> | null>(null)
    const storyCellRef = useRef()
    const isFocused = useIsFocused();
    const [currentVisibleIndex, setCurrentVisibleIndex] = useState(0)
    const [isLoadingContent, setIsLoadingContent] = useState(false)
    const { isLoading, onLikeButtonTapped } = useLikeBookmarkToggleMutation();

    useEffect(() => {
        if (isFocused == false) {
            storyCellRef.current?.pauseVideo()
        }
        if (isFocused == true && isFollowingSelected) {
            followersPostListingResponse.refetch()
        }
    },[isFocused])

    const followersPostListingResponse = useInfiniteQuery({
        queryKey: [`social-connect-posts-following`],
        initialPageParam: 1,
        
        queryFn:  ({pageParam}) =>  {
            return getPostsFromFollowers(pageParam)
        },
        getNextPageParam: (lastPage, pages) => {
            return lastPage.current_page < lastPage.last_page ?  lastPage.current_page + 1 : undefined

        }
    })
    const postListingResponse =  useInfiniteQuery({
        queryKey: [`social-connect-posts`],
        initialPageParam: 1,
        
        queryFn:  ({pageParam}) =>  {
            return getPosts(pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page < lastPage.last_page ?  lastPage.current_page + 1 : undefined,
    })
    
    useEffect(() => {
        if (isFollowingSelected) {
            followersPostListingResponse.refetch()
        } else {
            postListingResponse.refetch()
        }
        flatListRef.current?.scrollToOffset({ animated: true, offset: 0 });

    },[isFollowingSelected])
    let flattenData = useMemo(() => {
        if (isFollowingSelected) {
            return followersPostListingResponse.data?.pages.flatMap(page => page.data) || [];
        } else {
            return postListingResponse.data?.pages.flatMap(page => page.data) || [];
        }
    }, [postListingResponse.data, postListingResponse.isRefetching, followersPostListingResponse.data, followersPostListingResponse.isRefetching, isFollowingSelected]);
    const loadNext = useCallback(() => {
        if (isFollowing) {
            followersPostListingResponse.hasNextPage && followersPostListingResponse.fetchNextPage();
        } else {            
            postListingResponse.hasNextPage && postListingResponse.fetchNextPage();
        }
    }, [postListingResponse.fetchNextPage, postListingResponse.hasNextPage, followersPostListingResponse.fetchNextPage, followersPostListingResponse.hasNextPage]);
    
    const onRefresh = useCallback(() => {
        if (!isRefreshing) {
            setIsRefreshing(true);
            if (isFollowingSelected) {
                followersPostListingResponse.refetch()
                .then(() => onDataFetched())
                .catch(() => onDataFetched());
            } else {
                postListingResponse.refetch()
                .then(() => onDataFetched())
                .catch(() => onDataFetched());
            }
        }
    }, [isRefreshing, postListingResponse.refetch, followersPostListingResponse.refetch]);

    function onDataFetched() {
        setIsRefreshing(false)
        flatListRef.current?.scrollToOffset({ animated: true, offset: 0 });

    }
    function addButtonTapped() {
        if (profileResponse.data?.user_profile.user_type == UserTypes.FREE) {
            showPremiumMemberPopup()
        } else {
            if (isAddOptionsVisible == false) {
                addOptionsWidth.value = withTiming(250)
            } else {
                addOptionsWidth.value = withTiming(0)
            }
            setIsAddOptionsVisible(!isAddOptionsVisible)
        }
        
    }
    function addTextButtonTapped() {
        navigation.navigate('AddText', {onGoBack: (shouldRefresh) => {
            if (shouldRefresh == true) {
                onRefresh()
            }
        }})
    }
    function addStoryButtonTapped() {
        let options = [t("home:ChooseVideo"), t("home:Camera"), t("home:Cancel")];
        showActionSheetWithOptions({
            options: options,
            cancelButtonIndex: 2,
            destructiveButtonIndex: 2,
            showSeparators: true,
            textStyle: fonts.green50,
            }, (i?: number) => {
                onPickOptionPress(i ?? 0, "video")
        }); 
    }
    function addPostButtonTapped() {

        let options = [t("home:ChoosePhoto"), t("home:Camera"), t("home:Cancel")];
        showActionSheetWithOptions({
            options: options,
            cancelButtonIndex: 2,
            destructiveButtonIndex: 2,
            showSeparators: true,
            textStyle: fonts.green50,
            }, (i?: number) => {
                onPickOptionPress(i ?? 0, "photo")
        }); 
    }
    async function onPickOptionPress (index: number, type: MediaType) {
        if (index == 0) {
            setIsPreloading(true)
            launchImageLibrary({mediaType: type, quality: 0.1}, (result) => {
                if (result.assets != null && result.assets.length > 0) {
                    let multimedia = result.assets[0]
                    if (multimedia != null) {
                        if (type == "photo") {
                            let photoLink = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri
                            addOptionsWidth.value = withTiming(0)
                            setIsPreloading(false)

                            navigation.navigate('AddMultimedia', {photoLink: photoLink, onGoBack: (shouldRefresh) => {
                                if (shouldRefresh == true) {
                                    onRefresh()
                                }
                            }})
                        } else if (type == "video") {
                            let videoLink = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri
                            addOptionsWidth.value = withTiming(0)
                            setIsPreloading(false)

                            navigation.navigate('AddMultimedia', {videoLink: videoLink, onGoBack: (shouldRefresh) => {
                                if (shouldRefresh == true) {
                                    onRefresh()
                                }
                            }})
                        }
                    }   
                }
            });
            
        } else if (index == 1) {
            const result = await launchCamera({mediaType: type, quality: 0.1});
            if (result.assets != null && result.assets.length > 0) {
                let multimedia = result.assets[0]
                if (type == "photo") {
                    let photoLink = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri
                    addOptionsWidth.value = withTiming(0)
                    navigation.navigate('AddMultimedia', {photoLink: photoLink})
                } else if (type == "video") {
                    let videoLink = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri
                    addOptionsWidth.value = withTiming(0)
                    navigation.navigate('AddMultimedia', {videoLink: videoLink})
                }
            }            
        }
    }
    const viewabilityConfig = {
        waitForInteraction: true,
        // At least one of the viewAreaCoveragePercentThreshold or itemVisiblePercentThreshold is required.
        viewAreaCoveragePercentThreshold: 100,
      }
    function onViewableItemsChanged({viewableItems, changed}: any) {
        if (viewableItems?.length > 0) {
            if (viewableItems[0]?.isViewable == true) {
                let index = viewableItems[0].index
                setCurrentVisibleIndex(index)
            }
        }
    };
    const tempAuthMutation = useMutation({
        mutationFn: () => tempAuth(),
        onSuccess: (response) => {
            setIsLoadingContent(false)
            navigation.navigate('AKWebView', {source: `${response.web_url}?paymentKey=${response.key}`})
        },
        onError: (error) => {
            setIsLoadingContent(false)
            Alert.alert("Error!", error.message)
        }
    })
    function showPremiumMemberPopup() {
        let options = [{text: t("home:Upgrade"), onPress: async () => {
            setIsLoadingContent(true)
            tempAuthMutation.mutate()
        }}, {text: t("home:Cancel"), onPress: async () => {
            
        }}]
        Alert.alert(t("home:PremiumUser"), t("home:PremiumUserDesc"), options)
    }
    
    function renderItem({item, index}: any) {
        function onCommentButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema> ) {
            if (profileResponse.data?.user_profile.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                setSelectedCommentItem(tappedItem)
                setIsModalVisible(true)
            } 
        }
        function onShareButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
            if (profileResponse.data?.user_profile.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                onShareButtonTapped(tappedItem)
            }
        }
        function onLikeButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
            if (profileResponse.data?.user_profile.user_type == UserTypes.FREE) {
                showPremiumMemberPopup()
            } else {
                onLikeButtonTapped(tappedItem)
            }
        }
        function onProfileButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema> ) {
            if (tappedItem.user != null) {
                navigation.navigate('Profile', {userId: tappedItem.user.id})
            }
        }
        let currentItem = item as z.infer<typeof socialConnectPostSchema>

        return (
            <SocialConnectContentCell
                cellRef={storyCellRef}
                isFocused={currentVisibleIndex == index}
                onLikeButtonTapped={onLikeButton} 
                onCommentButtonTapped={onCommentButtonTapped} 
                onShareButtonTapped={onShareButton}
                item={currentItem}
                isLoading={isLoading}
                onProfileTapped={onProfileButtonTapped}
            />
        )
    }
    return (
        <View style={[layout.flex_1, layout.itemsCenter]}>
            <Spinner
                visible={isLoadingContent}
            />
            {flattenData?.length == 0 && 
                <View style={[layout.flex_1,layout.justifyCenter, layout.itemsCenter]}>
                    <Text>{t('sideTabs:NoPostAvailable')}</Text>
                </View>
            }
            <FlatList
                ref={flatListRef}
                initialNumToRender={1}
                windowSize={1}
                data={flattenData}
                renderItem={renderItem}
                horizontal={false}
                pagingEnabled={true}
                showsVerticalScrollIndicator={false}
                refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                onEndReached={loadNext}
                viewabilityConfig={viewabilityConfig}
                onViewableItemsChanged={onViewableItemsChanged}
                removeClippedSubviews={true}
                ListFooterComponent={
                    <View style={[layout.row,layout.justifyCenter, layout.itemsCenter ,{height: (postListingResponse.isFetchingNextPage || followersPostListingResponse.isFetchingNextPage) ? 100 : 0}]}>
                    {(postListingResponse.isFetchingNextPage || followersPostListingResponse.isFetchingNextPage) && <ActivityIndicator />}
                    </View>
                }
            />
            <TouchableOpacity onPress={addButtonTapped} style={ [ {height: 28 ,width: 43, position: 'absolute', bottom:50,}]}>

                <ImageVariant
                    source={AddIcon}
                    style={ [{height: 28 ,width: 43}]}
                />
            </TouchableOpacity>

            <Animated.View style={[ layout.absolute, {height: 132, borderTopLeftRadius: 25 ,width: addOptionsWidth, right: 0, bottom: 150, overflow: 'hidden'}]}>
                <Animated.View style={[layout.flex_1,backgrounds.green50, {top: -69,borderBottomLeftRadius: 45, width: addOptionsWidth, height: 132}]}>
                    <View style={[layout.absolute, layout.row, {bottom: 0, height: 63, right:0}]}>
                        <TouchableOpacity onPress={addPostButtonTapped}>
                            <View style={[layout.justifyCenter, layout.itemsCenter,{height: 63, width: 70}]}>
                                <Text style={[fonts.gray100, fonts.bold]}>{t('home:Post')}</Text>
                            </View>
                        </TouchableOpacity>
                        
                        <TouchableOpacity onPress={addStoryButtonTapped}>
                            <View style={[layout.justifyCenter, layout.itemsCenter,{height: 63, width: 70}]}>
                                <Text style={[fonts.gray100, fonts.bold]}>{t('home:Story')}</Text>
                            </View>
                        </TouchableOpacity>
                        
                        <TouchableOpacity onPress={addTextButtonTapped}>
                            <View style={[layout.justifyCenter, layout.itemsCenter,{height: 63, width: 70}]}>
                                <Text style={[fonts.gray100, fonts.bold]}>{t('home:Text')}</Text>
                            </View>
                        </TouchableOpacity>
                        
                    </View>
                </Animated.View>

            </Animated.View>
            <View style={[layout.absolute, layout.row, layout.itemsCenter ,{height: 50}]}>
                <TouchableOpacity onPress={() => {
                        setIsFollowingSelected(true)
                        isFollowing = true
                    }}>
                    <View style={[layout.justifyCenter, layout.itemsCenter,{width: 100}]}>
                        <Text style={[isFollowingSelected ? fonts.gray100 : fonts.gray400, fonts.bold]}>Following</Text>
                    </View>
                </TouchableOpacity>
                <Text style={[fonts.gray100, fonts.bold]}>|</Text>
                <TouchableOpacity onPress={() => {
                        setIsFollowingSelected(false)
                        isFollowing = false
                    }}>
                    <View style={[layout.justifyCenter, layout.itemsCenter,{width: 100}]}>
                        <Text style={[isFollowingSelected ? fonts.gray400 : fonts.gray100, fonts.bold]}>For You</Text>
                    </View>
                </TouchableOpacity>

            </View>
            <SocialConnectCommentBottomSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                selectedCommentItem={selectedCommentItem}
            />
            {isPreloading &&
                <ActivityIndicator
                    animating
                    color={"gray"}
                    size="large"
                    style={{ flex: 1, position:"absolute", top:"50%", left:"45%" }}
                />
            }
            
        </View>
    )

}
export default SocialConnect