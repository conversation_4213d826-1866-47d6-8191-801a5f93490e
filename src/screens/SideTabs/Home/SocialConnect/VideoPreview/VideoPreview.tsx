import { Alert, TouchableOpacity, View, Text, Platform } from "react-native"
import { useTheme } from "@/theme";
import { useEffect, useRef, useState } from "react";
import { useRoute, useNavigation } from "@react-navigation/native";
import _Icon from 'react-native-vector-icons/Feather';
import Video, { OnLoadData, OnProgressData, VideoRef } from "react-native-video"
import Slider from '@react-native-community/slider';
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import PlayIcon from "@/theme/assets/images/SocialConnect/PlayIcon.png";
import PauseIcon from "@/theme/assets/images/SocialConnect/PauseIcon.png";
import VolumeIcon from "@/theme/assets/images/SocialConnect/VolumeIcon.png";
import UnmuteIcon from "@/theme/assets/images/SocialConnect/UnmuteIcon.png";
import ChevronLeftIcon from "@/theme/assets/images/SocialConnect/ChevronLeftIcon.png";
import CameraIcon from "@/theme/assets/images/SocialConnect/CameraIcon.png";
import { formatVideoSliderTime, openCamera, timeSince } from "@/utils/utility";
import SocialConnectPostFooter from "@/components/molecules/SocialConnectFooter/SocialConnectFooter";
import FastImage from "react-native-fast-image";
import ExpandableText from "@/components/atoms/ExpandableText/ExpandableText";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import SocialConnectPostCommentsSheet from "@/components/molecules/SocialConnectPostCommentsSheet/SocialConnectPostCommentsSheet";
import { useLikeBookmarkToggleMutation } from "../hooks/useLikeBookmarkToggleMutation";
import { z } from 'zod';
import LinearGradient from "react-native-linear-gradient";
import ImagePicker from "react-native-image-crop-picker";
import { useSafeAreaInsets } from "react-native-safe-area-context";

function VideoPreview() {

    const route = useRoute();
    // const { item } = route.params;
    const [item, setItem] = useState(route.params?.item)

    const navigation = useNavigation();

    const {
        layout,
        fonts,
        colors,
        gutters,
        backgrounds,
    } = useTheme();

    const c = colorTokens();

    const { onLikeButtonTapped, MUTATION_TYPE } = useLikeBookmarkToggleMutation(false, (post) =>{
        setItem(post)
    });

    const { top, bottom } = useSafeAreaInsets();

    const videoRef = useRef<VideoRef>(null);
    const [isPaused, setIsPaused] = useState(true);
    const [duration, setDuration] = useState(0);
    const [currentTime, setCurrentTime] = useState(0);
    const [isMuted, setIsMuted] = useState(false);

    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedCommentItem, setSelectedCommentItem] = useState<z.infer<typeof socialConnectPostSchema> | null>(null)

    useEffect(() => {
        console.log("Item", route.params?.item)

    },[route.params?.item])
    function onLoad(data: OnLoadData) {
        setDuration(data.duration);
        setIsPaused(!isPaused);
    }

    function onProgress(data: OnProgressData) {
        setCurrentTime(data.currentTime);
    }

    function togglePlayPause() {
        setIsPaused(!isPaused);
    }

    function toggleMuteUnmute() {
        setIsMuted(!isMuted);
    }

    function seekTo(time: number) {
        videoRef.current.seek(time);
        setCurrentTime(time);
    };

    function onEnd() {
        videoRef.current.seek(0);
        setIsPaused(true);
        setCurrentTime(0);
    }

    function onPressProfilePicture() {
        navigation.navigate('UserProfile', { userId: user?.id });
    }

    function onCommentButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema> ) {
        setSelectedCommentItem(tappedItem);
        setIsModalVisible(true);
    }

    function onLikeButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        onLikeButtonTapped(tappedItem)
    }

    function onBookmarkButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        onLikeButtonTapped(tappedItem, MUTATION_TYPE.BOOKMARK)
    }

    // async function onPressCamera() {
    //     const result = await openCamera('photo');
    //     if (result) {
    //         const params = {
    //             photoLink: result
    //         }
    //         navigation.navigate('AddSocialPost', params);
    //     }
    // }
    function onPressCamera() {
        ImagePicker.openCamera({
            mediaType: 'photo', 
            width: 375,
            height: 375,
            quality: 0.1,
            cropping: true
        }).then((multimedia) => {
            console.log(multimedia)
            const params = {
                photoLink: multimedia.path
            }
            navigation.navigate('AddSocialPost', params);
        });
    }

    const { user, media_link: mediaLink, updated_at: updatedAt, text } = item || {};
    const fullName = `${user?.first_name} ${user?.last_name}`;

    return (
        <View style={[layout.flex_1, layout.justifyCenter, backgrounds.black]}>
            <View style={[{ backgroundColor: c.background.bold.neutral.default}]}>
                <Video
                    ref={videoRef}
                    source={{uri: mediaLink}}
                    style={[ gutters.marginHorizontal_0, {height: '100%', width: '100%', overflow: 'hidden'}]}
                    paused={isPaused}
                    controls={false}
                    onLoad={onLoad}
                    onProgress={onProgress}
                    resizeMode="contain"
                    muted={isMuted}
                    onEnd={onEnd}
                    poster={item.video_thumbnail ?? ''}

                />
            </View>
            <LinearGradient 
                locations={[0,1]} 
                colors={['transparent', '#000000']} 
                useAngle={true} 
                angle={0}  style={[layout.absolute, layout.justifyEnd,  { top: 0, height: top + 80, width: '100%', zIndex: 5 }]}>
                <View style={[layout.row, layout.justifyBetween, gutters.paddingHorizontal_16,gutters.marginBottom_16]}>
                    <AKIcon size={24} source={ChevronLeftIcon} onPress={navigation.goBack} />
                    <AKIcon size={24} source={CameraIcon} onPress={onPressCamera} />
                </View>
                
            </LinearGradient>
            <LinearGradient 
                locations={[0, 1]} 
                colors={['transparent', '#000000']} 
                useAngle={true} 
                angle={180} style={[layout.absolute, layout.flex_1, { width: '100%', bottom: 0 }]}>
                <View style={[layout.row, gutters.paddingHorizontal_16, gutters.marginTop_16, gutters.marginBottom_12, layout.itemsCenter]}>
                    <TouchableOpacity onPress={onPressProfilePicture}>
                        <FastImage
                            source={{uri: user.profile_photo} }
                            resizeMode={FastImage.resizeMode.contain}
                            style={[{width: 28, height: 28, borderRadius: 50,  backgroundColor: colors.black}]}
                        />
                    </TouchableOpacity>
                    <View style={[gutters.marginLeft_8, { gap: 2 }]}>
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Bold, {color: c.content.onBold.default.emphasis}]}>{fullName}</Text>
                    </View>
                    <View style={[layout.flex_1, layout.itemsEnd]}>
                        <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, {color: c.content.onBold.default.subdued}]}>{timeSince(new Date(updatedAt))}</Text>
                    </View>
                </View>
                {
                    text && (
                        <View style={[gutters.paddingHorizontal_16, gutters.marginBottom_12]}>
                            <ExpandableText text={text} textColor={c.content.onBold.default.emphasis} />
                        </View>
                    )
                }
                <View style={[layout.row, layout.itemsCenter, gutters.paddingHorizontal_12]}>
                    <AKIcon key={isPaused ? 'isPaused' : 'playing'} source={isPaused ? PlayIcon : PauseIcon} onPress={togglePlayPause} styles={[gutters.marginRight_12]} />
                    <Slider
                        style={{ flex: 1, height: 40 }}
                        value={currentTime}
                        minimumValue={0}
                        maximumValue={duration}
                        onSlidingComplete={seekTo}
                        minimumTrackTintColor='#AEAEAE' //{c.fill.onBold.transparent.restSecondary}
                        maximumTrackTintColor="#8C8C8C"
                    />
                    <View style={[layout.itemsCenter, { width: 68 }]}>
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.body, { color: c.content.onBold.default.default }]}>{formatVideoSliderTime(currentTime)}</Text>
                    </View>
                    <AKIcon key={isMuted ? 'muted' : 'unmuted'} source={isMuted ? UnmuteIcon : VolumeIcon} onPress={toggleMuteUnmute} />
                </View>
                <View style={{ marginBottom: bottom || 8 }}>
                    <SocialConnectPostFooter
                        item={item}
                        onCommentButtonTapped={onCommentButtonTapped}
                        onLikeButton={onLikeButton}
                        onBookmarkButton={onBookmarkButton}
                        isWhite
                    />
                </View>
                
            </LinearGradient>
            <SocialConnectPostCommentsSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                item={selectedCommentItem}
            />
        </View>
    )

}

export default VideoPreview;
