import { Alert, TouchableOpacity, View, Text } from "react-native"
import { useTheme } from "@/theme";
import { useRef, useState, useEffect } from "react";
import { z } from "zod";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { SocialConnectCommentBottomSheet, SocialConnectContentCell } from "@/components/molecules";
import { onShareButtonTapped } from "../utility";
import { useLikeBookmarkToggleMutation } from "../hooks/useLikeBookmarkToggleMutation";
import getPostDetail from "@/services/socialConnect/getPostDetail";
import { useRoute, useNavigation, useIsFocused, RouteProp } from "@react-navigation/native";
import Spinner from "react-native-loading-spinner-overlay";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useActionSheet } from "@expo/react-native-action-sheet";
import { useTranslation } from "react-i18next";
import { ImageVariant } from '@/components/atoms';
import NavBarLogo from "@/theme/assets/images/NavBarLogo.png"
import _Icon from 'react-native-vector-icons/Feather';
import { getProfile } from "@/services/users";
import deletePost from "@/services/socialConnect/deletePost";
import { ApplicationStackParamList } from "@/types/navigation";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
export type SocialConnectDetailNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'AddText',
    'AddMultimedia'
>;
function SocialConnectDetail() {

    const route = useRoute<RouteProp<ApplicationStackParamList, 'SocialConnectDetail'>>();

    const {
		layout,
        fonts,
        colors,
        gutters,
	} = useTheme();

    const queryClient = useQueryClient();

    const { data: profile } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });

    const [isModalVisible, setIsModalVisible] = useState(false)
    const [isDeleting, setIsDeleting] = useState(false);
    const [selectedCommentItem, setSelectedCommentItem] = useState<z.infer<typeof socialConnectPostSchema> | null>(null)
    const storyCellRef = useRef<typeof SocialConnectContentCell>(null)
    const { t } = useTranslation(['home']);

    const isFocused = useIsFocused();

    const { showActionSheetWithOptions } = useActionSheet();
    const navigation = useNavigation<SocialConnectDetailNavigationProp>();

    useEffect(() => {
        if (isFocused == false) {
            storyCellRef.current?.pauseVideo();
        } else {
            storyCellRef.current?.playVideo();
            console.log("Params:::", route.params)
            if ( JSON.parse(route.params.showComments ?? 'false') == true) {
                if (data != null) {
                    setSelectedCommentItem(data)
                    setIsModalVisible(true)
                }
            }
        }
    },[isFocused])

    const { isLoading, onLikeButtonTapped } = useLikeBookmarkToggleMutation(true);

    const { data, isLoading: isPostLoading, isError, isFetching } = useQuery({
        queryKey: [`social-connect-post-${route?.params.id}`],
        queryFn: () => getPostDetail(route?.params.id)
    });

    useEffect(() => {
        if (isPostLoading == false) {
            if ( JSON.parse(route.params.showComments ?? 'false') == true) {
                if (data != null) {
                    setSelectedCommentItem(data)
                    setIsModalVisible(true)
                }
            }
        }
    }, [isPostLoading])
    const deletePostMutation = useMutation(
        {
            mutationFn: (data: any) => deletePost(data),
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );

    function onSuccessMutation(): void {
        setIsDeleting(false);
        queryClient.setQueryData(['get_user_profile'], (cacheData: any) => {
            let posts = cacheData?.posts?.filter((post: any) => post.id !== data?.id );
            return {
                ...cacheData,
                posts,
            };
        });
        navigation.goBack();
    }

    function onErrorMutation(error: any): void {
        setIsDeleting(false);
        Alert.alert("Error!", error.message);
    }

    const headerRightComponent = () => {
        return (
            <TouchableOpacity onPress={openActionSheet} style={[gutters.marginRight_4]}>
                <_Icon style={[]} name='more-vertical' size={25} color={colors.gray800}/>
            </TouchableOpacity>
        );
    }

    useEffect(() => {
        if (!!profile && profile?.user_profile?.id === data?.user?.id) {
            let options = {
                headerShown: true,
                headerTintColor: colors.gray800,
                headerTitle: () => <ImageVariant  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>,
                headerRight: headerRightComponent,
            };
            navigation.setOptions(options);
        }
    }, [navigation, profile, data]);


    function onCommentButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema> ): void {
        setSelectedCommentItem(tappedItem)
        setIsModalVisible(true)
    }

    function openActionSheet(): void {
        let options = [t("home:Edit"), t("home:Delete"), t("home:Cancel")];
        showActionSheetWithOptions({
            options: options,
            cancelButtonIndex: 2,
            destructiveButtonIndex: 2,
            showSeparators: true,
            textStyle: fonts.green50,
            }, (i?: number) => {
                if (i === 0) {
                    handleEditPost();
                } else if (i === 1) {
                    handledeletePost();
                }
        }); 
    }

    function handleEditPost(): void {
        const postTypeMapping = {
            'post': 'photo',
            'story': 'video',
        };
        if(data?.type === "text") {
            navigation.navigate('AddText', { 
                postText: data?.text, isUpdating: true, postId: data?.id,
            })
        } else {
            if (data != null) {
                navigation.navigate('AddMultimedia', {
                    postId: data?.id,
                    postText: data?.text,
                    postType: postTypeMapping[data?.type as keyof typeof postTypeMapping],
                    isUpdating: true,
                    ...(data?.type === 'post' && { photoLink: data?.media_link }), 
                    ...(data?.type === 'story' && { videoLink: data?.media_link }),
                })
            }
            
        }
    }

    function handledeletePost(): void {
        Alert.alert('Delete Post', 'Are you sure you want to delete the post?', [
            {
              text: 'No',
              onPress: () => {},
            },
            {
                text: 'Yes',
                onPress: () => {
                    setIsDeleting(true);
                    deletePostMutation.mutate(data?.id)
                }
            },
        ]);
    }

    if (isError) {
        return (
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
                <Text style={[fonts.size_16, fonts.bold]}>No post found!</Text>
            </View>
        )
    }
    return (
        <View style={[layout.flex_1, layout.itemsCenter]}>
            <Spinner
                visible={isPostLoading || isDeleting}
            />
            {
                !isPostLoading && (
                    <SocialConnectContentCell
                        cellRef={storyCellRef}
                        isFocused={true}
                        onLikeButtonTapped={onLikeButtonTapped} 
                        onCommentButtonTapped={onCommentButtonTapped} 
                        onShareButtonTapped={onShareButtonTapped}
                        item={data}
                        isLoading={isLoading}
                    />
                )
            }
            <SocialConnectCommentBottomSheet
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                selectedCommentItem={selectedCommentItem}
            />
        </View>
    )

}

export default SocialConnectDetail;
