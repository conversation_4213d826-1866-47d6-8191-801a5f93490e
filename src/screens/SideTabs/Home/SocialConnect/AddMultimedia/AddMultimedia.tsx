import { AKButton } from "@/components/atoms";
import addPost from "@/services/socialConnect/addPost";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useIsFocused, useNavigation, useRoute } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { Alert, Platform, TextInput, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Spinner from "react-native-loading-spinner-overlay";
import Video, { VideoRef } from "react-native-video";
import { getVideoMetaData, Video as VideoCompressor } from 'react-native-compressor';
import _Icon from 'react-native-vector-icons/MaterialIcons';
import { useActionSheet } from "@expo/react-native-action-sheet";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import editPost from "@/services/socialConnect/editPost";
import { useQueryCacheUpdate } from "../hooks/useQueryCacheUpdate";
import { z } from "zod";
import { basicSchema } from "@/types/schemas/basic";

type SuccessResponse = z.infer<typeof basicSchema>

function AddMultimedia() {
    const { t } = useTranslation(['home']);
    const route = useRoute<RouteProp<ApplicationStackParamList, 'AddMultimedia'>>();
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();

    const { showActionSheetWithOptions } = useActionSheet();
    const [text, setText] = useState(route?.params?.postText || '');
    const [photoLink, setPhotoLink] = useState(route?.params?.photoLink);
    const [videoLink, setVideoLink] = useState(route?.params?.videoLink);
    const navigation = useNavigation()
    const [isLoading, setIsLoading] = useState(false)
    const videoRef = useRef<VideoRef>(null)
    const isFocused = useIsFocused();
    const hasPostText = useRef(!!route?.params?.postText);

    const { updateQueryCache } = useQueryCacheUpdate(route?.params?.postId);

    useEffect(() => {
        if (isFocused == false) {
            videoRef.current?.pause()
        }
    },[isFocused]);

    function onSuccessMutation(response: SuccessResponse): void {
        setIsLoading(false)
        if (response.success == true) {
            let options = [{text: "Ok", onPress: async () => {
                if (route.params.onGoBack !=null) {
                    route.params.onGoBack(true)
                }
                navigation.goBack()
            }}]
            Alert.alert("Success!", `Post ${isPostUpdating ? 'Edited' : 'Added'} Successfully.`, options)
            if (isPostUpdating) {
                updateQueryCache(text, photoLink, videoLink);
            }
        } else {
            Alert.alert(t('home:Error'), response.message)
        }
    }

    function onErrorMutation(error: any): void {
        setIsLoading(false)
        Alert.alert(t('home:Error'), error.message)
    }

    const addPostMutation = useMutation(
        {
            mutationFn: (data: any) => addPost(data),
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );

    const editPostMutation = useMutation(
        {
            mutationFn: (data: any) => editPost(data, route?.params?.postId),
            onSuccess: onSuccessMutation,
            onError: onErrorMutation,
        },
    );

    function createFormData(multimedia?: string | null, body: Record<string, string> = {}) {
        const data = new FormData();
        if (photoLink != null) {
            data.append('image_file', {
                name: "Image.png",
                type: "png",
                uri: multimedia,
              });
        }
        if (videoLink != null) {
            data.append('video_file', {
                name: "video.mov",
                type: "mov",
                uri: multimedia,
              });
        }
        
      
        Object.keys(body).forEach((key) => {
          data.append(key, body[key]);
        });
        return data;
    };
    async function submitButtonPressed(): Promise<void> {
        let multimediaLink = photoLink ? photoLink : videoLink

        setIsLoading(true)
        if (videoLink != null) {
            const metaData = await getVideoMetaData(videoLink);
            console.log("MetaData before:", metaData)
            const result = await VideoCompressor.compress(
                videoLink,
                {
                  compressionMethod: 'auto',
                },
                (progress) => {
                  console.log('Compression Progress: ', progress);
                }
            );
            const metaDataAfter = await getVideoMetaData(result);
            console.log("MetaData After:", metaDataAfter)
            multimediaLink = result
        }
        let data: any = {
            type: photoLink ? 'post' : 'story'
        }
        if (hasPostText.current || text) {
            data.text = text;
        }
        let mutateFunction = addPostMutation.mutate;
        if (isPostUpdating) {
            data["_method"] = 'PUT';
            mutateFunction = editPostMutation.mutate;
        }
        // multimediaLink = photoLink ? photoLink : videoLink
        let formData = createFormData(multimediaLink, data)
        mutateFunction(formData);
    }

    function openActionSheet(): void {
        let options = [photoLink ? t("home:ChoosePhoto") : t("home:ChooseVideo"), t("home:Camera"), t("home:Cancel")];
        showActionSheetWithOptions({
            options: options,
            cancelButtonIndex: 2,
            destructiveButtonIndex: 2,
            showSeparators: true,
            textStyle: fonts.green50,
            }, (i?: number) => {
                onPickOptionPress(i ?? 0)
        }); 
    }

    function updateMediaLink(result: any): void {
        if (result.assets != null && result.assets.length > 0) {
            let multimedia = result.assets[0]
            if (multimedia != null) {
                if (postType == "photo") {
                    let link = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri;
                    setPhotoLink(link);
                } else if (postType == "video") {
                    let link = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri;
                    setVideoLink(link)
                }
            }   
        }
    }

    async function onPickOptionPress (index: number): Promise<void> {
        if (index == 0) {
            launchImageLibrary({mediaType: postType, quality: 0.1}, (result) => {
                updateMediaLink(result);
            });
        } else if (index == 1) {
            const result = await launchCamera({mediaType: postType, quality: 0.1});
            updateMediaLink(result);         
        }
    }

    const isPostUpdating = !!route?.params?.isUpdating;
    const postType = route?.params?.postType;

    return (
        <KeyboardAwareScrollView contentContainerStyle={[gutters.marginBottom_24]}>
            <Spinner
                visible={isLoading}
            />
            <View>
                <View style={[layout.row, gutters.marginHorizontal_12, gutters.marginTop_16]}>
                    <View style={[]}>
                        {photoLink && 
                            <FastImage
                                defaultSource={require('@/theme/assets/images/ProfilePlaceholder.png')}
                                source={photoLink != null ? {uri: photoLink} : require('@/theme/assets/images/ProfilePlaceholder.png')}
                                resizeMode={FastImage.resizeMode.cover}
                                style={{height: 100, width: 100, borderRadius: 15}}
                            />
                        }
                        {videoLink && 
                            <View>
                                <Video
                                    ref={videoRef}
                                    source={{uri: videoLink}}
                                    controls={false}
                                    paused={false}
                                    style={[ gutters.marginHorizontal_0,{height: 100, width: 100, borderRadius: 15, overflow: 'hidden'}]}
                                    hideShutterView={false}
                                    allowsExternalPlayback={false}
                                    repeat={true}
                                    playInBackground={false}
                                    disableFocus={false}
                                    onPlaybackStateChanged={(e) => {
                                    
                                    }}  
                                />

                            </View>
                        }
                        {
                            isPostUpdating && (
                                <TouchableOpacity onPress={openActionSheet} style={[layout.absolute, borders.gray800, borders.rounded_16, gutters.padding_4, backgrounds.white, {bottom: -6, right: -6}]}>
                                    <_Icon style={[]} name='edit' size={15} color={colors.gray800}/>
                                </TouchableOpacity>
                            )
                        }
                    </View>
                    <View style={[gutters.marginHorizontal_12, layout.flex_1]}>
                        <TextInput
                            value={text}
                            multiline={true}
                            placeholder={t('home:WriteText')}
                            style={[{minHeight: 100}]}
                            placeholderTextColor={colors.gray200}
                            onChangeText={(text) => setText(text)}
                        />
                    </View>
                    

                </View>
                <View style={[gutters.marginTop_32, gutters.marginBottom_24]}>
                    <AKButton 
                        height={48}
                        backgroundColor={backgrounds.green50}
                        title={isPostUpdating ? t("home:UpdatePost") : t("home:AddPost")}
                        textStyle={fonts.gray50}
                        onPress={submitButtonPressed}
                        viewStyle={gutters.marginHorizontal_32}
                    />
                </View>

            </View>
            

        </KeyboardAwareScrollView>
    )
}
export default AddMultimedia