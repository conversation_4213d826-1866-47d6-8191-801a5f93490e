import { Alert, Share } from 'react-native';
import { onShareButtonTapped } from '../utility';

// Mock the React Native modules
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn()
  },
  Share: {
    share: jest.fn(),
    sharedAction: 'sharedAction',
    dismissedAction: 'dismissedAction'
  }
}));

// Mock environment variables
jest.mock('react-native-config', () => ({
  WEB_BASE_URL: 'https://test.akinaconnect.com/'
}));

// Set environment variable directly as fallback
process.env.WEB_BASE_URL = 'https://test.akinaconnect.com/';

describe('SocialConnect Utility Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('onShareButtonTapped', () => {
    const mockPost = {
      id: 123,
      user: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>'
      },
      // Add other required properties from the schema
      content: 'Test post content',
      created_at: '2023-01-01T12:00:00Z',
      updated_at: '2023-01-01T12:00:00Z',
      likes_count: 5,
      comments_count: 2,
      is_liked: false,
      is_bookmarked: false,
      media: []
    };

    it('should call Share.share with correct parameters', async () => {
      (Share.share as jest.Mock).mockResolvedValue({ action: Share.sharedAction });
      
      await onShareButtonTapped(mockPost);
      
      expect(Share.share).toHaveBeenCalledWith({
        message: 'Here is a post from John Doe',
        url: 'https://test.akinaconnect.com/social-connect/post/123'
      });
    });

    it('should handle successful share with activity type', async () => {
      (Share.share as jest.Mock).mockResolvedValue({ 
        action: Share.sharedAction,
        activityType: 'com.apple.UIKit.activity.CopyToPasteboard'
      });
      
      await onShareButtonTapped(mockPost);
      
      expect(Share.share).toHaveBeenCalled();
      // No additional expectations needed as the function doesn't do anything specific with this result
    });

    it('should handle successful share without activity type', async () => {
      (Share.share as jest.Mock).mockResolvedValue({ 
        action: Share.sharedAction,
        activityType: null
      });
      
      await onShareButtonTapped(mockPost);
      
      expect(Share.share).toHaveBeenCalled();
      // No additional expectations needed as the function doesn't do anything specific with this result
    });

    it('should handle dismissed share', async () => {
      (Share.share as jest.Mock).mockResolvedValue({ 
        action: Share.dismissedAction
      });
      
      await onShareButtonTapped(mockPost);
      
      expect(Share.share).toHaveBeenCalled();
      // No additional expectations needed as the function doesn't do anything specific with this result
    });

    it('should show alert when share fails', async () => {
      const errorMessage = 'Share failed';
      (Share.share as jest.Mock).mockRejectedValue({ message: errorMessage });
      
      await onShareButtonTapped(mockPost);
      
      expect(Share.share).toHaveBeenCalled();
      expect(Alert.alert).toHaveBeenCalledWith(errorMessage);
    });

    it('should handle post with missing user information', async () => {
      const incompletePost = {
        id: 123,
        user: null,
        // Add other required properties
        content: 'Test post content',
        created_at: '2023-01-01T12:00:00Z',
        updated_at: '2023-01-01T12:00:00Z',
        likes_count: 5,
        comments_count: 2,
        is_liked: false,
        is_bookmarked: false,
        media: []
      };
      
      (Share.share as jest.Mock).mockResolvedValue({ action: Share.sharedAction });
      
      await onShareButtonTapped(incompletePost);
      
      expect(Share.share).toHaveBeenCalledWith({
        message: 'Here is a post from undefined undefined',
        url: 'https://test.akinaconnect.com/social-connect/post/123'
      });
    });
  });
});