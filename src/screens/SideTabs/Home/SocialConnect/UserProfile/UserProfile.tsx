import getUserProfile from "@/services/socialConnect/users/getUserProfile"
import { useTheme } from "@/theme"
import { ApplicationStackParamList } from "@/types/navigation"
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native"
import { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { useInfiniteQuery, useMutation, useQuery } from "@tanstack/react-query"
import { useCallback, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { ActivityIndicator, Alert, Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import Spinner from "react-native-loading-spinner-overlay"
import _Icon from 'react-native-vector-icons/AntDesign';
import { getProfile } from "@/services/users"
import followUnfollowUser from "@/services/socialConnect/users/followUnfollowUser"
import AKIcon from "@/components/atoms/AKIcon/AKIcon"
import { colorTokens } from "@/theme/colorTokens"
import SocialConnectProfileHeader from "@/components/molecules/SocialConnectProfileHeader/SocialConnectProfileHeader"
import Add from "@/theme/assets/images/AddIcon.png";
import BackButtonIcon from "@/theme/assets/images/BackButtonIcon.png";
import SmileCard from "@/theme/assets/images/SocialConnect/SmileCard.png";

import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import SocialConnectFeed from "@/components/molecules/SocialConnectFeed/SocialConnectFeed"
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView"
import getPostsByUser from "@/services/socialConnect/getPostsByUser"
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import BlockBottomSheet from "@/components/molecules/BlockBottomSheet/BlockBottomSheet"

export type ProfileNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'UserListing'
>;

function UserProfile() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'Profile'>>();
    const navigation = useNavigation<ProfileNavigationProp>()

    const c = colorTokens();

    const { t } = useTranslation(['home', 'sideTabs']);
    const { top } = useSafeAreaInsets();
    const {
        layout,
        gutters,
        fonts,
        borders,
    } = useTheme();

    const {data, refetch, isLoading } = useQuery({
        queryKey: [`get_user_profile${route.params.userId}`],
        queryFn: () => getUserProfile(route.params.userId)
    });

    const { data: currentuserProfileResponse } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile, 
    });

    const postListingResponse =  useInfiniteQuery({
        queryKey: [`social-connect-user-posts-${route.params.userId}`],
        initialPageParam: 1,
        // refetchOnWindowFocus: "always",
        queryFn:  ({pageParam}) =>  {
            return getPostsByUser(route.params.userId, pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage?.current_page < lastPage?.last_page ?  lastPage?.current_page + 1 : undefined,
    })

    let flattenData = useMemo(() => {
        return postListingResponse.data?.pages.flatMap(page => page.data) || [];
    }, [postListingResponse.data?.pages, postListingResponse.isRefetching]);

    const [trackedY, setTrackedY] = useState(500);

    const scrollY = useSharedValue(0);

    const scrollHandler = useAnimatedScrollHandler({
        onScroll: (event) => {
            scrollY.value = event.contentOffset.y;
        },
    });

    function onMeasured(y: number): void {
        setTrackedY(y)
    }
    function onReloadAfterUserStatusChnaged() {
        
    }

    function ListHeaderComponent(): JSX.Element {
        return (
            <SocialConnectProfileHeader
                onMeasured={onMeasured}
                profileInfo={data?.user}
                postsCount={data?.posts?.length || 0}
                isOwnProfile={isOwnProfile}
                profileRefetch={refetch}
                userId={currentuserProfileResponse?.user_profile?.id}
            />
        )
    }

    const animatedHeaderStyle = useAnimatedStyle(() => {
        const opacity = interpolate(
            scrollY.value,
            [trackedY - 120, trackedY - 100],
            [0, 1],
            Extrapolate.CLAMP
        );
        return {
            opacity,
        };
    });

    function onPressAdd(): void {
        navigation.navigate('AddSocialPost', {})
    }

    function getAddPostButton(): JSX.Element {
        return (
            <TouchableOpacity onPress={onPressAdd} style={[layout.row, layout.itemsCenter, gutters.paddingVertical_8, gutters.paddingHorizontal_16, gutters.marginTop_4, borders.rounded_8, { gap: 8, backgroundColor: c.fill.bold.neutrals.rest }]}>
                <AKIcon source={Add} />
                <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, { color: c.content.onBold.default.default }]}>Add Post</Text>
            </TouchableOpacity>
        )
    }

    function ListFooterComponent() {
        return postListingResponse.isFetchingNextPage ? <ActivityIndicator size="small" style={[gutters.marginVertical_4]} /> : null
    }

    function getEmptyPlaceholder(): JSX.Element {
        let placeHolder;
        if (isOwnProfile) {
            placeHolder = (
                <EmptyDataView
                    heading="Start Posting"
                    desc="Your feed starts with your first post."
                    actionButton={getAddPostButton}
                />
            )
        } else if(isProfilePrivate) {
            placeHolder = (
                <EmptyDataView
                    heading="This Profile is Private"
                    desc="Send a request to see their posts."
                />
            )
        } else {
            placeHolder = (
                <EmptyDataView
                    heading="No Posts Yet"
                    image={SmileCard}
                />
            )
        }
        return (
            <View style={[layout.flex_1]}>
                {placeHolder}
            </View>
        );
    }

    const loadNext = useCallback(() => {
        postListingResponse.hasNextPage && postListingResponse.fetchNextPage();
    }, [postListingResponse.fetchNextPage, postListingResponse.hasNextPage]);

    const isOwnProfile = currentuserProfileResponse?.user_profile?.id == route.params.userId;
    const isProfilePrivate = false;

    return (
        <AKSafeAreaView>
            <Spinner
                visible={isLoading}
            />
            
            <SocialConnectFeed 
                ListHeaderComponent={ListHeaderComponent}
                onScroll={scrollHandler}
                data={flattenData}
                ListEmptyComponent={getEmptyPlaceholder}
                ListFooterComponent={ListFooterComponent}
                loadNext={loadNext}
                refetchProfile={refetch}
            />
            <Animated.View style={[animatedHeaderStyle, layout.absolute, layout.z10, layout.itemsCenter, layout.justifyCenter, { top: top || 6, left:10, width: 40, height: 40, backgroundColor: 'black', borderRadius: 25}]} />
            <View style={[layout.absolute, layout.z10, layout.itemsCenter, layout.justifyCenter, { top: top || 6, left:10, width: 40, height: 40, borderRadius: 25}]}>
                <AKIcon source={BackButtonIcon} size={32} onPress={() => navigation.goBack()}/>
            </View>
        </AKSafeAreaView>
    )
}

export default UserProfile;
