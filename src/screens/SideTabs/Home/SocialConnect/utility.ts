import { Alert, Platform, Share } from "react-native";
import { z } from "zod";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";

export async function onShareButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema>) {
    let shareURL = process.env.WEB_BASE_URL+`social-connect/post/${tappedItem.id}` 
    try {
        const result = await Share.share({
            message: `Here is a post from ${tappedItem?.user?.first_name} ${tappedItem?.user?.last_name}\n${shareURL}`
        });
        if (result.action === Share.sharedAction) {
            if (result.activityType) {
            // shared with activity type of result.activityType
            } else {
            // shared
            }
        } else if (result.action === Share.dismissedAction) {
            // dismissed
        }
    } catch (error: any) {
        Alert.alert(error.message);
    }
}
