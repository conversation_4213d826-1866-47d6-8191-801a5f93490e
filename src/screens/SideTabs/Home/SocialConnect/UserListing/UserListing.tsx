import { UserCell } from "@/components/molecules";
import followUnfollowUser from "@/services/socialConnect/users/followUnfollowUser";
import getUserFollowers from "@/services/socialConnect/users/getUserFollowers";
import getUserFollowings from "@/services/socialConnect/users/getUserFollowings";
import { getProfile } from "@/services/users";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";
import { userFollowerSchema, userFollowingSchema } from "@/types/schemas/user";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useInfiniteQuery, useMutation, useQuery } from "@tanstack/react-query";
import { useCallback, useMemo, useState } from "react";
import { ActivityIndicator, <PERSON><PERSON>, FlatList, RefreshControl, TouchableOpacity, View } from "react-native"
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
export type UserListingNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'Profile'
>;
function UserListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'UserListing'>>();
    const navigation = useNavigation<UserListingNavigationProp>()
    
    const currentuserProfileResponse = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const [isLoading, setIsLoading] = useState(false)
    const [isRefreshing, setIsRefreshing] = useState(false)
    const userListingResponse = useInfiniteQuery({
        queryKey: [`social-connect-users`, route.params.isFollowing ? `-${route.params.userId}-following`: `-${route.params.userId}-followers`],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            console.log("Page Param: ;", pageParam)
            return route.params.isFollowing? getUserFollowings(route.params.userId,pageParam) : getUserFollowers(route.params.userId, pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,

    });
    const flattenData = useMemo(() => {
        return userListingResponse.data?.pages.flatMap(page => page.data) || [];
    }, [userListingResponse.data, userListingResponse.isRefetching]);
    const followUnFollowMutation = useMutation(
        {
            mutationFn: (data: any) => followUnfollowUser(data),
            onSuccess: (response) => {
                userListingResponse.refetch()
                setIsLoading(false)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        }
    );
    function followUnfollowButtonTapped(item:  z.infer<typeof socialConnectUserSchema>) {
        let data = {
            user_id: item.id
        }
        setIsLoading(true)
        followUnFollowMutation.mutate(data)
    }
    function profileTapped(user: z.infer<typeof socialConnectUserSchema>) {
        navigation.push('Profile', {userId: user.id})
    }
    function renderItem({item}: any) {
        let user = item as z.infer<typeof socialConnectUserSchema>
        return (
            <TouchableOpacity onPress={() => profileTapped(user)}>
                <UserCell item={user} currentUser={currentuserProfileResponse.data?.user_profile} onPress={followUnfollowButtonTapped}/>
            </TouchableOpacity>
        )  
    }
    const loadNext = useCallback(() => {
            userListingResponse.hasNextPage && userListingResponse.fetchNextPage();
    }, [userListingResponse.fetchNextPage, userListingResponse.hasNextPage]);
        
    const onRefresh = useCallback(() => {
    if (!isRefreshing) {
        setIsRefreshing(true);
        userListingResponse.refetch()
        .then(() => setIsRefreshing(false))
        .catch(() => setIsRefreshing(false));
    }
    }, [isRefreshing, userListingResponse.refetch]);
    
    return (
        <View style={[layout.flex_1]}>
            <Spinner
                visible={isLoading}
            />
            <FlatList 
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{paddingBottom: 20}} 
                data={userListingResponse.data?.pages[0].total != 0 ? flattenData : []}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
                refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                onEndReached={loadNext}
                removeClippedSubviews={true}
                ListFooterComponent={
                    <View style={[layout.row,layout.justifyCenter, layout.itemsCenter ,{height: 100}]}>
                    {userListingResponse.isFetchingNextPage && <ActivityIndicator />}
                    </View>
                }
            />
        </View>
        
    )
}
export default UserListing