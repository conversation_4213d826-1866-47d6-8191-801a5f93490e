import { ReportingContent } from "@/components/molecules/ReportPostBottomSheet/ReportPostBottomSheet";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

export const useQueryCacheUpdate = (postId: number | undefined) => {
    const queryClient = useQueryClient();

    function updateQueryCache(text: string, userId: number, type: string, mediaLink?: string | null) {
        queryClient.setQueryData([`social-connect-post-${postId}`], (cacheData: any) => {
            return {
                ...cacheData,
                text,
                type,
                media_link: mediaLink,
            }
        })
        queryClient.setQueriesData(
            {
                predicate: (query) => [`social-connect-user-posts-${userId}`, 'social-connect-posts-following', 'social-connect-posts'].includes(query?.queryKey[0] as string),
            },
            (cacheData: any) => {
            if (cacheData != null) {
                let pages = cacheData?.pages?.map((page: any) => {
                    let pageData = page?.data?.map((post: z.infer<typeof socialConnectPostSchema>) => {
                        if (post.id === postId) {
                            return {
                                ...post,
                                text,
                                type,
                                media_link: mediaLink,
                            };
                        } else {
                            return post;
                        }
                    });
                    return {
                        ...page,
                        data: pageData,
                    };
                });
                return {
                    ...cacheData,
                    pages: pages,
                };
            }
        });
        queryClient.setQueryData([`get_user_profile${userId}`], (cacheData: any) => {
            let posts = cacheData?.posts?.map((post: any) => {
                if (post.id === postId) {
                    return {
                        ...post,
                        text,
                        type,
                        media_link: mediaLink,
                    };
                } else {
                    return post;
                }
            });
            return {
                ...cacheData,
                posts,
            };
        });
    }

    function updateProfileCache(shouldIncrement: boolean = true) {
        queryClient.setQueryData(['get_profile'], (cacheData: any) => {
            return {
                ...cacheData,
                user_profile: {
                    ...cacheData.user_profile,
                    sc_posts_count: cacheData.user_profile.sc_posts_count + (shouldIncrement ? 1 : -1),
                },
            }
        });
    }

    function updatePostsCache(userId?: number) {
        queryClient.setQueriesData(
            {
                predicate: (query) => ['social-connect-posts-following', 'social-connect-posts', `social-connect-user-posts-${userId}`].includes(query?.queryKey[0] as string),
            },
            (cacheData: any) => {
            if (cacheData != null) {
                let pages = cacheData?.pages?.map((page: any) => {
                    let pageData = page?.data?.filter((post: any) => post.id !== postId);
                    return {
                        ...page,
                        data: pageData,
                    };
                });
                return {
                    ...cacheData,
                    pages: pages,
                };
            }
        });
    }

    function updateCommentsCache(commentID: number) {
        queryClient.setQueriesData(
            {
                predicate: (query) => [`social-connect-posts-${postId}`, `social-connect-post-${postId}`].includes(query?.queryKey[0] as string),
            },
            (cacheData: any) => {
            const returnObject = {
                ...cacheData,
                comments: cacheData?.comments?.map((comment: any) => {
                    if (comment.id === commentID) {
                        return {
                            ...comment,
                            reported_comment_exists: true,
                        };
                    } else {
                        return comment;
                    }
                }),
                comments_count: cacheData?.comments_count - 1,
            }
            return returnObject;
        });
    }

    function updateRepliesCache(commentID?: number, replyID?: number) {
        queryClient.setQueriesData(
            {
                predicate: (query) => [`social-connect-posts-${postId}`, `social-connect-post-${postId}`].includes(query?.queryKey[0] as string),
            },
            (cacheData: any) => {
            const returnObject = {
                ...cacheData,
                comments: cacheData?.comments?.map((comment: any) => {
                    if (comment.id === commentID) {
                        return {
                            ...comment,
                            replies: comment.replies.map((reply: any) => {
                                if (reply.id === replyID) {
                                    return {
                                        ...reply,
                                        reported_reply_exists: true,
                                    };
                                } else {
                                    return reply;
                                }
                            }),
                        };
                    } else {
                        return comment;
                    }
                }),
            }
            return returnObject;
        });
    }

    function updateCommentsCacheForOtherModules(moduleName: string, itemId: number, reportingContent: ReportingContent) {
        if (reportingContent?.replyID != null) {
            queryClient.setQueryData([`${moduleName}Detail${itemId}`], (cachedData: any) => {
                if (cachedData != null) {
                    return {
                        ...cachedData,
                        comments: cachedData.comments.map((comment: any) => {
                            if (comment.id == reportingContent?.commentID) {
                                return {
                                    ...comment,
                                    replies: comment.replies.map((reply: any) => {
                                        if (reply.id == reportingContent?.replyID) {
                                            return {
                                                ...reply,
                                                reported_reply_exists: true,
                                            };
                                        } else {
                                            return reply;
                                        }
                                    }),
                                };
                            } else {
                                return comment;
                            }
                        }),
                    }
                }
            })
        } else {
            queryClient.setQueryData([`${moduleName}Detail${itemId}`], (cachedData: any) => {
                if (cachedData != null) {
                    return {
                        ...cachedData,
                        comments: cachedData.comments.map((comment: any) => {
                            if (comment.id == reportingContent?.commentID) {
                                return {
                                    ...comment,
                                    reported_comment_exists: true,
                                };
                            } else {
                                return comment;
                            }
                        }),
                    }
                }
            })
        }
    }

    return {
        updateQueryCache,
        updateProfileCache,
        updatePostsCache,
        updateCommentsCache,
        updateRepliesCache,
        updateCommentsCacheForOtherModules,
    }
}
