import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Alert } from 'react-native';
import likeUnlikePost from "@/services/socialConnect/likeUnlikePost";
import bookmarkUnbookmarkPost from "@/services/socialConnect/bookmarkUnbookmarkPost";
import { z } from 'zod';
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { socialConnectPostLikeSchema } from '@/types/schemas/socialConnectPostLike';

export const useLikeBookmarkToggleMutation = (isPost: boolean, onUpdate?: (item: z.infer<typeof socialConnectPostSchema>) => void) => {
    const queryClient = useQueryClient();
    const { t } = useTranslation(['home']);

    const MUTATION_TYPE = {
        LIKE: 'LIKE',
        BOOKMARK: 'BOOKMARK'
    };

    const [isLoading, setIsLoading] = useState(false)

    const mutation = useMutation({
        mutationFn: (data: amy) => {
            if (data.type == MUTATION_TYPE.BOOKMARK) {
                return bookmarkUnbookmarkPost(data.id);
            } else {
                return likeUnlikePost(data.id);
            }
        },
        onSuccess: (response: z.infer<typeof socialConnectPostLikeSchema>) => {
            setIsLoading(false);
            if (onUpdate != null) {
                onUpdate(response.post)
            }
            if (isPost) {
                queryClient.setQueryData([`social-connect-post-${response.post.id}`], () => {
                    return response.post;
                })
            } else {
                
                queryClient.setQueriesData(
                    {
                        predicate: (query) => ['social-connect-posts', 'social-connect-posts-following', `social-connect-user-posts-${response.post.user.id}`].includes(query?.queryKey[0] as string),
                    }, 
                    (cacheData: any) => {
                        if (cacheData !=null) {
                            let pages = cacheData?.pages?.map((page: any) => {
                                let pageData = page?.data?.map((post: z.infer<typeof socialConnectPostSchema>) => {
                                    if (post.id === response.post.id) {
                                        return response.post;
                                    } else {
                                        return post;
                                    }
                                });
                                return {
                                    ...page,
                                    data: pageData,
                                };
                            });
                            return {
                                ...cacheData,
                                pages: pages,
                            };
                        }
                    
                });
            }
        },
        onError: (error: any) => {
            setIsLoading(false);
            Alert.alert(t('home:Error'), error.message);
        },
    });

    function onLikeButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema>, type: string = MUTATION_TYPE.LIKE) {
        setIsLoading(true)
        console.log("Button Tapped")
        mutation.mutate({
            id: tappedItem.id,
            type
        });
    }

    return {
        isLoading,
        onLikeButtonTapped,
        MUTATION_TYPE
    };
};
