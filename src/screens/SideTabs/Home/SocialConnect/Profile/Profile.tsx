import { SafeScreen } from "@/components/template"
import getUserProfile from "@/services/socialConnect/users/getUserProfile"
import { useTheme } from "@/theme"
import { ApplicationStackParamList } from "@/types/navigation"
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost"
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native"
import { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { useMutation, useQuery } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { Alert, FlatList, Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import Spinner from "react-native-loading-spinner-overlay"
import Video from "react-native-video"
import { z } from "zod"
import _Icon from 'react-native-vector-icons/AntDesign';
import { getProfile } from "@/services/users"
import followUnfollowUser from "@/services/socialConnect/users/followUnfollowUser"
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView"

export type ProfileNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'UserListing'
>;

const Icon = _Icon as React.ElementType

function Profile() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'Profile'>>();
    const navigation = useNavigation<ProfileNavigationProp>()

    const { t } = useTranslation(['home', 'sideTabs']);
    
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const {data, refetch, isFetching } = useQuery({
        queryKey: [`get_user_profile${route.params.userId}`],
        queryFn: () => getUserProfile(route.params.userId)
    });
    const currentuserProfileResponse = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const followUnFollowMutation = useMutation(
        {
            mutationFn: (data: any) => followUnfollowUser(data),
            onSuccess: (response) => {
                refetch()
                setIsLoading(false)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        }
    );
    const [isLoading, setIsLoading] = useState(isFetching)

    useEffect(() => {
        setIsLoading(isFetching)
    },[isFetching])
    const profileLink = data?.user?.profile_photo
    function onFollowingButtonTapped(): void {
        if (data?.user?.followings_count != null && data?.user?.followings_count > 0) {
            navigation.navigate('UserListing', {userId: data.user.id, isFollowing: true})
        }
    }
    function onFollowersTapped(): void {
        if (data?.user?.followers_count != null && data?.user?.followers_count > 0) {
            navigation.navigate('UserListing', {userId: data.user.id, isFollowing: false})
        }
    }
    function onLikesTapped() {
        
    }
    function renderItem({item, index}: any): JSX.Element {
        let currentItem = item as  z.infer<typeof socialConnectPostSchema>

        function onPressPost() {
            navigation.navigate('SocialConnectPostDetail', { id: currentItem?.id })
        }

        if (currentItem.type == 'text') {
            return (
                <TouchableOpacity onPress={onPressPost} style={[ layout.itemsCenter,layout.justifyCenter,backgrounds.gray400, ((index == (data?.posts?.length ?? 0) -1) && ((data?.posts?.length ?? 0) %3 != 0) ? gutters.marginRight_32: gutters.marginRight_0), { flex: 1/3, height: 182 ,borderRadius: 25, overflow: 'hidden'}]}>
                    <Text style={[fonts.gray100]}>{currentItem.text  ?? ''}</Text>

                </TouchableOpacity>
            )
        }
        if (currentItem.type == 'story') {
            return (
                <TouchableOpacity onPress={onPressPost} style={[layout.itemsCenter, ,layout.justifyCenter,backgrounds.gray400,((index == (data?.posts?.length ?? 0) -1) && ((data?.posts?.length ?? 0) %3 != 0) ? gutters.marginRight_32: gutters.marginRight_0),{flex: 1/3, height: 182 ,borderRadius: 25, overflow: 'hidden'}]}>
                    <Video
                        source={ {uri: currentItem.media_link ?? ''}  }
                        controls={false}
                        paused={true}
                        style={[ gutters.marginHorizontal_0,{height: '100%', width: '100%', overflow: 'hidden'}]}
                        hideShutterView={false}
                        allowsExternalPlayback={false}
                        repeat={true}
                        disableFocus={true}
                        onPlaybackStateChanged={(e) => {
                        
                        }}  
                    />
                </TouchableOpacity>
            )
        }
        if (currentItem.type == 'post') {
            return (
                <TouchableOpacity onPress={onPressPost} style={[layout.itemsCenter, layout.justifyCenter,backgrounds.gray400, ((index == (data?.posts?.length ?? 0) -1) && ((data?.posts?.length ?? 0) %3 != 0) ? gutters.marginRight_32: gutters.marginRight_0) ,{flex: 1/3, height: 182 ,borderRadius: 25, overflow: 'hidden'}]}>
                    <FastImage
                        source={{uri: currentItem.media_link ?? ''} }
                        resizeMode={FastImage.resizeMode. cover}
                        style={[gutters.marginHorizontal_0, gutters.marginVertical_0 ,{width: '100%', height: '100%'}]}
                    />
                </TouchableOpacity>
            )
        }
        return (
            <View style={[layout.flex_1, gutters.padding_16, layout.itemsCenter, layout.justifyCenter,backgrounds.gray400,{ height: 182 ,borderRadius: 25}]}>

            </View>
        )
    }
    function onFollowUnfollowButtonTapped(): void {
        let apiData = {
            user_id: data?.user?.id
        }
        setIsLoading(true)
        followUnFollowMutation.mutate(apiData)
    }
    return (
        <AKSafeAreaView>
            <Spinner
                visible={isLoading}
            />
            
            {currentuserProfileResponse.data?.user_profile.id != data?.user.id &&
            <TouchableOpacity style={{zIndex: 2}} onPress={onFollowUnfollowButtonTapped} >
                <View style={ [ layout.absolute ,layout.itemsCenter, layout.justifyCenter, gutters.marginRight_12 ,{width: 48, height: 48, right: 0, zIndex: 0}]}>
                    <Icon style={[gutters.marginRight_4]} name='adduser' size={25} color={(data?.user.is_followed == false ? colors.gray400 : colors.green50)}/>
                </View>
            </TouchableOpacity>
            }
            <View style={[layout.flex_1 ]}>
                <View style={[layout.itemsCenter, gutters.marginHorizontal_0, ]}>
                    <View style={[gutters.marginTop_24, ]}>
                        <FastImage
                            defaultSource={require('@/theme/assets/images/ProfilePlaceholder.png')}
                            source={profileLink != null ? {uri: profileLink} : require('@/theme/assets/images/ProfilePlaceholder.png')}
                            resizeMode={FastImage.resizeMode.cover}
                            style={{height: 150, width: 150, borderRadius: 75}}
                        />
                        
                    </View>
                    <View style={[layout.itemsCenter, gutters.marginTop_16,]}>
                        <Text>{data?.user?.user_name ?? (data?.user?.first_name ?? '') + ' ' +  (data?.user?.last_name ?? '')}</Text>
                        <Text>{(data?.user?.profile?.bio ?? '') }</Text>
                    </View>
                </View>
                <View style={[layout.row,gutters.marginHorizontal_0,layout.justifyCenter, layout.itemsCenter, gutters.marginBottom_12 ,{height: 50, }]}>
                    <View style={[layout.row,  layout.itemsEnd ,{height: 50, width: 300 }]}>

                        <TouchableOpacity onPress={onFollowingButtonTapped}>
                            <View style={[layout.justifyCenter, layout.itemsCenter ,{height: 50, width: 100}]}>
                                <Text style={[fonts.gray800, fonts.size_12, {fontWeight: '800'}]}>{data?.user?.followings_count}</Text>
                                <Text style={[fonts.gray800, fonts.size_12, fonts.Medium]}>{t('home:Following')}</Text>
                            </View>
                        </TouchableOpacity>
                        
                        <TouchableOpacity onPress={onFollowersTapped}>
                            <View style={[layout.justifyCenter, layout.itemsCenter,{height: 50, width: 100}]}>
                                <Text style={[fonts.gray800, fonts.size_12, {fontWeight: '800'}]}>{data?.user?.followers_count}</Text>
                                <Text style={[fonts.gray800, fonts.size_12, fonts.Medium]}>{t('home:Followers')}</Text>
                            </View>
                        </TouchableOpacity >
                        <TouchableOpacity onPress={onLikesTapped}>
                            <View style={[layout.justifyCenter, layout.itemsCenter,{height: 50, width: 100}]}>
                                <Text style={[fonts.gray800, fonts.size_12, {fontWeight: '800'}]}>{data?.user?.social_connect_likes_count}</Text>
                                <Text style={[fonts.gray800, fonts.size_12, fonts.Medium]}>{t('home:Likes')}</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={[gutters.marginHorizontal_16, layout.flex_1, gutters.marginTop_32]}>
                    {data?.posts.length == 0 && 
                        <View style={[layout.flex_1,layout.justifyCenter, layout.itemsCenter]}>
                            <Text>{t('sideTabs:NoPostAvailable')}</Text>
                        </View>
                    }
                    <FlatList
                        data={data?.posts}
                        renderItem={renderItem}
                        numColumns={3}
                        columnWrapperStyle={{gap: 16}}
                        contentContainerStyle={{gap: 16}}
                        showsVerticalScrollIndicator={false}
                        ListFooterComponent={() => (data?.posts?.length ?? 0) % 3 == 0 ? <View style={{width: 0}}/> : <View style={{width: 10}}/>}
                    />
                </View>
            </View>
            
        </AKSafeAreaView>
    )
}
export default Profile