import { View, Text, TextInput, Keyboard, Platform, Animated } from "react-native"
import { useTheme } from "@/theme";
import { ElementRef, useEffect, useRef, useState } from "react";
import { z } from "zod";
import { socialConnectPostSchema } from "@/types/schemas/socialConnectPost";
import { useLikeBookmarkToggleMutation } from "../hooks/useLikeBookmarkToggleMutation";
import getPostDetail from "@/services/socialConnect/getPostDetail";
import { useRoute, RouteProp, useIsFocused } from "@react-navigation/native";
import Spinner from "react-native-loading-spinner-overlay";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import _Icon from 'react-native-vector-icons/Feather';
import { getProfile } from "@/services/users";
import { ApplicationStackParamList } from "@/types/navigation";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import SocialConnectPost from "@/components/molecules/SocialConnectPost/SocialConnectPost";
import SocialConnectPostCommentsArea from "@/components/molecules/SocialConnectPostCommentsArea/SocialConnectPostCommentsArea";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import { colorTokens } from "@/theme/colorTokens";
import SocialConnectPostSettingsBottomSheet from "@/components/molecules/SocialConnectPostSettingsBottomSheet/SocialConnectPostSettingsBottomSheet";
import { usePremiumMemberPopup } from "@/hooks/usePremiumMemberPopup";
import { UserTypes } from "@/utils/constants";
import { KeyboardAwareFlatList, KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import SocialConnectPostComment from "@/components/molecules/SocialConnectPostComment/SocialConnectPostComment";
import { useCommentActions } from "@/hooks/usePostCommentsHooks";
import CommentInputContainer from "@/components/molecules/CommentInputContainer/CommentInputContainer";
import SelectedCommentOverlay from "@/components/molecules/SelectedCommentOverlay/SelectedCommentOverlay";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import NotFound from '@/theme/assets/images/NotFound.png'

import ReportPostBottomSheet from "@/components/molecules/ReportPostBottomSheet/ReportPostBottomSheet";
export type SocialConnectDetailNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'AddText',
    'AddMultimedia'
>;
function SocialConnectPostDetail() {
    const viewRef = useRef(null);
    const route = useRoute<RouteProp<ApplicationStackParamList, 'SocialConnectDetail'>>();
    const inputRef = useRef<ElementRef<typeof TextInput>>();

    const {
        layout,
        fonts,
        gutters
    } = useTheme();

    const { data: profile } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });

    const c = colorTokens();

    const showPremiumMemberPopup = usePremiumMemberPopup();

    const [isDeleting, setIsDeleting] = useState(false);
    const [selectedCommentItem, setSelectedCommentItem] = useState<z.infer<typeof socialConnectPostSchema> | null>(null)
    const [isBlurViewVisible, setIsBlurViewVisible] = useState(false);
    const { t } = useTranslation(['home']);
    const [boxYPosition, setBoxYPosition] = useState(0);

    const { onLikeButtonTapped, MUTATION_TYPE } = useLikeBookmarkToggleMutation(true);
    const {
        onSendPress,
        onEditComment,
        onDeleteComment,
        onPressCross,
        onPressComment,
        onReportComment,
        reportingContent,
        setReportingContent,
        isSending,
        commentText,
        setCommentText,
        replyingComment,
        editingComment,
        selectedComment,
        commentCoordinates,
        setReplyingComment
    } = useCommentActions(
        route.params.id,
        true,
        setIsBlurViewVisible
    );
    const insets = useSafeAreaInsets();
    const isFocused = useIsFocused();
    const animatedHeight = useState(new Animated.Value(0))[0];
    const [heightForBottomView, setHeightForBottomView] = useState(0)
    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const [isKeyboardVisible, setKeyboardVisible] = useState(false);
    const { data, isLoading: isPostLoading, isError, isFetching } = useQuery({
        queryKey: [`social-connect-post-${route?.params.id}`],
        queryFn: () => getPostDetail(route?.params.id)
    });
    useEffect(() => {
        setTimeout(() => {
            viewRef.current?.measureInWindow((_, y) => {
                setBoxYPosition(y);
            });
        }, 1000);
    }, []);
    useEffect(() => {
        if (isFocused == true) {
            const keyboardWillShowListener = Keyboard.addListener(
                Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
                (e) => {
                    setKeyboardVisible(true);
                    setHeightForBottomView(insets.bottom)
                    setKeyboardHeight(e.endCoordinates.height);
                    Animated.timing(animatedHeight, {
                    toValue: Platform.OS === 'android' ? 0 : e.endCoordinates.height - insets.bottom,
                    duration: 250,
                    useNativeDriver: false,
                    }).start();
                }
            );
            
            const keyboardWillHideListener = Keyboard.addListener(
            Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
            () => {
                setKeyboardVisible(false);
                setHeightForBottomView(0)
                Animated.timing(animatedHeight, {
                toValue: 0,
                duration: 250,
                useNativeDriver: false,
                }).start();
            }
            );
            
                // Clean up listeners
            return () => {
            keyboardWillShowListener.remove();
            keyboardWillHideListener.remove();
            };
        } else {
            
        }
        
    }, [isFocused]);

    function onLikeButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        if (profile?.user_profile.user_type == UserTypes.FREE) {
            showPremiumMemberPopup()
        } else {
            onLikeButtonTapped(tappedItem)
        }
    }

    function onBookmarkButton(tappedItem: z.infer<typeof socialConnectPostSchema>) {
        if (profile?.user_profile.user_type == UserTypes.FREE) {
            showPremiumMemberPopup()
        } else {
            onLikeButtonTapped(tappedItem, MUTATION_TYPE.BOOKMARK)
        }
    }

    function onCommentButtonTapped(tappedItem: z.infer<typeof socialConnectPostSchema> ): void {

    }

    function onPressPostSettings() {}

    function onPressReport(commentID: number, replyID?: number) {
        onReportComment(commentID, replyID, profile?.user_profile?.id);
        // setTimeout(() => {
        //     setReportingContent({
        //         postID: route?.params.id,
        //         commentID,
        //         replyID: replyID,
        //         userID: profile?.user_profile?.id,
        //     });
        // }, 750)
    }

    function renderItem({item}: any) {
        return (
            <View style={[gutters.marginHorizontal_16]} onStartShouldSetResponder={() => true}>
                <SocialConnectPostComment
                    comment={item}
                    setReplyingComment={setReplyingComment}
                    onDeleteComment={onDeleteComment}
                    onEditComment={onEditComment}
                    onPressComment={onPressComment}
                    onReportComment={onPressReport}
                    displayPopoverBackdrop={true}
                />
            </View>
        );
    }

    if (isError) {
        return (
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
                <Text style={[fonts.size_16, fonts.bold]}>No post found!</Text>
            </View>
        )
    }
    function headerComponent() {
        return (
            <SocialConnectPost
                item={data}
                isVisible={true}
                onCommentButtonTapped={onCommentButtonTapped}
                onLikeButton={onLikeButton}
                onBookmarkButton={onBookmarkButton}
                onPressPostSettings={onPressPostSettings}
            />
        )
    }
    return (
        <AKSafeAreaView>
            <Spinner
                visible={isPostLoading || isDeleting}
            />
            {
                !isPostLoading && (
                    <View ref={viewRef} style={[layout.flex_1]} onLayout={() => {}}>
                        <View style={[layout.flex_1]}>
                            {
                                isBlurViewVisible && (
                                    <View style={[layout.absolute, { top: 0, left: 0, right: 0, bottom: 0, zIndex: 5, backgroundColor: 'rgba(0,0,0,0.5)' }]} />
                                )
                            }
                            <SelectedCommentOverlay 
                                selectedComment={selectedComment}
                                commentCoordinates={commentCoordinates}
                                boxYPosition={boxYPosition}
                            />

                            <KeyboardAwareFlatList
                                showsVerticalScrollIndicator={false}
                                contentContainerStyle={[{flexGrow: 1, }]}
                                data={data?.comments ?? []}
                                keyExtractor={(item) => item.id.toString()}
                                renderItem={renderItem}
                                ListHeaderComponent={headerComponent()}
                                ListFooterComponent={() => <View style={{height: 100}}/>}
                                
                            />
                            <Animated.View style={[layout.flex_1,layout.absolute, layout.z10, {paddingBottom: insets.bottom ? 0 : 12, bottom: animatedHeight, left:0, right: 0, backgroundColor: c.background.default.neutrals.default}]} >
                                <CommentInputContainer
                                    inputRef={inputRef}
                                    commentText={commentText}
                                    setCommentText={setCommentText}
                                    onSendPress={onSendPress}
                                    onPressCross={onPressCross}
                                    isSending={isSending}
                                    replyingComment={replyingComment}
                                    editingComment={editingComment}
                                    profilePhotoUrl={profile?.user_profile?.profile_photo}
                                />
                                <Animated.View style={[{height: heightForBottomView}]}></Animated.View>
                            </Animated.View>
                        </View>
                    </View>
                )
            }
            <ReportPostBottomSheet
                isModalVisible={!!reportingContent}
                setReportingContent={setReportingContent}
                reportingContent={reportingContent}
                canGoBack
            />
            {data?.status == false && 
                <EmptyDataView
                    heading="Post not found"
                    desc="The post you are looking for does not exist or has been removed."
                    image={NotFound}
                    isFullScreen
                />
            }
        </AKSafeAreaView>
    )

}

export default SocialConnectPostDetail;
