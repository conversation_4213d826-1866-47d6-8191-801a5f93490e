import { getProfile } from "@/services/users";
import { useTheme } from "@/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ElementRef, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Animated, FlatList, Keyboard, KeyboardAvoidingView, TextInput, TouchableOpacity, View } from "react-native"
import { KeyboardAwareFlatList, KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Spinner from "react-native-loading-spinner-overlay";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { Pusher, PusherMember, PusherChannel, PusherEvent } from '@pusher/pusher-websocket-react-native'
import startConversation from "@/services/akinaAI/startConversation";
import { RouteProp, useIsFocused, useNavigation, useRoute } from "@react-navigation/native";
import { ApplicationStackParamList } from "@/types/navigation";
import conversationDetail from "@/services/akinaAI/conversationDetail";
import MessageComponent from "@/components/molecules/MessageComponent/MessageComponent";
import { z } from "zod";
import { conversationSchema, messageSchema } from "@/types/schemas/conversationSchema";
import { HeaderBackButton } from '@react-navigation/elements';


import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import React from "react";
import { useTranslation } from "react-i18next";
import { useCustomRoute } from "@/hooks/useCustomRoute";
import tempAuth from "@/services/users/tempAuth";
import { UserTypes } from "@/utils/constants";
import { colorTokens } from "@/theme/colorTokens";
export type ChatNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'ChatListing'
>;
let currentUniqueId: number | null = null
function Chat() {
    const { t } = useTranslation(['home']);
    const queryClient = useQueryClient()
    const pusher = Pusher.getInstance();
    const route = useCustomRoute(); // useRoute<RouteProp<ApplicationStackParamList, 'Chat'>>();
    const Icon = _Icon as React.ElementType
    const inputRef = useRef<ElementRef<typeof TextInput>>()
    const [currentConversationId, setCurrentConversationId] = useState(route.params.item?.id)
    const profileData = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const conversationData = useQuery({
        queryKey: [`Conversation${currentConversationId}`],
        queryFn: () =>  conversationDetail(route.params?.item?.moduleId, route.params.item?.id) 
    })
    const {
		layout,
		gutters,
		backgrounds,
        colors
	} = useTheme();
    const c = colorTokens();
    const [isLoading, setIsLoading] = useState(false)
    const [messageText, setMessageText] = useState('')
    const isFocused = useIsFocused();
    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const navigation = useNavigation<ChatNavigationProp>()
    const [currentLimit, setCurrentLimit] = useState(route.params.item?.limit)
    useEffect(() => {
        function onKeyboardWillShow(e: any) { // Remove type here if not using TypeScript
            setKeyboardHeight(e.endCoordinates.height);
        }
        function onKeyboardWillHide() {
            setKeyboardHeight(0);
        }
        const showSubscription = Keyboard.addListener('keyboardWillShow', onKeyboardWillShow);
        const hideSubscription = Keyboard.addListener('keyboardWillHide', onKeyboardWillHide);
        if (route.params.item?.id == null) {
            navigation.setOptions({
                headerBackTitleVisible: false,
                headerLeft: () => <HeaderBackButton tintColor={colors.gray800}  labelVisible={false} onPress={() => navigation.goBack('ChatListing')} />
            })
        }
        
        return () => {
            showSubscription.remove();
            hideSubscription.remove();
        };
        
    }, []);
    useEffect(() => {
        setupPusher()
    },[])
    useEffect(() => {
        if (!isFocused) {
          pusher.unsubscribe({channelName: `conversations-${currentConversationId}`})
        }
    }, [isFocused]);
    const tempAuthMutation = useMutation({
        mutationFn: () => tempAuth(),
        onSuccess: (response) => {
            navigation.navigate('AKWebView', {source: `${response.web_url}?paymentKey=${response.key}`} )
        },
        onError: (error) => {
            Alert.alert("Error!", error.message)
        }
    })
    function showPremiumMemberPopup() {
        let options = [{text: t("home:Upgrade"), onPress: async () => {
            tempAuthMutation.mutate()
        }}, {text: t("home:Cancel"), onPress: async () => {
            
        }}]
        Alert.alert(t("home:PremiumUser"), t("home:LimitOverDesc"), options)
    }
    async function subscribeToChannel(conversationId: number) {
        await pusher.subscribe({
            channelName: `conversations-${conversationId}`, // channel name
            onEvent: (event: PusherEvent) => {
                setIsLoading(false)
                updateData(JSON.parse(event.data).message, conversationId)
            },
        });
        await pusher.connect();
    }
    function updateData(text: string, conversationId: number) {
        queryClient.setQueryData([`Conversation${conversationId}`], (cacheData:  z.infer<typeof conversationSchema>) => {
            if (cacheData?.messages != null) {
                let currentMessage = cacheData.messages.filter((message) => message.id == currentUniqueId)
                if (currentMessage.length == 0) {
                    return {
                        ...cacheData,
                        messages: [
                            ...cacheData.messages,
                            {
                                id: currentUniqueId,
                                content: text == '' ? ' ' : text,
                                conversation_id: conversationId,
                                sender_type: 'ai',
                                user_id: profileData.data?.user_profile.id,
                                created_at: new Date(),
                                updated_at: new Date()
                            }
                        ]
                    }
                } else {
                    let messageToUpdate = currentMessage[0]
                    return {
                        ...cacheData,
                        messages: cacheData.messages.map(message => {
                            if (message.id == messageToUpdate.id) {
                                return {
                                    ...message,
                                    content: message.content + text
                                }
                            } else {
                                return message
                            }
                        })
                    }
                } 
            }  else {
                return {
                    id: conversationId,
                    user_id: profileData.data?.user_profile.id,
                    title: null,
                    created_at: new Date(),
                    updated_at: new Date(),
                    messages: [
                        {
                            id: currentUniqueId,
                            content: text == '' ? ' ' : text,
                            conversation_id: conversationId,
                            sender_type: 'ai',
                            user_id: profileData.data?.user_profile.id,
                            created_at: new Date(),
                            updated_at: new Date()
                        }
                    ]
                }
            }                
        })
    }
            
    
    async function setupPusher() {
        await pusher.init({
            apiKey: `${process.env.PUSHER_API_KEY}`, //PUSHER_API_KEY, // api key
            cluster: `${process.env.PUSHER_CLUSTER}` // cluster name
        });
        if (currentConversationId != null) {
            await pusher.subscribe({
                channelName: `conversations-${currentConversationId}`, // channel name
                onEvent: (event: PusherEvent) => {
                    setIsLoading(false)
                    updateData(JSON.parse(event.data).message, currentConversationId)
                },
            });
            await pusher.connect();
        } 
    }

    function addNewMessage(message: z.infer<typeof messageSchema>) {
        queryClient.setQueryData([`Conversation${message.conversation_id}`], (cacheData:  z.infer<typeof conversationSchema>) => {
            if (cacheData?.messages != null) {
                return {
                    ...cacheData,
                    messages: [
                        ...cacheData.messages,
                        message
                    ]
                }
            } else {
                return {
                    id: message.conversation_id,
                    user_id: profileData.data?.user_profile.id,
                    title: null,
                    created_at: new Date(),
                    updated_at: new Date(),
                    messages: [
                        message
                    ]
                }
            }
            
        })
    }

    const startConversationMutation = useMutation(
        {
            mutationFn: (data: any) => startConversation(data.module_id, data.message_data),
            onSuccess: (response) => {
                currentUniqueId = response.message.id + 1
                if (currentConversationId== null) {
                    subscribeToChannel(response.conversation_id)
                }
                setCurrentConversationId(response.conversation_id)
                addNewMessage(response.message)
                setMessageText('')
                setCurrentLimit(response.request_limit)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        }
    );
    function sendMessage() {
        let data: any = {
            module_id: route.params.item?.moduleId,
            message_data: {
                message: messageText
            }
        }
        if (currentConversationId != null) {
            data.message_data.conversation_id = currentConversationId
        }
        setIsLoading(true)
        startConversationMutation.mutate(data)
    }

    function onMessageSend() {
        if (profileData.data?.user_profile.user_type == UserTypes.FREE ) {
            if (currentLimit == 0) {
                showPremiumMemberPopup()
            } else {
                sendMessage()
            }
        } else {
            sendMessage()
        }
        
    }
    function renderItem({item}: any) {
        if (profileData.data != null) {
            return (
                <MessageComponent item={item} user={profileData.data?.user_profile}/>
            )
        } else {
            return <></>
        }
        
    }
    function reversedArray(array?: Array<z.infer<typeof messageSchema>> | null) {
        if (array == null ) {
            return null
        }
        let currentArray = [...array]
        return currentArray.reverse()
    }
    
    return (
        <KeyboardAvoidingView style={[layout.flex_1, layout.justifyEnd, backgrounds.white]}>
            <Spinner
                visible={(currentConversationId && conversationData.isFetching) || isLoading || profileData.isRefetching}
            />
            <KeyboardAwareFlatList 
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{ paddingBottom: 20}} 
                data={reversedArray(conversationData.data?.messages)}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
                inverted
            />
            <Animated.View style={[layout.row , gutters.marginHorizontal_0,layout.justifyBetween ,layout.itemsCenter ,gutters.marginTop_12,{height: 80, marginBottom: 16+ keyboardHeight}]}>
                <TextInput 
                    ref={inputRef}
                    placeholder={t("home:WriteQuestionPlaceholder")}
                    style={[gutters.marginHorizontal_12, layout.flex_1 , {height: 48, color: colors.white ,padding: 15, borderRadius: 20,  backgroundColor: c.content.default.default}]}
                    placeholderTextColor={colors.gray200}
                    value={messageText}
                    onChangeText={(text) => setMessageText(text)}
                    
                />
                <TouchableOpacity onPress={onMessageSend} disabled={messageText == ''}>
                    <View style={ [ layout.itemsCenter, layout.justifyCenter, gutters.marginRight_12 ,{width: 48, height: 48, borderRadius: 24, backgroundColor: messageText == '' ? colors.gray200 : c.content.default.default}]}>
                        <Icon style={[gutters.marginRight_4]} name='send' size={25} color={colors.gray50}/>
                    </View>
                </TouchableOpacity>
            </Animated.View>
            
        </KeyboardAvoidingView>
    )
}
export default Chat