import React, { useCallback, useMemo, useState } from 'react';
import { View, SectionList, RefreshControl, Text, Platform } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { useTheme } from '@/theme';
import { colorTokens } from '@/theme/colorTokens';
import { conversationSchema } from '@/types/schemas/conversationSchema';
import { getAIChatHistorySectionData } from '@/utils/utility';
import getConversations from '@/services/akinaAI/getConversations';
import ChatEmpty from '@/theme/assets/images/Home/ChatEmpty.png';
import Search from '@/theme/assets/images/Search.png';
import SearchCross from '@/theme/assets/images/SearchCross.png';
import { SearchBar } from "@/components/molecules/SearchBar/SearchBar";
import { ConversationItem } from "@/components/molecules/ConversationItem/ConversationItem";
import EmptyDataView from '@/components/molecules/EmptyDataView/EmptyDataView';

type Props = {
    moduleId: number;
    itemPressed?: (item: z.infer<typeof conversationSchema>) => void;
};

function ChatHistory(props: Props) {
    const { layout, fonts, gutters, colors } = useTheme();
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const c = colorTokens();

    const conversationListingResponse = useQuery({
        queryKey: [`conversations`],
        queryFn: () => getConversations(props.moduleId)
    });

    const filteredConversations = useMemo(() => {
        if (!searchQuery.trim()) {
        return conversationListingResponse.data?.conversations || [];
        }
        
        return (conversationListingResponse.data?.conversations || []).filter(conversation => 
        conversation.title?.toLowerCase().includes(searchQuery.toLowerCase())
        );
    }, [conversationListingResponse.data, searchQuery]);

    const flattenSectionData = useMemo(() => {
        return getAIChatHistorySectionData(filteredConversations);
    }, [filteredConversations, conversationListingResponse.isRefetching]);

    const onRefresh = useCallback(() => {
        if (!isRefreshing) {
        setIsRefreshing(true);
        conversationListingResponse.refetch()
            .then(() => setIsRefreshing(false))
            .catch(() => setIsRefreshing(false));
        }
    }, [isRefreshing, conversationListingResponse.refetch]);

    const handleItemPress = useCallback((item: z.infer<typeof conversationSchema>) => {
        props.itemPressed?.(item);
    }, [props.itemPressed]);

    const renderItem = ({ item }: any) => (
        <ConversationItem
        item={item}
        onPress={handleItemPress}
        searchQuery={searchQuery}
        titleStyle={{}}
        timeStyle={[fonts.gray400, {}]}
        highlightStyle={[fonts.fontSizes.body.sm]}
        />
    );


    const ListEmptyComponent = () => {
        const isSearching = searchQuery.trim().length > 0;
        const heading = isSearching ? "No Results Found" : "No Chats Yet";
        const desc = isSearching 
            ? `No chats found matching "${searchQuery}". Try a different search term.`
            : "Your chats will appear here once you get the conversation going.";

        return (
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
                <EmptyDataView
                    heading={heading}
                    desc={desc}
                    image={ChatEmpty}
                    isFullScreen={false}
                />
            </View>
        )
    }

    return (
        <View style={[layout.flex_1]}>
            {/* Search Bar */}
            {
                (!!flattenSectionData?.length || !!searchQuery) && (
                    <View style={[gutters.marginHorizontal_16, gutters.marginTop_12, gutters.marginBottom_8]}>
                        <SearchBar
                            value={searchQuery}
                            onChangeText={setSearchQuery}
                            placeholder="Search here"
                            placeholderTextColor={c.content.default.subdued}
                            searchIcon={Search}
                            clearIcon={SearchCross}
                            containerStyle={[
                                layout.row,
                                layout.itemsCenter,
                                {
                                    backgroundColor: c.background.default.neutrals.secondary,
                                    borderColor: c.stoke.default.subdued,
                                    borderWidth: 1,
                                    borderRadius: 18,
                                    paddingVertical: Platform.OS === 'ios' ? 7 : 3
                                }
                            ]}
                            inputStyle={[
                                fonts.fontSizes.body.sm,
                                { color: c.content.default.default }
                            ]}
                        />
                    </View>
                )
            }

            <View style={[gutters.marginHorizontal_0, layout.flex_1]}>
                <SectionList 
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={[gutters.marginHorizontal_12, { flexGrow: 1 }]} 
                sections={flattenSectionData}
                keyExtractor={(_, index) => index.toString()}
                renderItem={renderItem}
                renderSectionHeader={({ section: { title } }) => (
                        <View style={[gutters.marginBottom_4, gutters.marginTop_4]}>
                            <Text style={[fonts.fontSizes.body.xs, fonts.lineHeight.body.xs, {fontWeight: '600', color: c.content.default.subdued}]}>{title}</Text>
                        </View>
                    )}
                renderSectionFooter={({ section: { title } }) => (
                    <View style={[gutters.marginVertical_20 ,{height: 1, backgroundColor: c.stoke.default.subdued},]}/>
                )}
                refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                removeClippedSubviews={true}
                ListEmptyComponent={ListEmptyComponent}
                stickySectionHeadersEnabled={false}
                />
            </View>
        </View>
    );
}

export default ChatHistory;
