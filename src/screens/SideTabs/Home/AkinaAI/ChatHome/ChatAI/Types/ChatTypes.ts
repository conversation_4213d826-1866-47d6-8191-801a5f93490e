import { z } from "zod";
import { conversationSchema, messageSchema } from "@/types/schemas/conversationSchema";

export type MessageType = 'U' | 'A';

export type PayLoadData = {
    user_id: string;
    conversation_id: string;
    user_query: string;
    message_id?: string;
};

export type MessageSection = {
    title: string;
    data: z.infer<typeof messageSchema>[];
};

export type ChatAIProps = {
    moduleId: number;
    conversation?: z.infer<typeof conversationSchema> | null;
    conversationId?: number;
    onNewChat?: () => void
};