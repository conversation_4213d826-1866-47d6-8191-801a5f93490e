import React, { useState, useEffect } from 'react';
import { KeyboardAvoidingView, Platform, SectionList, Text, View } from 'react-native';
import { KeyboardAwareSectionList } from 'react-native-keyboard-aware-scroll-view';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useTheme } from "@/theme";
import basicProfile from "@/services/users/basicProfile";
import getDefaultQuestions from "@/services/akinaAI/getDefaultQuestions";
import conversationDetail from "@/services/akinaAI/conversationDetail";
import askQuery from "@/services/akinaAI/askQuery";
import likeDislikeMessage from "@/services/akinaAI/likeDislikeMessage";
import MessageComponent from "@/components/molecules/MessageComponent/MessageComponent";
import { conversationSchema, messageSchema } from "@/types/schemas/conversationSchema";
import { aiQuestion } from "@/types/schemas/aiQuestion";
import { z } from "zod";
import Clipboard from '@react-native-clipboard/clipboard';
// import Loading from '@/theme/assets/images/'
// Import our new hooks and components
import { useKeyboardHandler } from './hooks/useKeyboardHandler';
import { useMessageManager } from './hooks/useMessageManager';
import { useConversationId } from './hooks/useConversationId';
import { groupMessagesByDate } from './utils/dateUtils';
import { EmptyChatView } from '@/components/molecules/EmptyChatView/EmptyChatView';
import { ChatInput } from '@/components/molecules/ChatInput/ChatInput';
import { AISectionHeader } from '@/components/molecules/AISectionHeader/AISectionHeader';
import { ChatAIProps } from './Types/ChatTypes';
import {MESSAGE_TYPE} from './Constants/ChatConstants'
import { useCustomToast } from '@/hooks/useCustomToast';
import { AKButton, ImageVariant } from '@/components/atoms';
import { colorTokens } from '@/theme/colorTokens';

const ChatAI: React.FC<ChatAIProps> = ({ moduleId, conversation, conversationId, onNewChat }) => {
    const { layout, gutters, fonts } = useTheme();
    const [messageText, setMessageText] = useState('');
    const [isLoading, setIsLoading] = useState(false)
    const showToast = useCustomToast();
    const [isStreamingDone, setIsStreamingDone] = useState(true)
    // Custom hooks
    const { keyboardHeight, animatedHeight } = useKeyboardHandler();
    const c = colorTokens();
    
    const profileResponse = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile,
    });

    const { currentConversationId, setCurrentConversationId } = useConversationId(
        conversation?.external_conversation_id,
        profileResponse.data?.user.id
    );

    const {
        lastMessageId,
        setLastMessageId,
        generateMessageId,
        addNewMessage,
        updateMessage,
        updateStreamingMessage
    } = useMessageManager(currentConversationId, profileResponse.data?.user.id);

    // Queries
    const { data: questionsData } = useQuery({
        queryKey: [`default-ai-questions`],
        queryFn: () => getDefaultQuestions(moduleId)
    });

    const conversationData = useQuery({
        queryKey: [`Conversation${currentConversationId}`],
        queryFn: () => {
            if (conversationId == null) {
                return conversationSchema.parse({
                    id: -1,
                    user_id: profileResponse.data?.user.id,
                    external_conversation_id: currentConversationId,
                    title: null,
                    created_at: new Date(),
                    updated_at: new Date(),
                    messages: [],
                    allow_interaction: 1
                });
            } else {
                return conversationDetail(moduleId, conversationId);
            }
        },
        enabled: conversationId != null
    });

    // Mutations
    const likeDislikeMutation = useMutation({
        mutationFn: (data: any) => likeDislikeMessage(moduleId, data.external_message_id, data.payload),
        onSuccess: (response) => {
            if (response.success) {
                updateMessage(response.data);
            }
        }
    });

    const askQueryMutation = useMutation({
        mutationFn: (data: any) => askQuery(data.payload, {
            onToken: (token) => {},
            onTextChunk: (text) => {
                setIsLoading(false)
                if (currentConversationId) {
                    updateStreamingMessage(text, data.answerMessageId);
                }
            },
            onComplete: (fullText) => {
                setIsLoading(false)
                setIsStreamingDone(true)
            },
            onError: (error) => {
                setIsLoading(false)
                console.log(error)
                setIsStreamingDone(true)

            } 
        })
    });

    // Event handlers
    const handleQuestionPress = (question: z.infer<typeof aiQuestion>) => {
        const userId = profileResponse.data?.user.id;
        if (!userId) return;

        const messageId = generateMessageId(MESSAGE_TYPE.USER_QUERY);
        if (!messageId) return;

        setLastMessageId(messageId);
        
        const payload = {
            user_id: userId.toString(),
            conversation_id: currentConversationId!,
            user_query: question.message,
            message_id: messageId
        };
        setIsStreamingDone(false)
        sendQueryToServer(payload);
        
        const message = {
            id: -1,
            conversation_id: -1,
            user_id: userId,
            external_message_id: messageId,
            content: question.message,
            created_at: new Date().toString(),
            updated_at: new Date().toString(),
            sender_type: 'user'
        };
        
        addNewMessage(message);
    };

    const handleSendMessage = () => {
        const userId = profileResponse.data?.user.id;
        if (!userId || !messageText.trim()) return;

        const messageId = generateMessageId(MESSAGE_TYPE.USER_QUERY);
        if (!messageId) return;

        setLastMessageId(messageId);
        
        const payload = {
            user_id: userId.toString(),
            conversation_id: currentConversationId!,
            user_query: messageText,
            message_id: messageId
        };
        setIsStreamingDone(false)
        sendQueryToServer(payload);
        
        const message = {
            id: -1,
            conversation_id: -1,
            user_id: userId,
            external_message_id: messageId,
            content: messageText,
            created_at: new Date().toString(),
            updated_at: new Date().toString(),
            sender_type: 'user'
        };
        
        addNewMessage(message);
        setMessageText('');
    };

    const sendQueryToServer = (payload: any) => {
        const messageId = generateMessageId(MESSAGE_TYPE.ANSWER);
        if (!messageId) return;

        setLastMessageId(messageId);
        
        const data = {
            payload: payload,
            externalMessageId: payload.message_id,
            answerMessageId: messageId
        };
        setIsLoading(true)
        askQueryMutation.mutate(data);
    };

    const handleLike = (item: z.infer<typeof messageSchema>) => {
        const data = {
            payload: { like_status: 1 },
            external_message_id: item.external_message_id
        };
        likeDislikeMutation.mutate(data);
    };

    const handleDislike = (item: z.infer<typeof messageSchema>) => {
        const data = {
            payload: { like_status: 2 },
            external_message_id: item.external_message_id
        };
        likeDislikeMutation.mutate(data);
    };

    const handleCopy = (item: z.infer<typeof messageSchema>) => {
        // Implement copy functionality
        Clipboard.setString(item.content);
        showToast("Content Copied!")
    };

    const renderItem = ({ item }: any) => {
        if (!profileResponse.data) return null;
        
        return (
            <MessageComponent 
                onCopy={handleCopy}
                onThumbsUp={handleLike}
                onThumbsDown={handleDislike}
                item={item}
                user={profileResponse.data.user}
            />
        );
    };

    // Effects
    useEffect(() => {
        if (conversationData.data?.messages?.length) {
            const count = conversationData.data.messages.length;
            const lastMessage = conversationData.data.messages[count - 1];
            setLastMessageId(lastMessage.external_message_id ?? null);
        }
    }, [conversationData.data]);

    useEffect(() => {
        console.log("Conversation ID", conversationId)
        if (conversation?.external_conversation_id) {
            setCurrentConversationId(conversation.external_conversation_id);
        } else {
            setCurrentConversationId(null);

        }
        conversationData.refetch();
    }, [conversationId]);

    function sectionFooter({section}) {
        return (
            <AISectionHeader section={section}/>
        )
    }

    const sectionsData = groupMessagesByDate(conversationData.data?.messages);
    const hasMessages = conversationData.data?.messages?.length > 0;
    const userName = profileResponse.data?.user.first_name || '';
    function newChatButtonTapped() {
        if (onNewChat != null) {
            onNewChat()
        }
    }
    return (
        <View style={[layout.flex_1]}>
            <SectionList 
                showsVerticalScrollIndicator={false} 
                style={[{ marginBottom: 0 }]}
                sections={sectionsData}
                keyExtractor={(item, index) => `${item.external_message_id || item.id}-${index}`}
                renderItem={renderItem}
                renderSectionFooter={sectionFooter}
                inverted
                keyboardShouldPersistTaps="always"
                stickySectionHeadersEnabled={false}
                pointerEvents='auto'
                keyboardDismissMode='on-drag'
            />
            
            {!hasMessages && (
                <EmptyChatView
                    userName={userName}
                    questions={questionsData?.questions}
                    keyboardHeight={keyboardHeight}
                    onQuestionPress={handleQuestionPress}
                />
            )}
            {isLoading && <ImageVariant style={[layout.absolute, {bottom: 138+ keyboardHeight, width: 50, height: 30}]} source={require('@/theme/assets/images/Loading.gif')}/>}
            {(conversationData.data?.allow_interaction == undefined || conversationData.data?.allow_interaction === 1) ?
            
            (<ChatInput
                messageText={messageText}
                onChangeText={setMessageText}
                onSend={handleSendMessage}
                animatedHeight={animatedHeight}
                isStreamingDone={isStreamingDone}
            />)
            :

            
            (<View style={[gutters.marginHorizontal_16,{ 
                height: 150, 
                borderRadius: 20, 
                borderTopRightRadius: 20, 
                marginBottom: 20,
                backgroundColor: c.background.default.neutrals.default, 
                shadowRadius: 4, 
                shadowOpacity: 0.10, 
                elevation: 10, 
                shadowColor: '#000000', 
                shadowOffset: { width: 0, height: 0 }
            }]}>
                <View style={[gutters.marginHorizontal_16]}>
                    <Text style={[
                        gutters.marginTop_12,
                        fonts.fontSizes.headings.H5, 
                        fonts.lineHeight.headings.H5, 
                        { fontWeight: '700', color: c.content.default.emphasis }
                    ]}>This Chat is read-only</Text>
                    <Text style={[
                        gutters.marginTop_4,
                        fonts.fontSizes.body.sm, 
                        fonts.lineHeight.body.sm, 
                        { fontWeight: '400', color: c.content.default.subdued }
                        ]}>You can view this conversation, but cannot continue it. Start a new chat to ask questions.</Text>
                    <AKButton 
                        onPress={newChatButtonTapped}
                        title='New Chat' 
                        borderRadius={8} 
                        viewStyle={[
                            gutters.marginTop_8,
                            gutters.marginBottom_8
                        ]} 
                        textStyle={
                            [fonts.fontSizes.utility.sm, 
                            fonts.lineHeight.utility.sm, { 
                                fontWeight: '600', color: c.content.onBold.default.default 
                            }]
                        }
                    />
                </View>
                
            </View>)
            }
        </View>
    );
};

export default ChatAI;