import { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import uuid from 'react-native-uuid';
import { z } from 'zod';
import { conversationSchema, messageSchema } from '@/types/schemas/conversationSchema';
import { MESSAGE_TYPE } from '../Constants/ChatConstants';
import {MessageType} from '../Types/ChatTypes'
export const useMessageManager = (
    currentConversationId: string | undefined | null,
    userId: number | undefined | null
) => {
    const [lastMessageId, setLastMessageId] = useState<string | null>(null);
    const queryClient = useQueryClient();

    const generateMessageId = (type: MessageType): string | null => {
        if (!userId || !currentConversationId) return null;

        if (!lastMessageId) {
            return `${currentConversationId}-1${type}`;
        }

        const index = getIndexFromMessageId(lastMessageId);
        if (index != null) {
            const lastMessageType = lastMessageId.slice(-1) as MessageType;
            const increment = lastMessageType === MESSAGE_TYPE.USER_QUERY ? 0 : 1;
            return `${currentConversationId}-${index + increment}${type}`;
        }

        return `${currentConversationId}-1${type}`;
    };

    const getIndexFromMessageId = (messageId: string): number | null => {
        const match = messageId.match(/-(\d+)[UA]$/);
        return match ? parseInt(match[1], 10) : null;
    };

    const addNewMessage = (message: z.infer<typeof messageSchema>) => {
        queryClient.setQueryData([`Conversation${currentConversationId}`], 
            (cacheData: z.infer<typeof conversationSchema>) => {
                if (cacheData?.messages != null) {
                    return {
                        ...cacheData,
                        messages: [...cacheData.messages, message]
                    };
                } else {
                    return {
                        id: message.conversation_id,
                        user_id: userId,
                        external_conversation_id: currentConversationId,
                        title: null,
                        created_at: new Date(),
                        updated_at: new Date(),
                        messages: [message]
                    };
                }
            }
        );
    };

    const updateMessage = (item: z.infer<typeof messageSchema>) => {
        queryClient.setQueryData([`Conversation${currentConversationId}`], 
            (cacheData: z.infer<typeof conversationSchema>) => {
                if (!cacheData?.messages) return cacheData;

                return {
                    ...cacheData,
                    messages: cacheData.messages.map(message => 
                        message.external_message_id === item.external_message_id ? item : message
                    )
                };
            }
        );
    };

    const updateStreamingMessage = (text: string, externalMessageId: string) => {
        queryClient.setQueryData([`Conversation${currentConversationId}`], 
            (cacheData: z.infer<typeof conversationSchema>) => {
                if (!cacheData?.messages) {
                    return createNewConversationWithMessage(text, externalMessageId);
                }

                const existingMessage = cacheData.messages.find(
                    message => message.external_message_id === externalMessageId
                );

                if (!existingMessage) {
                    return {
                        ...cacheData,
                        messages: [
                            ...cacheData.messages,
                            createAIMessage(text, externalMessageId)
                        ]
                    };
                } else {
                    return {
                        ...cacheData,
                        messages: cacheData.messages.map(message => 
                            message.external_message_id === externalMessageId
                                ? { ...message, content: message.content + text }
                                : message
                        )
                    };
                }
            }
        );
    };

    const createAIMessage = (text: string, externalMessageId: string): z.infer<typeof messageSchema> => ({
        id: -1,
        external_message_id: externalMessageId,
        content: text === '' ? ' ' : text,
        conversation_id: currentConversationId || '',
        sender_type: 'ai',
        user_id: userId,
        created_at: new Date().toString(),
        updated_at: new Date().toString()
    });

    const createNewConversationWithMessage = (text: string, externalMessageId: string) => ({
        id: currentConversationId,
        user_id: userId,
        external_conversation_id: currentConversationId,
        title: null,
        created_at: new Date(),
        updated_at: new Date(),
        messages: [createAIMessage(text, externalMessageId)]
    });

    return {
        lastMessageId,
        setLastMessageId,
        generateMessageId,
        addNewMessage,
        updateMessage,
        updateStreamingMessage
    };
};
