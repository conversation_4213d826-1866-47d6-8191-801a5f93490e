import { useState, useEffect } from 'react';
import uuid from 'react-native-uuid';

export const useConversationId = (
    initialConversationId?: string,
    userId?: string
) => {
    const [currentConversationId, setCurrentConversationId] = useState<string | undefined>(
        initialConversationId
    );

    useEffect(() => {
        if (!currentConversationId && userId) {
            const newId = `${userId}-${uuid.v4().replace(/-/g, '')}`;
            setCurrentConversationId(newId);
        }
    }, [userId, currentConversationId]);

    return { currentConversationId, setCurrentConversationId };
};