import { useEffect, useState } from 'react';
import { Animated, Keyboard } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CHAT_CONFIG } from '../Constants/ChatConstants';

export const useKeyboardHandler = () => {
    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const insets = useSafeAreaInsets();
    const animatedHeight = useState(new Animated.Value(0))[0];

    useEffect(() => {
        const onKeyboardWillShow = (e: any) => {
            setKeyboardHeight(e.endCoordinates.height);
            Animated.timing(animatedHeight, {
                toValue: e.endCoordinates.height - insets.bottom,
                duration: CHAT_CONFIG.KEYBOARD_ANIMATION_DURATION,
                useNativeDriver: false,
            }).start();
        };

        const onKeyboardWillHide = () => {
            setKeyboardHeight(0);
            Animated.timing(animatedHeight, {
                toValue: 0,
                duration: CHAT_CONFIG.KEYBOARD_ANIMATION_DURATION,
                useNativeDriver: false,
            }).start();
        };

        const showSubscription = Keyboard.addListener('keyboardWillShow', onKeyboardWillShow);
        const hideSubscription = Keyboard.addListener('keyboardWillHide', onKeyboardWillHide);

        return () => {
            showSubscription.remove();
            hideSubscription.remove();
        };
    }, [animatedHeight, insets.bottom]);

    return { keyboardHeight, animatedHeight };
};