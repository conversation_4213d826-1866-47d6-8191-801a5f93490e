import { messageSchema } from "@/types/schemas/conversationSchema";
import { z } from "zod";
import { MessageSection } from "../Types/ChatTypes";

export const formatDateForSection = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());

    if (messageDate.getTime() === todayDate.getTime()) {
        return 'Today';
    } else if (messageDate.getTime() === yesterdayDate.getTime()) {
        return 'Yesterday';
    } else {
        return date.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    }
};

export const groupMessagesByDate = (
    messages?: Array<z.infer<typeof messageSchema>> | null
): MessageSection[] => {
    if (!messages || messages.length === 0) {
        return [];
    }

    const grouped = messages.reduce((acc, message) => {
        const dateKey = formatDateForSection(message.created_at);
        
        if (!acc[dateKey]) {
            acc[dateKey] = [];
        }
        acc[dateKey].push(message);
        
        return acc;
    }, {} as Record<string, z.infer<typeof messageSchema>[]>);

    return Object.entries(grouped).map(([title, data]) => ({
        title,
        data: data.reverse()
    })).reverse();
};