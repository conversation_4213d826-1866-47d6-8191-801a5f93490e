import { useTheme } from "@/theme";
import { Text, View } from "react-native"
import LinearGradient from "react-native-linear-gradient"
import AkinaBGLogo from '@/theme/assets/images/AkinaBGLogo.png'
import { AKButton, ImageVariant } from "@/components/atoms";
import { useTranslation } from "react-i18next";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ApplicationStackParamList } from "@/types/navigation";
import { colorTokens } from "@/theme/colorTokens";

export type AkinaAINavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList
    
>;
function AkinaAI() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'AkinaAI'>>();
    const { t } = useTranslation(['sideTabs']);
    const navigation = useNavigation<AkinaAINavigationProp>()
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();

    const c = colorTokens();

    function continueButtonTapped() {
        navigation.goBack()
        // navigation.navigate('Chat', {item: {
        //     moduleId: route.params.moduleId,            
        // }})
        // navigation.navigate('ChatHome', 
        //     {moduleId: route.params.moduleId}            
        // )
    }
    return (
        <View style={[layout.flex_1, layout.justifyEnd]}>
            <ImageVariant
                source={AkinaBGLogo}
                tintColor={c.content.primary.default}
                style={[layout.absolute ,{width: 48, height: 64, top: 100, left: 16}]}     
            />
            <LinearGradient
                start={{ x: 0, y: 0 }}         // Start from left
                end={{ x: 1, y: 0 }}   
                style={[layout.absolute ,{top: 0, left: 0, right: 0, bottom: 0}]} 
                colors={['#FFFFFF',c.background.default.primary.default]}  >
            </LinearGradient>
            <View style={[gutters.marginHorizontal_16]}>
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.SemiBold, {color: c.content.primary.default}]}>Ask Akina</Text>
            </View>
            <View style={[gutters.marginHorizontal_16, gutters.marginTop_8]}>
                <Text style={[fonts.fontSizes.headings.H1, fonts.lineHeight.headings.H1, fonts.SemiBold, {color: c.content.default.emphasis}]}>A new kind of simpler chat experience</Text>
            </View>
            <View style={[ gutters.marginHorizontal_16,gutters.marginVertical_24,{height: 60}]}>
                <AKButton 
                    height={48}
                    title={"Let's go"}
                    backgroundColor={c.content.default.emphasis}
                    onPress={continueButtonTapped}
                    borderRadius={10}
                />
            </View>
            <ImageVariant
                source={AkinaBGLogo}
                tintColor={c.content.primary.default}
                style={[layout.absolute ,{width: 48, height: 64, top: 100, left: 16}]}     
            />
        </View>
    )

}
export default AkinaAI