import getConversations from "@/services/akinaAI/getConversations";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { conversationSchema } from "@/types/schemas/conversationSchema";
import { RouteProp, useRoute, useNavigation, useIsFocused } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Alert, FlatList, RefreshControl, Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image";
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
import Markdown from 'react-native-markdown-display';
import { timeSince } from "@/utils";
import AddIcon from '@/theme/assets/images/Home/AddIcon.png'
import { AKFastImage, ImageVariant } from "@/components/atoms";
import { UserTypes } from "@/utils/constants";
import { getProfile } from "@/services/users";
import tempAuth from "@/services/users/tempAuth";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";

export type ConversationsNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'AkinaAI'
>;
function ChatListing() {
    const isFocused = useIsFocused();
    const profileResponse = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });
    const route = useRoute<RouteProp<ApplicationStackParamList, 'ChatListing'>>();
    const { t } = useTranslation(['home']);
    const navigation = useNavigation<ConversationsNavigationProp>()
    const {
		layout,
		gutters,
        fonts,
        borders,
        colors
	} = useTheme();
    const {data, isLoading, refetch } = useQuery({
        queryKey: [`conversations`],
        queryFn: () =>  getConversations(route.params.moduleId)
    });
    useEffect(() => {
        if (isFocused == true) {
            refetch()
        }
    },[isFocused])
    useEffect(() => {
        if (isLoading == false && data?.conversations.length == 0) {

            navigation.navigate('AkinaAI', {moduleId: route.params.moduleId})
        }
    },[isLoading, data])
    function itemPressed(item: z.infer<typeof conversationSchema>) {
        navigation.navigate('Chat', {
            item: {
                moduleId: route.params.moduleId,
                id: item?.id,
                limit: data?.request_limit ?? 0
            }
        })
    }
    const tempAuthMutation = useMutation({
        mutationFn: () => tempAuth(),
        onSuccess: (response) => {
            // setIsLoading(false)
            navigation.navigate('AKWebView', {source: `${response.web_url}?paymentKey=${response.key}`})
        },
        onError: (error) => {
            // setIsLoading(false)
            Alert.alert("Error!", error.message)
        }
    })
    function showPremiumMemberPopup() {
        let options = [{text: t("home:Upgrade"), onPress: async () => {
            // setIsLoading(true)
            tempAuthMutation.mutate()
        }}, {text: t("home:Cancel"), onPress: async () => {
            
        }}]
        Alert.alert(t("home:PremiumUser"), t("home:LimitOverDesc"), options)
    }
    function addButtonTapped() {
        if (profileResponse.data?.user_profile.user_type == UserTypes.FREE && data?.request_limit == 0) {
            showPremiumMemberPopup()
        } else {
            navigation.navigate('Chat', {
                item: {
                    moduleId: route.params.moduleId,
                    limit: 10
                }
            })
        }
    }
    function renderItem({item}: any) {
        let currentItem = item as z.infer<typeof conversationSchema>
        return (
            <TouchableOpacity onPress={() => itemPressed(currentItem)}>
                    <View style={[borders.rounded_16  ,gutters.marginTop_16,,gutters.marginHorizontal_16,{  borderWidth: 1, borderColor: colors.gray100}]}>
                        <View style={[gutters.marginTop_12, gutters.marginBottom_16,layout.row, layout.itemsCenter]}>
                            <FastImage 
                                source={require('@/theme/assets/images/AkinaLogo.png')}
                                resizeMode="contain" style={[ gutters.marginLeft_12, {height: 50, width: 50 ,backgroundColor: colors.gray200, borderRadius: 25,} ]} />
                            <View style={[,layout.justifyCenter, layout.flex_1,gutters.marginHorizontal_16 ,{maxHeight: 102, minHeight: 50, overflow: 'hidden'}]}>
                                <Markdown style={{}}>
                                    {currentItem.title}
                                </Markdown>
                            </View>
                        </View>
                        <Text style={[layout.absolute,fonts.gray400 , gutters.marginHorizontal_16, gutters.marginBottom_12 ,{ textAlign: 'right', right: 0, bottom: 0}]}>{timeSince(new Date(currentItem.updated_at))}</Text>
                    </View>
            </TouchableOpacity>
        )
    }
    return (
        <AKSafeAreaView>
            <Spinner
                visible={isLoading}
            />
            {(data == null || data.conversations.length == 0) && 
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
                <Text style={[fonts.size_16]}>{t('home:NoConversations')}</Text>
            </View>
            }
            
            <View style={[gutters.marginHorizontal_0]}>
                <FlatList 
                    style={[gutters.marginHorizontal_0, ]}
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={[ gutters.marginHorizontal_0,{ paddingBottom: 20}]} 
                    data={data?.conversations}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                />
            </View>
            
            <TouchableOpacity onPress={addButtonTapped} style={ [ layout.absolute,{height: 28 ,width: 43, bottom:50, alignSelf: 'center'}]}>
                <ImageVariant
                    source={AddIcon}
                    style={ [{height: 28 ,width: 43}]}
                />
            </TouchableOpacity>
        </AKSafeAreaView>
    )

}
export default ChatListing