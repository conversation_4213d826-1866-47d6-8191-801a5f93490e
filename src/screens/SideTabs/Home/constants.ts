import { MODULE_TYPES } from '@/utils/constants';

export const MODULE_TYPES_OPTIONS = {
	[MODULE_TYPES.BLOG]: {
		navigationPath: 'BlogListing',
	},
	[MODULE_TYPES.EVENT]: {
		navigationPath: 'EmpowerHerListing',
	},
	[MODULE_TYPES.MUSIC]: {
		navigationPath: 'MusicListing',
	},
	[MODULE_TYPES.PODCAST]: {
		navigationPath: 'PodcastListing',
	},
	[MODULE_TYPES.NEWS]: {
		navigationPath: 'NewsSourceListing',
	},
	[MODULE_TYPES.VIDEO]: {
		navigationPath: 'VideoListing',
	},
	[MODULE_TYPES.EVENT_NEW]: {
		navigationPath: 'LocationListing',
	},
	[MODULE_TYPES.SOCIAL_CONNECT]: {
		navigationPath: 'SocialConnect',
		shouldNotTakeProps: true,
	},
	[MODULE_TYPES.AKINA_AI]: {
		navigationPath: 'ChatListing',
		shouldNotTakeProps: false,
	},
};
