import RectPodcast<PERSON>ell from "@/components/molecules/RectPodcastCell/RectPodcastCell";
import SquarePodcast<PERSON>ell from "@/components/molecules/SquarePodcastCell/SquarePodcastCell";
import podcastListing from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { podcastByCategoryPaginationSchema, podcastByCategoryResponseSchema, podcastCategorySchema, podcastSchema } from "@/types/schemas/podcastListing";
import { LAYOUT_TYPES } from "@/utils/constants";
import { RouteProp, useIsFocused, useNavigation, useRoute } from "@react-navigation/native";
import { QueryClient, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { ActivityIndicator, Alert, FlatList, Text, TouchableOpacity, View } from "react-native"
import Spinner from "react-native-loading-spinner-overlay";
import Animated from "react-native-reanimated";
import _Icon from 'react-native-vector-icons/FontAwesome';
import { z } from "zod";
import PodcastPlayerView from "@/components/molecules/PodcastPlayerView/PodcastPlayerView";
import TrackPlayer, { State, usePlaybackState } from "react-native-track-player";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import podcastsByCategory from "@/services/home/<USER>";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
export type PodcastListingNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'PodcastDetail'
>;
var selectedCategoryId: number = -1
function PodcastListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'PodcastListing'>>();
    const navigation = useNavigation<PodcastListingNavigationProp>()
    const Icon = _Icon as React.ElementType
    const queryClient = useQueryClient()

    const {
		layout,
		gutters,
        fonts,
        borders,
        colors
	} = useTheme();
    const playerState = usePlaybackState();
    const {data, isLoading } = useQuery({
        queryKey: [`podcasts${route.params.item.id}`],
        queryFn: () =>  podcastListing(route.params.item.id)
    });
    function updateData(response: z.infer<typeof podcastByCategoryResponseSchema>) {
        if (response.success == true) {
            let key = `podcasts${route.params.item.id}`
            queryClient.setQueryData([key], (cachedData: any) => {
                let allCategories = cachedData.categories.map((category: z.infer<typeof podcastCategorySchema>) => {
                    if (category.id == selectedCategoryId) {
                        return {
                            ...category,
                            podcasts: [
                                ...category.podcasts,
                                ...response.podcasts.data
                            ],
                            is_Loading: false
                        }
                    } else {
                        return category
                    }
                })
                
                return {
                    ...cachedData,
                    categories: [
                        ...allCategories
                    ]
                }
            })

        }
    }
    const podcastByCategoryMutation = useMutation(
        {
            mutationFn: (data: any) => podcastsByCategory(data.id, data.category_id, data.page),
            onSuccess: (response) => {
                updateData(response)
            },
            onError: (error) => {
                Alert.alert("Error!", error.message)
            }
        }

    )
    
    function headerContent() {
        return (
            <></>
        )
    }
    function musicItemPressed(item: z.infer<typeof podcastSchema>, category: z.infer<typeof podcastCategorySchema>) {
        navigation.navigate('PodcastDetail', {item: item, category: category.name })
    }
    function fetchNextPageData(item: z.infer<typeof podcastCategorySchema>) {
        if (item.podcasts.length < item.podcasts_count!) {
            let data = {
                id: item.module_id,
                category_id: item.id,
                page: ((item.podcasts.length)/10) + 1
            }
            selectedCategoryId = item.id
            let key = `podcasts${route.params.item.id}`
            queryClient.setQueryData([key], (cachedData: any) => {
                let allCategories = cachedData.categories.map((category: z.infer<typeof podcastCategorySchema>) => {
                    if (item.id == category.id) {
                        return {
                            ...category,
                            is_Loading: true
                        }
                    } else {
                        return category
                    }
                })
                return {
                    ...cachedData,
                    categories: [
                        ...allCategories
                    ]
                }
            })
            podcastByCategoryMutation.mutate(data)
        }
    }
    function renderItem({item}) {
        let categoryItem = item
        if (categoryItem.layout == LAYOUT_TYPES.SQUARE_SLIDER) {
            return (
                <View>
                    <View style={[layout.row, layout.itemsCenter, gutters.marginTop_12]}>
                        <Text style={[gutters.marginHorizontal_12,fonts.gray800, fonts.size_16, fonts.Bold]}>{categoryItem.name}</Text>

                    </View>

                    <FlatList 
                        showsHorizontalScrollIndicator={false} 
                        contentContainerStyle={{}} 
                        data={categoryItem.podcasts}
                        horizontal
                        keyExtractor={
                            (_, index)=> index.toString()
                        }
                        renderItem={({item}) => (
                            <View style={[gutters.marginHorizontal_12, borders.rounded_16 , gutters.marginVertical_12, {width: 160, height: 160, overflow: 'hidden'}]}>
                                <SquarePodcastCell item={item} onPress={(music) => musicItemPressed(music, categoryItem)}/>
                            </View>
                        )}
                        onEndReached={() => fetchNextPageData(categoryItem)}
                        ListFooterComponent={
                            <View style={[layout.justifyCenter, layout.flex_1 ,layout.itemsCenter ,{width: 100}]}>
                              {categoryItem.is_loading && <ActivityIndicator color={colors.black} />}
                            </View>
                        }
                    />
                </View>
            )
        }
        else if (categoryItem.layout == LAYOUT_TYPES.RECT_SLIDER) {
            return (
                <View style={[layout.flex_1,  layout.justifyCenter, gutters.marginBottom_24]}>
                    <View style={[layout.row, layout.itemsCenter, gutters.marginTop_24]}>
                        <Text style={[gutters.marginHorizontal_12,fonts.gray800, fonts.size_16, fonts.Bold]}>{categoryItem.name}</Text>

                    </View>
                    <FlatList 
                        showsHorizontalScrollIndicator={false} 
                        contentContainerStyle={{}} 
                        data={categoryItem.podcasts}
                        horizontal
                        keyExtractor={
                            (_, index)=> index.toString()
                        }
                        renderItem={({item}) => (
                            <View style={[gutters.marginHorizontal_12 ,gutters.marginVertical_12,{width: 250, height: 184, overflow: 'hidden'}]}>
                    
                                <RectPodcastCell item={item} onPress={(music) => musicItemPressed(music, categoryItem)}/>
                            </View>
                        )}
                        onEndReached={() => fetchNextPageData(categoryItem)}
                        ListFooterComponent={
                            <View style={[layout.justifyCenter, layout.itemsCenter ,{width: 100}]}>
                              {categoryItem.is_loading && <ActivityIndicator color={colors.black}/>}
                            </View>
                        }
                    />
                </View>

            )
        } else {
            return <></>
        }

    }
    
    return (
        <AKSafeAreaView>
            <Spinner
                visible={isLoading || playerState.state == State.Loading}
            />
            <View style={[layout.flex_1]}>
                <FlatList 
                    ListHeaderComponent={headerContent}
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 20}} 
                    data={data?.categories}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                />
            </View>
        </AKSafeAreaView>
    )
}

export default PodcastListing