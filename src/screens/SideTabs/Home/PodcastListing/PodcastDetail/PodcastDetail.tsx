import { AddCommentSection, BlogDetail<PERSON><PERSON><PERSON><PERSON><PERSON>, CommentCell, MusicDetailBottomSection } from "@/components/molecules";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { secondToHHMMSS } from "@/utils/utility";
import { RouteProp, useIsFocused, useRoute } from "@react-navigation/native";
import { memo, MutableRefObject, useEffect, useRef, useState } from "react";
import { Linking, ScrollView, View, useWindowDimensions } from "react-native"
import FastImage from "react-native-fast-image";
import LinearGradient from 'react-native-linear-gradient';
import TrackPlayer, {State, useTrackPlayerEvents, Event, useProgress, usePlaybackState} from 'react-native-track-player';
import RenderHtml from 'react-native-render-html';
import WebView from "react-native-webview";
import AutoHeightWebView from 'react-native-autoheight-webview'
import { useQuery } from "@tanstack/react-query";
import podcastDetail from "@/services/home/<USER>";
import PodcastThumb from '@/theme/assets/images/Home/PodcastThumb.png'
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import { Comment } from "@/types/models/comment";
import { withComments } from "@/components/hoc/withComments/withComments";
import { AnyARecord } from "node:dns";
import { getProfile } from "@/services/users";
import { useCustomRoute } from "@/hooks/useCustomRoute";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import NotFound from '@/theme/assets/images/NotFound.png'

// import table, {IGNORED_TAGS} from '@native-html/table-plugin';
type Props = {
    setCurrentItemId: (id: number) => void
    commentRef: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>
    onDeleteComment: (comment: Comment) => void
    onCommentReply: (commentId: number, reply: string) => void
    onCommentSend: (text: string, isEdit: boolean, comment?: Comment, replyRef?: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>) => void
    onCommentButton?: () => void
    onLikeButtonPressed?: () => void
    onBookmarkButtonPressed?: () => void
    onReportComment: (postId: number, commentID: number, replyID?: number, userID?: number) => void
}
function PodcastDetail({ setCurrentItemId, commentRef, onDeleteComment, 
    onCommentReply, onCommentSend, onCommentButton,
    onLikeButtonPressed, onBookmarkButtonPressed, onReportComment }: Props) {
    const route = useCustomRoute();
    const isFocused = useIsFocused();
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const [moduleId, setModuleId] = useState(route.params?.item?.module_id)
    const [podcastId, setPodcastId] = useState(route.params?.item.id)
    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });
    const { data: podcastDetailData } = useQuery({
        queryKey: [`podcastsDetail${podcastId}`],
        queryFn: () => podcastDetail(moduleId,podcastId)
    })
    const [comments, setComments] = useState(podcastDetailData?.comments)
    const { width } = useWindowDimensions();
    const [htmlContent, setHtmlContent] = useState(podcastDetailData?.description)
    const flatListRef = useRef()
    const webRef = useRef<AutoHeightWebView>(null)

    
    useEffect(() => {
        if (isFocused == false) {
            TrackPlayer.stop()
        }
    },[isFocused])
    useEffect(() => {
        setHtmlContent(podcastDetailData?.description)
    },[podcastDetailData?.description])
    useEffect(() => {
        setComments(podcastDetailData?.comments)
    },[podcastDetailData?.comments])

    useEffect(() => {
        setCurrentItemId(route.params?.item.id);
    }, []);
    
  
    
    function onEditComment(comment: Comment) {
        if (commentRef.current != null) {
            commentRef?.current.editComment(comment)
        }
        setTimeout(() => {
            if (commentRef.current != null) {
                commentRef.current.makeCommentFirstResponsder()
            }
        }, 200)
    }
    function renderItem({item}: any) {
        let comment: Comment = item
        return (
            <CommentCell 
                comment={comment} 
                onCommentReply={onCommentReply}
                onEditComment={onEditComment}
                onDeleteComment={onDeleteComment}
                onCommentSend={onCommentSend}
                onReportComment={onReportComment}
            />
        )
    }
    return (
        <View style={[layout.flex_1]}>
                <View style={[layout.flex_1]}>
                <KeyboardAwareFlatList 
                    ListHeaderComponent={
                        <BlogDetailListHeader
                            postDetail={podcastDetailData}
                            onLikeButtonPressed={onLikeButtonPressed}
                            onBookmarkButtonPressed={onBookmarkButtonPressed}
                            onCommentButton={onCommentButton}
                            onCommentSend={onCommentSend}
                            commentRef={commentRef}
                            profilePhoto={data?.user_profile.profile_photo}
                        />
                    }
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 80, flexGrow: 1}} 
                    data={comments}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    extraHeight={150}
                    keyboardShouldPersistTaps={'handled'}
                />
            </View>
            <View style={[gutters.marginBottom_32]}>
                <MusicDetailBottomSection
                    url={podcastDetailData?.audio_link}
                    image={podcastDetailData?.thumbnail}
                    hasNext={false}
                    hasPrevious={false}
                    title={podcastDetailData?.title}
                    subtitle={podcastDetailData?.category?.name}
                    isPodcast={true}
                />
            </View>
            {podcastDetailData?.status == false && 
                <EmptyDataView
                    heading="Podcast not found"
                    desc="The podcast you are looking for does not exist or has been removed."
                    image={NotFound}
                    isFullScreen
                />
            }
        </View>
        
    )

}

export default withComments(PodcastDetail, 'podcasts')