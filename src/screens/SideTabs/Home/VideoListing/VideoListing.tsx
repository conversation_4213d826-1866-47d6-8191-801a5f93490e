import { ImageVariant } from "@/components/atoms";
import { CarouselItemsCell, RectMusicCell, SquareMusicCell } from "@/components/molecules";
import musicListing from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { categoriesListingSchema, musicSchema } from "@/types/schemas/musicListing";
import { LAYOUT_TYPES } from "@/utils/constants";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { ActivityIndicator, Alert, FlatList, Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
// import CircularCarousel from  'react-native-circular-carousel';
import Carousel from 'react-native-reanimated-carousel';
import _Icon from 'react-native-vector-icons/FontAwesome';
import { z } from "zod";
import ColorLogo from "@/theme/assets/images/ColorLogo.png"
import videoListing from "@/services/home/<USER>";
import { videoSchema } from "@/types/schemas/videoListing";
import RectVideoCell from "@/components/molecules/RectVideoCell/RectVideoCell";
import SquareVideoCell from "@/components/molecules/SquareVideoCell/SquareVideoCell";
import VideoCarouselView from "@/components/molecules/VideoCarouselView/VideoCarouselView";
import Video from "react-native-video";
import { useTranslation } from "react-i18next";
import Spinner from "react-native-loading-spinner-overlay";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";

export type VideoListingNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'VideoDetail'
>;
function VideoListing() {
    const { t } = useTranslation(['home']);
    const route = useRoute<RouteProp<ApplicationStackParamList, 'VideoListing'>>();
    const navigation = useNavigation<VideoListingNavigationProp>()
    const [videoLoading, setVideoLoading] = useState(false)

    const Icon = _Icon as React.ElementType

    const {
		layout,
		gutters,
        fonts,
        borders,
        colors
	} = useTheme();
    const {data, isFetching } = useQuery({
        queryKey: [`videos${route.params.item.id}`],
        queryFn: () =>  videoListing(route.params.item.id)
    });

    function headerContent() {
        return (
            <View>
                <View style={[{height: 316}]}>
                    <View style={[gutters.margin_16 , layout.flex_1, ]}>
                        {videoLoading &&
                            <ActivityIndicator
                                animating
                                color={"gray"}
                                size="large"
                                style={{ flex: 1, position:"absolute", top:"50%", left:"45%" }}
                            />
                        }
                        <Video 
                            onLoadStart={() => setVideoLoading(true)}
                            poster={require('@/theme/assets/images/ProfilePlaceholder.png')} 
                            resizeMode="cover" 
                            repeat={true} 
                            style={[layout.flex_1,borders.rounded_16, {overflow: 'hidden'}]} 
                            source={{uri: data?.module.header_content_value}} 
                        />
                            <ImageVariant
                                source={ColorLogo}
                                style={ [ { resizeMode: 'contain' ,position: 'absolute' ,top: 8, right: 30, width: 38, height: 36}]}
                            />

                    </View>
                </View>
                <View style={[gutters.marginHorizontal_24, gutters.marginTop_16 ]}>
                    <Text style={[fonts.size_24, fonts.Medium, {textAlign: 'center'}]}>{t('home:BrowseYourPlaylist')}</Text>
                </View>
         </View>

        )
        
    }
    function videoItemPressed(item: z.infer<typeof videoSchema>, category: z.infer<typeof categoriesListingSchema>) {
        navigation.navigate("VideoDetail", {item: item, categoryName: category.name})
    }
    function renderItem({item}: any) {
        let categoryItem = item
        if (categoryItem.layout == LAYOUT_TYPES.CAROUSEL) {
            return (
                <VideoCarouselView item={categoryItem} onPress={(video) => videoItemPressed(video, item)}/>
            )
        }
        else if (categoryItem.layout == LAYOUT_TYPES.SQUARE_SLIDER) {
            return (
                <View>
                    <View style={[layout.row, layout.itemsCenter,]}>
                        <Text style={[gutters.marginHorizontal_12,fonts.musicCategoryTitleColor, fonts.size_24, fonts.Bold]}>{categoryItem.name}</Text>
                        <Icon style={[gutters.marginLeft_16]} name='angle-right' size={30} color={colors.musicCategoryTitleColor}/>

                    </View>

                    <FlatList 
                        showsHorizontalScrollIndicator={false} 
                        contentContainerStyle={{}} 
                        data={categoryItem.videos}
                        horizontal
                        keyExtractor={
                            (_, index)=> index.toString()
                        }
                        renderItem={({item}) => (
                            <View style={[gutters.marginHorizontal_12, borders.rounded_16 , gutters.marginVertical_24, {width: 160, height: 160, overflow: 'hidden'}]}>
                                <SquareVideoCell item={item} onPress={(video) => videoItemPressed(video, categoryItem)}/>
                            </View>
                        )}
                    />
                </View>
            )
        }
        else if (categoryItem.layout == LAYOUT_TYPES.RECT_SLIDER) {
            return (
                <View style={[layout.flex_1,]}>
                    <View style={[layout.row, layout.itemsCenter,]}>
                        <Text style={[gutters.marginHorizontal_12,fonts.musicCategoryTitleColor, fonts.size_24, fonts.Bold]}>{categoryItem.name}</Text>
                        <Icon style={[gutters.marginLeft_16]} name='angle-right' size={30} color={colors.musicCategoryTitleColor}/>
                    </View>
                    <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter, gutters.marginBottom_24]}>

                    <FlatList 
                        showsHorizontalScrollIndicator={false} 
                        contentContainerStyle={{}} 
                        data={categoryItem.videos}
                        horizontal
                        keyExtractor={
                            (_, index)=> index.toString()
                        }
                        renderItem={({item}) => (
                            <View style={[gutters.marginHorizontal_12, borders.rounded_16 ,gutters.marginVertical_24,{width: 304, height: 160, overflow: 'hidden'}]}>
                    
                                <RectVideoCell item={item} onPress={(video) => videoItemPressed(video, categoryItem)}/>
                            </View>
                        )}
                    />
                    </View>
                </View>
            )
        } else {
            return <></>
        }
        
    }
    return (
        <AKSafeAreaView>
            <Spinner
                visible={isFetching}
            />    
            <View>
                <FlatList 
                    ListHeaderComponent={headerContent}
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 20}} 
                    data={data?.categories}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                />
            </View>
        </AKSafeAreaView>
    )
}
export default VideoListing