import { withComments } from "@/components/hoc/withComments/withComments";
import { CommentCell, VideoDetailListHeader } from "@/components/molecules";
import { useCustomRoute } from "@/hooks/useCustomRoute";
import videoDetail from "@/services/home/<USER>";
import { getProfile } from "@/services/users";
import { useTheme } from "@/theme";
import { Comment } from "@/types/models/comment";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useRoute } from "@react-navigation/native";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { View } from "react-native"
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import Spinner from "react-native-loading-spinner-overlay";
import { VideoRef } from "react-native-video";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import NotFound from '@/theme/assets/images/NotFound.png'

type Props = {
    onDeleteComment: () => void
    onBookmarkButtonPressed: () => void
    onCommentButton: () => void
    onCommentSend: () => void
    onLikeButtonPressed: () => void
    onCommentReply: () => void
    commentRef: any
    onReportComment: (postId: number, commentID: number, replyID?: number, userID?: number) => void
}

function VideoDetail(props: Props) {
    
    const { commentRef, onDeleteComment, 
        onCommentReply, onCommentSend, onCommentButton,
        onLikeButtonPressed, onBookmarkButtonPressed, onReportComment } = props;
    const { t } = useTranslation(['home']);
    // const route = useRoute<RouteProp<ApplicationStackParamList, 'VideoDetail'>>();
    const route = useCustomRoute();
    const [currentVideo, setCurrentVideo] = useState(route.params.item)
    const flatListRef = useRef<any>()
    const videoRef = useRef<VideoRef>(null)
    const [isLoading, setIsLoading] = useState(true)

    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });

    const videoDetailResponse = useQuery({
        queryKey: [`videosDetail${route.params.item?.id}`],
        queryFn: () => videoDetail(route.params.item?.module_id, route.params.item?.id)
    })

    const {
		layout,
	} = useTheme();
    useEffect(() => {
        if (videoDetailResponse.isSuccess) {
            setCurrentVideo(videoDetailResponse.data)
        }
    }, [videoDetailResponse.data, videoDetailResponse.isFetching])

    function onEditComment(comment: Comment) {
        if (commentRef.current != null) {
            commentRef?.current.editComment(comment)
        }
        setTimeout(() => {
            if (commentRef.current != null) {
                commentRef.current.makeCommentFirstResponsder()
            }
            if (flatListRef.current != null) {
                flatListRef?.current.scrollToPosition(0, 0)
            }
        }, 200)

    }

    function renderItem({item}: any) {
        let comment: Comment = item
        return (
            <CommentCell 
                comment={comment} 
                onCommentReply={onCommentReply}
                onEditComment={onEditComment}
                onDeleteComment={onDeleteComment}
                onReportComment={onReportComment}
            />
        )
    }


    return (
        <View style={[layout.flex_1]}>
            <Spinner
                visible={videoDetailResponse.isFetching }
            />
            <View style={[layout.flex_1]}>
                <KeyboardAwareFlatList 
                    ref={flatListRef}
                    ListHeaderComponent={
                        <VideoDetailListHeader
                            currentVideo={currentVideo}
                            onLikeButtonPressed={onLikeButtonPressed}
                            onBookmarkButtonPressed={onBookmarkButtonPressed}
                            onCommentButton={onCommentButton}
                            onCommentSend={onCommentSend}
                            commentRef={commentRef}
                            profilePhoto={data?.user_profile.profile_photo}
                            title={currentVideo?.title}
                        />
                    }
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 20,}} 
                    data={videoDetailResponse.data?.comments}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    keyboardShouldPersistTaps={'handled'}  
                />
            </View>
            {videoDetailResponse.data?.status == false && 
                <EmptyDataView
                    heading="Video not found"
                    desc="The video you are looking for does not exist or has been removed."
                    image={NotFound}
                    isFullScreen
                />
            }
        </View>
    )
}

export default withComments(VideoDetail, 'videos');
