import { SafeScreen } from '@/components/template';
import modules from '@/services/home/<USER>';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { FlatList, TouchableOpacity, View } from 'react-native';
import { moduleSchema } from '@/types/schemas/module';
import HomeItem from '@/components/atoms/HomeItem/HomeItem';
import Spinner from 'react-native-loading-spinner-overlay';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ApplicationStackParamList } from '@/types/navigation';
import { useNavigation } from '@react-navigation/native';
import { getProfile } from '@/services/users';
import { MODULE_TYPES_OPTIONS } from './constants';

import { useTranslation } from "react-i18next";
import storeFcmToken from '@/services/users/storeFcmToken';
import { getFcmToken, registerNotificationForegroundHandler } from '@/integrations/fireabase/messaging';
import { storage } from '@/App';
import { FCM_TOKEN_STORAGE_KEY } from '@/utils/constants';
export type HomeScreenNavigationProp = NativeStackNavigationProp<
	ApplicationStackParamList,
	'BlogListing',
	'EmpowerHerListing'
>;
function Home() {

    const { t } = useTranslation(['home']);
    const navigation = useNavigation<HomeScreenNavigationProp>()
    const {data, refetch, isFetched, isFetching } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        
    });

	const { data: modulesList, isLoading } = useQuery({
        queryKey: ['modules'],
        queryFn: modules
    });
	  
	async function handleTokenUpdate() {
		try {
			const token = await getFcmToken();
			console.log("FCM::", token)
		  	const storedToken = await storage.getString(FCM_TOKEN_STORAGE_KEY);
			if (!!token && token !== storedToken) {
				storeFcmMutation.mutate({
					device_token: token,
					device_type: 'ios'
				});
			} else {
				console.log('Same token');
			}
		} catch (error) {
		  	console.log(error);
		}
	}

	const storeFcmMutation = useMutation(
		{
			mutationFn: (data: any) => storeFcmToken(data),
			onSuccess: (response) => {
				console.log(response);
			},
			onError: (error) => {
				console.log(error.message)
			}
		},
	);

    useEffect(() => {
        if ((isFetching == false) && (data?.user_profile?.is_completed == null || data?.user_profile?.is_completed == false)) {
            presentOnBoardingAfterDelay()
        }
    }, [data])

	useEffect(() => {
		handleTokenUpdate();
		registerNotificationForegroundHandler();
	}, []);

	function presentOnBoardingAfterDelay() {
		setTimeout(() => {
			navigation.navigate('OnBoarding');
		}, 1000);
	}
	function itemPressed(item: (typeof moduleSchema)['_output']) {
		const { navigationPath, shouldNotTakeProps } =
			MODULE_TYPES_OPTIONS[item.type] || {};
		if (navigationPath) {
			navigation.navigate(navigationPath, !shouldNotTakeProps ? { item } : {});
		}
	}

	function renderItem({ item }) {
		return (
			<TouchableOpacity onPress={() => itemPressed(item)}>
				<HomeItem item={item} />
			</TouchableOpacity>
		);
	}

	return (
		<SafeScreen>
			<View>
				<Spinner visible={isLoading} />
				<FlatList
					showsVerticalScrollIndicator={false}
					contentContainerStyle={{ paddingBottom: 20 }}
					data={modulesList?.modules}
					keyExtractor={(_, index) => index.toString()}
					renderItem={renderItem}
					// ItemSeparatorComponent={() => (<View style={styles.line}/>)}
				/>
			</View>
		</SafeScreen>
	);
}

export default Home;
