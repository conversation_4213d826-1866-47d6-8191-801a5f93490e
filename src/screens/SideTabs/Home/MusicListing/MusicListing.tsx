import { AKFastImage, ImageVariant } from "@/components/atoms";
import { CarouselItemsCell, RectMusicCell, SquareMusicCell } from "@/components/molecules";
import musicListing from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { categoriesListingSchema, musicSchema } from "@/types/schemas/musicListing";
import { LAYOUT_TYPES } from "@/utils/constants";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Alert, FlatList, Text, TouchableOpacity, View } from "react-native";
import FastImage from "react-native-fast-image";
// import CircularCarousel from  'react-native-circular-carousel';
import Carousel from 'react-native-reanimated-carousel';
import _Icon from 'react-native-vector-icons/FontAwesome';
import { z } from "zod";
import ColorSquareLogo from "@/theme/assets/images/ColorSquareLogo.png"
import { useTranslation } from "react-i18next";
import Spinner from "react-native-loading-spinner-overlay";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";

export type MusicListingNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'MusicDetail'
>;
const Icon = _Icon as React.ElementType

function MusicListing() {
    const { t } = useTranslation(['home']);
    const route = useRoute<RouteProp<ApplicationStackParamList, 'MusicListing'>>();
    const navigation = useNavigation<MusicListingNavigationProp>()
    const Icon = _Icon as React.ElementType
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const {data, isFetching } = useQuery({
        queryKey: [`music${route.params.item.id}`],
        queryFn: () =>  musicListing(route.params.item.id)
    });
    function headerContent() {
        return (
            <View>
                <View style={[{height: 316}]}>
                    <View style={[backgrounds.gray100, layout.flex_1, {borderBottomLeftRadius: 40}]}>
                            <AKFastImage
                                uri={route.params.item.header_content_value}
                                style={[layout.flex_1, gutters.marginLeft_16 ,{borderBottomLeftRadius: 45}]}
                                resizeMode={FastImage.resizeMode.cover}
                            />
                            <ImageVariant
                                source={ColorSquareLogo}
                                style={ [ { resizeMode: 'contain' ,position: 'absolute' ,top: 8, right: 30, width: 38, height: 36}]}
                            />

                    </View>
               
                </View>
            
                <View style={[gutters.marginHorizontal_24, gutters.marginTop_16 ]}>
                    <Text style={[fonts.size_24, fonts.Medium, {textAlign: 'center'}]}>Browse your playlist for your every mode</Text>
                </View>
         </View>

        )
        
    }
    function musicItemPressed(item: z.infer<typeof musicSchema>, category: z.infer<typeof categoriesListingSchema>) {
        navigation.navigate("MusicDetail", {item: item, categoryName: category.name})
    }
    function renderItem({item}: any) {
        let categoryItem = item
        if (categoryItem.layout == LAYOUT_TYPES.CAROUSEL) {
            return (
                <CarouselItemsCell item={categoryItem} onPress={(music) => musicItemPressed(music, item)}/>
            )
        }
        else if (categoryItem.layout == LAYOUT_TYPES.SQUARE_SLIDER) {
            return (
                <View>
                    <View style={[layout.row, layout.itemsCenter,]}>
                        <Text style={[gutters.marginHorizontal_12,fonts.musicCategoryTitleColor, fonts.size_24, fonts.Bold]}>{categoryItem.name}</Text>
                        <Icon style={[gutters.marginLeft_16]} name='angle-right' size={30} color={colors.musicCategoryTitleColor}/>
                    </View>
                    <FlatList 
                        showsHorizontalScrollIndicator={false} 
                        contentContainerStyle={{}} 
                        data={categoryItem.music}
                        horizontal
                        keyExtractor={
                            (_, index)=> index.toString()
                        }
                        renderItem={({item}) => (
                            <View style={[gutters.marginHorizontal_12, borders.rounded_16 , gutters.marginVertical_24, {width: 160, height: 160, overflow: 'hidden'}]}>
                                <SquareMusicCell item={item} onPress={(music) => musicItemPressed(music, categoryItem)}/>
                            </View>
                        )}
                    />
                </View>
            )
        }
        else if (categoryItem.layout == LAYOUT_TYPES.RECT_SLIDER) {
            return (
                <View>
                    <View style={[layout.row, layout.itemsCenter,]}>
                        <Text style={[gutters.marginHorizontal_12,fonts.musicCategoryTitleColor, fonts.size_24, fonts.Bold]}>{categoryItem.name}</Text>
                        <Icon style={[gutters.marginLeft_16]} name='angle-right' size={30} color={colors.musicCategoryTitleColor}/>
                    </View>
                    <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter, gutters.marginBottom_24]}>
                        <FlatList 
                            showsHorizontalScrollIndicator={false} 
                            contentContainerStyle={{}} 
                            data={categoryItem.music}
                            horizontal
                            keyExtractor={
                                (_, index)=> index.toString()
                            }
                            renderItem={({item}) => (
                                <View style={[gutters.marginHorizontal_12, borders.rounded_16 ,gutters.marginVertical_24,{width: 304, height: 160, overflow: 'hidden'}]}>
                        
                                    <RectMusicCell item={item} onPress={(music) => musicItemPressed(music, categoryItem)}/>
                                </View>
                            )}
                        />
                    </View>
                </View>

            )
        } else {
            return <></>
        }
    }
    return (
        <AKSafeAreaView>
            <Spinner
                visible={isFetching}
            />
            <View>
                <FlatList 
                    ListHeaderComponent={headerContent}
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 20}} 
                    data={data?.categories}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                />
            </View>
        </AKSafeAreaView>
    )
}
export default MusicListing