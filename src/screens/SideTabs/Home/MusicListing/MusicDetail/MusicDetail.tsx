import { ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import { hexToRgbA, secondToHHMMSS } from "@/utils/utility";
import { Alert, StyleSheet, Text, View } from "react-native"
import FastImage from "react-native-fast-image";
import LinearGradient from "react-native-linear-gradient";
import RoundButtonBG from "@/theme/assets/images/Home/RoundButtonBG.png"
import HeartFilled from "@/theme/assets/images/Home/HeartFilled.png"
import MessageIcon from "@/theme/assets/images/Home/MessageIcon.png"
import SaveIcon from "@/theme/assets/images/Home/SaveIcon.png"
import Next from "@/theme/assets/images/Home/Next.png"
import Previous from "@/theme/assets/images/Home/Previous.png"
import Pause from "@/theme/assets/images/Home/Pause.png"
import AkinaLogo from "@/theme/assets/images/AkinaLogo.png"

import Slider from '@react-native-community/slider';
import MusicDetailTopSection from "@/components/molecules/MusicDetailTopSection/MusicDetailTopSection";
import { MutableRefObject, useEffect, useRef, useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import musicDetail from "@/services/home/<USER>";
import { RouteProp, useRoute } from "@react-navigation/native";
import { ApplicationStackParamList } from "@/types/navigation";
import MusicDetailBottomSection from "@/components/molecules/MusicDetailBottomSection/MusicDetailBottomSection";
import MusicDetailButtonsSection from "@/components/molecules/MusicDetailButtonsSection/MusicDetailButtonsSection";
import TrackPlayer, {State, useTrackPlayerEvents, Event, useProgress, usePlaybackState} from 'react-native-track-player';
import { useIsFocused } from '@react-navigation/native';
import Spinner from "react-native-loading-spinner-overlay";
import { useTranslation } from "react-i18next";
import { Comment } from "@/types/models/comment";
import { withComments } from "@/components/hoc/withComments/withComments";
import { AddCommentSection, CommentCell } from "@/components/molecules";
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import { useCustomRoute } from "@/hooks/useCustomRoute";
import { getProfile } from "@/services/users";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import NotFound from '@/theme/assets/images/NotFound.png'

type Props = { 
    setCurrentItemId: (id: number) => void
    commentRef: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>
    onDeleteComment: (comment: Comment) => void
    onCommentReply: (commentId: number, reply: string) => void
    onCommentSend: (text: string, isEdit: boolean, comment?: Comment, replyRef?: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>) => void
    onCommentButton: () => void
    onLikeButtonPressed: () => void
    onBookmarkButtonPressed: () => void
    onReportComment: (postId: number, commentID: number, replyID?: number, userID?: number) => void
}

function MusicDetail({ setCurrentItemId ,commentRef, onDeleteComment, 
    onCommentReply, onCommentSend, onCommentButton,
    onLikeButtonPressed, onBookmarkButtonPressed, onReportComment }: Props) {
    const { t } = useTranslation(['home']);
    const route = useCustomRoute();
    const isFocused = useIsFocused();
    const [currentProduct, setCurrentProduct] = useState(route.params?.item)
    const [moduleId, setModuleId] = useState(route.params?.item?.module_id)
    const [musicId, setMusicId] = useState(route.params?.item?.id)
    const flatListRef = useRef(null)
    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const events = [
        Event.PlaybackState,
        Event.PlaybackError,
    ];
    const musicDetailResponse = useQuery({
        queryKey: [`musicDetail${musicId}`],
        queryFn: () => musicDetail(moduleId,musicId)
    })
    useEffect(() => {
        if (musicDetailResponse.isSuccess) {
            console.log("Music Data", musicDetailResponse)
            setCurrentProduct(musicDetailResponse.data)
            setCurrentItemId(musicDetailResponse.data.id)
            // route.params?.item?.id = musicDetailResponse.data.id
        }
    }, [musicDetailResponse.data, musicDetailResponse.isFetching])
    useEffect(() => {
        if (isFocused == false) {
            TrackPlayer.stop()
        }
    },[isFocused])
   
    
    function onNextButton() {
        if (currentProduct?.next !=null) {
            setMusicId(currentProduct.next)
        }
    }
    function onPreviousButton() {
        if (currentProduct?.previous !=null) {
            setMusicId(currentProduct.previous)
        }
    }
    function onEditComment(comment: Comment) {
        if (commentRef.current != null) {
            commentRef?.current.editComment(comment)
        }
        setTimeout(() => {
            if (commentRef.current != null) {
                commentRef.current.makeCommentFirstResponsder()
            }
        }, 200)
    }
    function renderItem({item}: any) {
        let comment: Comment = item
        return (
            <CommentCell 
                comment={comment} 
                onCommentReply={onCommentReply}
                onEditComment={onEditComment}
                onDeleteComment={onDeleteComment}
                onCommentSend={onCommentSend}
                onReportComment={onReportComment}
            />
        )
    }
    function renderHeader() {
        return (
            <View>
                <MusicDetailTopSection
                    isSaveSelected={currentProduct?.is_bookmarked ?? false} 
                    isLikeSelected={currentProduct?.is_liked ?? false} 
                    image={currentProduct?.thumbnail ?? ''}
                    onLikeButton={onLikeButtonPressed}
                    onSaveButton={onBookmarkButtonPressed} 
                    title={currentProduct?.title}
                    subtitle={route.params?.categoryName}
                />
                <View>
                    <MusicDetailButtonsSection 
                        isLikeSelected={currentProduct?.is_liked ?? false}
                        isSaveSelected={currentProduct?.is_bookmarked ?? false}
                        onLikeButton={onLikeButtonPressed}
                        onSaveButton={onBookmarkButtonPressed}
                        onCommentButton={onCommentButton}
                    />
                </View>
                <View>
                    <AddCommentSection  
                        ref={commentRef} 
                        onSend={onCommentSend} 
                        image={data?.user_profile.profile_photo}
                    />
                </View>
            </View>
            
        )
    }

    return (
        <View style={[ layout.flex_1, backgrounds.white]}>
            <Spinner
                visible={musicDetailResponse.isFetching}
            />
            
            <View style={[layout.flex_1]}>
                <KeyboardAwareFlatList 
                    ref={flatListRef}
                    ListHeaderComponent={renderHeader()}
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={{ paddingBottom: 80, flexGrow: 1}} 
                    data={musicDetailResponse.data?.comments}
                    // enableAutomaticScroll={false}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    extraHeight={150}
                    keyboardShouldPersistTaps={'handled'}
                    // style={[layout.flex_1]}
                />
            </View>
            <View style={[gutters.marginBottom_32]}>
                <MusicDetailBottomSection 
                    url={currentProduct.audio_link}
                    image={currentProduct?.thumbnail}
                    hasNext={currentProduct?.next != null}
                    hasPrevious={currentProduct?.previous != null}
                    onNext={onNextButton}
                    onPrevious={onPreviousButton}
                    title={currentProduct?.title}
                    subtitle={route.params?.categoryName}
                />
            </View>
            {musicDetailResponse.data?.status == false && 
                <EmptyDataView
                    heading="Music not found"
                    desc="The music you are looking for does not exist or has been removed."
                    image={NotFound}
                    isFullScreen
                />
            }
        </View>
    )
}
export default withComments(MusicDetail, 'music');