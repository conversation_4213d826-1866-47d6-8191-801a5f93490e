import { AKFastImage, ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { FlatList, Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import Akina<PERSON>ogo from "@/theme/assets/images/AkinaLogo.png"
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import eventListing from "@/services/home/<USER>";
import { eventSchema } from "@/types/schemas/eventListing";
import { z } from "zod";
import Spinner from "react-native-loading-spinner-overlay";
import { useEffect } from "react";
import _Icon from 'react-native-vector-icons/FontAwesome';
import moment from "moment";

export type EventsScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'LocationEvents'
>;
const Icon = _Icon as React.ElementType

function LocationEvents() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'LocationEvents'>>();
    const navigation = useNavigation<EventsScreenNavigationProp>()
    const { t } = useTranslation(['home']);
    const {data, isLoading } = useQuery({
        queryKey: [`events/${route.params.item.module_id}/locations/${route.params.item.id}`],
        queryFn: () =>  eventListing(route.params.item.module_id, route.params.item.id )
    });
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    useEffect(() => {
        console.log("Current Data::", data )
    },[data])
    function headerContent() {
        return (
            <View style={[{height: 316}]}>
                
                <View style={[backgrounds.gray100, layout.flex_1, {borderBottomLeftRadius: 40}]}>
                    <AKFastImage
                        uri={route.params.item.thumbnail}
                        style={[layout.flex_1 ,{borderBottomLeftRadius: 45}]}
                    />

                    <View style={[layout.absolute, layout.bottom0, layout.left0]}>
                        <ImageVariant
                            source={AkinaLogo}
                            style={ [ gutters.marginLeft_32, gutters.marginBottom_16, {tintColor:  colors.gray100, resizeMode: 'contain'  , width: 24, height: 40}]}
                        />

                    </View>

                </View>
                <View style={[gutters.marginHorizontal_24, gutters.marginTop_16 ]}>
                    <Text style={[fonts.size_24, fonts.Medium, {textAlign: 'center'}]}>{t("home:EventsTitle", {name: route.params.isAkinaEvents ? 'Akina' : route.params.item.name})}</Text>
                </View>
                <View style={[gutters.marginHorizontal_24, gutters.marginTop_16 ]}>
                    <Text style={[fonts.size_12,  ,{textAlign: 'center',}]}>{t("home:EventsTitle", {name: route.params.item.sub_title})}</Text>
                </View>
                
            </View>
        )
    }
    function itemPressed(item: z.infer<typeof eventSchema>) {
        navigation.navigate('EventDetail', {item: item})
    }
    function renderItem({item}) {
        let currentItem: z.infer<typeof eventSchema> = item
        return (
            <TouchableOpacity onPress={() => itemPressed(currentItem)}>
                <View style={[gutters.marginTop_16]}>
                    <View style={[ gutters.marginHorizontal_16,{height: 224}]}>
                        <AKFastImage
                            uri={item.thumbnail}
                            style={[layout.flex_1,{borderBottomLeftRadius: 100}]}
                        />
                    </View>
                    <View style={[layout.flex_1, layout.itemsCenter, gutters.marginVertical_16]}>
                        <Text>{`${moment(currentItem.event_date).format('ddd D MMMM YYYY').toString()} ${route.params.item.name}`}</Text>
                    </View>
                    <View style={[gutters.marginHorizontal_16, ]}>
                        <Text style={[fonts.bold]}>{currentItem.title}</Text>
                        <Text>{currentItem.sub_title}</Text>
                        <View style={[layout.row, gutters.marginTop_16, layout.itemsCenter]}>
                            <Text style={[fonts.bold, ]}>{currentItem.button_text}</Text>
                            <Icon style={[gutters.marginLeft_12]} name='angle-right' size={18} color={colors.gray800}/>

                        </View>
                    </View>
                    <View style={[layout.absolute, backgrounds.lightBlue, borders.rounded_4 ,{right: 26, top: 10}]}>
                        <Text style={[gutters.marginHorizontal_12, gutters.marginVertical_4]}>{t('home:Events')}</Text>
                    </View>
                </View>

            </TouchableOpacity>
            
        )


    }   
    return (
        <View>
            <Spinner
                visible={isLoading}
            />
            <FlatList 
                ListHeaderComponent={headerContent}
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{ paddingBottom: 20}} 
                data={route.params.isAkinaEvents ? data?.akina_events : data?.location_events}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
            />
        </View>
    )
}

export default LocationEvents