import { AKFastImage, ImageVariant } from "@/components/atoms"
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { ScrollView, Text, TouchableOpacity, View, useWindowDimensions } from "react-native"
import FastImage from "react-native-fast-image"
import Akina<PERSON>ogo from "@/theme/assets/images/AkinaLogo.png"
import RenderHtml from 'react-native-render-html';

export type EventDetailScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'EventDetail'
>;
function EventDetail() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'EventDetail'>>();
    const navigation = useNavigation<EventDetailScreenNavigationProp>()
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    const { width } = useWindowDimensions();

    return (
        <ScrollView >
            <View style={[gutters.marginBottom_80]}>
                <View style={[{height: 334}]}>
                    
                    <View style={[backgrounds.gray100, layout.flex_1, {borderBottomLeftRadius: 40, height: 334}]}>
                        <AKFastImage
                            uri={route.params.item.thumbnail}
                            style={[{borderBottomLeftRadius: 45, height: 334}]}
                        />
                        <View style={[layout.absolute, layout.bottom0, layout.left0]}>
                            <ImageVariant
                                source={AkinaLogo}
                                style={ [ gutters.marginLeft_32, gutters.marginBottom_16, {tintColor:  colors.orange, resizeMode: 'contain'  , width: 24, height: 40}]}
                            />

                        </View>

                    </View>
                    
                    
                </View>
                <View style={[gutters.marginHorizontal_24, gutters.marginTop_16 ]}>
                    <Text style={[fonts.size_24, fonts.Medium,{textAlign: 'left'}]}>{route.params.item.title}</Text>
                </View>
                <View style={[gutters.marginHorizontal_24, gutters.marginTop_16 ]}>
                    <Text style={[fonts.size_12,  ,{textAlign: 'left',}]}>{route.params.item.sub_title}</Text>
                </View>
                <View style={[gutters.marginHorizontal_24, gutters.paddingHorizontal_12 ,gutters.marginTop_12, backgrounds.lightBlue, borders.rounded_16]}>
                    <RenderHtml
                        contentWidth={width-48}
                        source={{html: route.params.item.content}}
                        
                    />
                    {/* <Text style={[gutters.marginVertical_16, gutters.marginHorizontal_16]}>{route.params.item.content}</Text> */}
                    <TouchableOpacity>

                        <View style={[gutters.marginVertical_16, gutters.marginHorizontal_16, backgrounds.orange ,layout.itemsCenter, layout.justifyCenter ,{height: 40, width: 50, borderRadius: 8}]}>
                                <Text>{route.params.item.button_text}</Text>
                        </View>
                    </TouchableOpacity>
                    <AKFastImage
                        uri={route.params.item.thumbnail}
                        style={[gutters.marginHorizontal_16, gutters.marginVertical_16 ,{borderRadius: 8,borderBottomLeftRadius: 45, height: 190}]}
                    />
                </View>
            </View>
            
        </ScrollView>
    )

}
export default EventDetail