import { AKFastImage, ImageVariant } from "@/components/atoms"
import empowerHerListing from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useQuery } from "@tanstack/react-query";
import { FlatList, Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image"
import Spinner from "react-native-loading-spinner-overlay"
import AkinaLogo from "@/theme/assets/images/AkinaLogo.png"
import { useTranslation } from "react-i18next";
import eventLocationListing from "@/services/home/<USER>";
import { eventLocationSchema } from "@/types/schemas/eventLocationListing";
import { z } from "zod";
import { useActionSheet } from "@expo/react-native-action-sheet";

export type LocationsScreenNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'LocationListing'
>;
function LocationListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'LocationListing'>>();
    const navigation = useNavigation<LocationsScreenNavigationProp>()
    const { t } = useTranslation(['home']);
    const { showActionSheetWithOptions } = useActionSheet();

    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors,
	} = useTheme();
    const {data, isLoading } = useQuery({
        queryKey: [`events/${route.params.item.id}`],
        queryFn: () =>  eventLocationListing(route.params.item.id)
    });
    function headerContent() {
        return (
            <View style={[{height: 316}]}>
                
                <View style={[backgrounds.gray100, layout.flex_1, {borderBottomLeftRadius: 40}]}>
                    <AKFastImage
                        uri={route.params.item.thumbnail}
                        style={[layout.flex_1, {borderBottomLeftRadius: 45}]}
                    />
                    <View style={[layout.absolute, layout.bottom0, layout.left0]}>
                        <ImageVariant
                            source={AkinaLogo}
                            style={ [ gutters.marginLeft_32, gutters.marginBottom_16, {tintColor:  colors.gray100, resizeMode: 'contain'  , width: 24, height: 40}]}
                        />

                    </View>

                </View>
                <View style={[gutters.marginHorizontal_24, gutters.marginTop_16 ]}>
                    <Text style={[fonts.size_24, fonts.Medium, {textAlign: 'center'}]}>{t("home:LocationListingTitle")}</Text>
                </View>
                
            </View>
        )
    }
    function onPickOptionPress(option: number, item: z.infer<typeof eventLocationSchema>) {
        navigation.navigate('LocationEvents', {item: item, isAkinaEvents: option == 0})
    }
    function itemPressed(item: z.infer<typeof eventLocationSchema>) {

        let options = [t('home:AkinaEvents'), t("home:EventsTitle", {name: item.name}), t('home:Cancel')]
        showActionSheetWithOptions({
            options: options,
            cancelButtonIndex: 2,
            destructiveButtonIndex: 2,
            showSeparators: true,
            useModal: true,
            textStyle: fonts.green50,
            }, (i?: number) => {
                if ( i!=2 ) {
                    onPickOptionPress(i ?? 0 , item)

                }
        }); 

    }
    function renderItem({item}) {

        return (
            <TouchableOpacity onPress={() => itemPressed(item)}>
                <View style={[gutters.marginTop_16]}>
                    <View style={[ gutters.marginHorizontal_16,{height: 221}]}>
                        <AKFastImage
                            uri={item.thumbnail}
                            style={[layout.flex_1,{borderTopRightRadius: 25, borderTopLeftRadius: 25}]}
                        />
                    </View>
                    <View style={[gutters.marginHorizontal_16, gutters.marginVertical_16]}>
                        <Text style={[fonts.bold]}>{item.name}</Text>
                        <Text>{item.sub_title}</Text>

                    </View>
                </View>

            </TouchableOpacity>
            
        )
    }
    return (
        <View>
            <Spinner
                visible={isLoading}
            />
            <FlatList 
                ListHeaderComponent={headerContent}
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{ paddingBottom: 20}} 
                data={data?.locations}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
            />
        

        </View>
    )

}
export default LocationListing

