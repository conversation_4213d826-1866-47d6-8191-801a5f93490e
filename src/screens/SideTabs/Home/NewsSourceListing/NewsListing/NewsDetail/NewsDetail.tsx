import { ImageVariant } from "@/components/atoms";
import { AddCommentSection, BlogDetailTopSection, CommentCell } from "@/components/molecules";
import newsDetail from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { RouteProp, useRoute } from "@react-navigation/native";
import { useQuery } from "@tanstack/react-query";
import { MutableRefObject, useRef } from "react";
import { Linking, Text, TouchableOpacity, View } from "react-native"
import AkinaLogo from "@/theme/assets/images/AkinaLogo.png"
import { getProfile } from "@/services/users";
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import { Comment } from "@/types/models/comment";
import { useTranslation } from "react-i18next";
import { withComments } from "@/components/hoc/withComments/withComments";
import { useCustomRoute } from "@/hooks/useCustomRoute";
type Props = { 
    commentRef: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>
    onDeleteComment: (comment: Comment) => void
    onCommentReply: (commentId: number, reply: string) => void
    onCommentSend: (text: string, isEdit: boolean, comment?: Comment, replyRef?: MutableRefObject< {editComment: (comment: Comment) => void, makeCommentFirstResponsder: () => void}>) => void
    onCommentButton: () => void
    onLikeButtonPressed: () => void
    onBookmarkButtonPressed: () => void
}
function NewsDetail({ commentRef, onDeleteComment, 
    onCommentReply, onCommentSend, onCommentButton,
    onLikeButtonPressed, onBookmarkButtonPressed }: Props) {
    const { t } = useTranslation(['home']);
    // const route = useRoute<RouteProp<ApplicationStackParamList, 'NewsDetail'>>();
    const route = useCustomRoute();
    const flatListRef = useRef<KeyboardAwareFlatList>(null)
    let heightOfHeader = 0

    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        colors
	} = useTheme();
    const newsDetailResponse = useQuery({
        queryKey: [`newsDetail${route.params?.item.id}`],
        queryFn: () => newsDetail(route.params?.item.module_id, route.params?.item!.news_source_id!, route.params?.item.id)
    })

    function onEditComment(comment: Comment) {
        if (commentRef.current != null) {
            commentRef?.current.editComment(comment)
        }
        setTimeout(() => {
            if (commentRef.current != null) {
                commentRef.current.makeCommentFirstResponsder()
            }
            if (flatListRef.current != null) {
                flatListRef?.current.scrollToPosition(0, heightOfHeader - 250)
            }
        }, 200)
    }

    function headerContent() {
        return (
            <View>
                <BlogDetailTopSection 
                    isSaveSelected={newsDetailResponse.data?.is_bookmarked ?? false} 
                    isLikeSelected={newsDetailResponse.data?.is_liked ?? false} 
                    image={newsDetailResponse.data?.thumbnail}
                    onLikeButton={onLikeButtonPressed}
                    onSaveButton={onBookmarkButtonPressed}
                    onCommentButton={onCommentButton}
                />
                <TouchableOpacity onPress={() => Linking.openURL(newsDetailResponse?.data?.url ?? '')}>
                    <View style={[layout.row, gutters.marginHorizontal_16]}>
                        <View style={[backgrounds.red500, layout.justifyCenter, layout.itemsCenter, {height: 64, width: 64 ,borderRadius: 32,} ]}>
                            <ImageVariant
                                source={AkinaLogo}
                                style={ [ {tintColor:  colors.yellow, resizeMode: 'contain'  , width: 40, height: 40}]}
                            />
                        </View>
                        <View style={[gutters.marginHorizontal_12, layout.flex_1, layout.justifyCenter]}>
                            <Text style={[fonts.Medium]}>{newsDetailResponse.data?.title}</Text>
                        </View>

                    </View>
                </TouchableOpacity>
                <View style={[gutters.marginHorizontal_32, gutters.marginTop_16, ]}>
                    <Text style={[fonts.Medium]}>{newsDetailResponse.data?.description}</Text>
                </View>
                <View style={[gutters.marginHorizontal_32, gutters.marginTop_16, ]}>
                    <TouchableOpacity onPress={() => Linking.openURL(newsDetailResponse?.data?.url ?? '')}>
                        <Text style={[fonts.orange, fonts.Medium, {textAlign: 'right'}]}>{t('home:ReadFullArticle', {path: route.params?.sourceName})}</Text>
                    </TouchableOpacity>
                </View>
                <AddCommentSection ref={commentRef}  onSend={onCommentSend} image={data?.user_profile.profile_photo}/>
            </View>
        )
    }
    function renderItem({item}) {
        let comment: Comment = item
        return (
            <CommentCell 
                comment={comment} 
                onCommentReply={onCommentReply}
                onEditComment={onEditComment}
                onDeleteComment={onDeleteComment}
            />
        )
    }
    return (
        <View style={[layout.flex_1]}>
            <View style={[layout.itemsCenter,backgrounds.black, layout.justifyEnd ,{height: 176, borderBottomLeftRadius: 88, marginTop: -88}]}>
                <View style={[layout.justifyCenter, layout.itemsCenter, {height: 88}]}>
                    <Text  numberOfLines={1} adjustsFontSizeToFit style={[fonts.gray50,fonts.alignCenter, gutters.marginHorizontal_32, fonts.Medium, {fontSize: 70}]}>{route.params?.sourceName}</Text>
                </View>
            </View>
            <View style={[layout.flex_1]}>
                <KeyboardAwareFlatList
                    ref={flatListRef}
                    ListHeaderComponent={headerContent}
                    showsVerticalScrollIndicator={false} 
                    contentContainerStyle={[, { paddingBottom: 20}]} 
                    data={newsDetailResponse.data?.comments}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    keyboardShouldPersistTaps={'handled'}
                />
            </View>
            

            
        </View>
    )

}

export default withComments(NewsDetail, 'news');
