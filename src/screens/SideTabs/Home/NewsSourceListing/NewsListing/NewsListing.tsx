import newsListing from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { newsSchema } from "@/types/schemas/newsListing";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useCallback, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActivityIndicator, FlatList, RefreshControl, Text, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image";
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
import { BlurView } from "@react-native-community/blur";

export type NewsListingNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'NewsDetail'
>;
function NewsListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'NewsListing'>>();
    const navigation = useNavigation<NewsListingNavigationProp>()
    const { t } = useTranslation(['home']);

    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const [isRefreshing, setIsRefreshing] = useState(false);
    const {data, isLoading, refetch ,fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
        queryKey: [`newsSources${route.params.item.module_id}/news/${route.params.item.id}`],
        initialPageParam: 1,
        queryFn: ({pageParam}) =>  {
            console.log("Page Param: ;", pageParam)
            return newsListing(route.params.item.module_id, route.params.item.id, pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.news.current_page != lastPage.news.last_page ?  lastPage.news.current_page + 1 : undefined,
    });
    const flattenData = useMemo(() => {
        return data?.pages.flatMap(page => page.news.data) || [];
    }, [data?.pages]);
    function itemPressed(item: z.infer<typeof newsSchema>) {
        navigation.navigate('NewsDetail', {item: item, sourceName: route.params.item.name})
    }
    const loadNext = useCallback(() => {
        hasNextPage && fetchNextPage();
      }, [fetchNextPage, hasNextPage]);
    
      const onRefresh = useCallback(() => {
        if (!isRefreshing) {
          setIsRefreshing(true);
          refetch()
            .then(() => setIsRefreshing(false))
            .catch(() => setIsRefreshing(false));
        }
      }, [isRefreshing, refetch]);
    function renderItem({item}: any) {
        return (
            <TouchableOpacity onPress={() => itemPressed(item)}>
                    <View style={[ gutters.marginTop_16, borders.rounded_16 ,gutters.marginHorizontal_16,{height: 102, overflow: 'hidden'}]}>
                        <FastImage resizeMode="cover" style={[ ,borders.rounded_4 ,{height: 102 ,backgroundColor: 'transparent'} ]} source={{uri: item?.thumbnail}}/>
                        <BlurView blurType="dark" blurAmount={40}  style={{height: 102, left: 0, right: 0, flex: 1,  position: 'absolute'}}/>
                        <View style={[layout.itemsCenter,layout.justifyCenter, layout.absolute, layout.flex_1, {height: 102}]}>
                            <Text style={[gutters.marginHorizontal_16, fonts.size_16 ,fonts.bold, fonts.gray50 ,{ }]}>{item?.title}</Text>
                        </View>
                    </View>
            </TouchableOpacity>
        )
    }
    return (
        <View>
            <Spinner
                visible={isLoading}
            />
            <View style={[layout.itemsCenter,backgrounds.black, layout.justifyEnd ,{height: 176, borderBottomLeftRadius: 88, marginTop: -88}]}>
                <View style={[layout.justifyCenter, layout.itemsCenter, {height: 88}]}>
                    <Text  numberOfLines={1} adjustsFontSizeToFit style={[fonts.gray50,fonts.alignCenter, gutters.marginHorizontal_32, fonts.Medium, {fontSize: 70}]}>{route.params.item.name}</Text>
                </View>
            </View>
            <FlatList 
                showsVerticalScrollIndicator={false} 
                contentContainerStyle={{ paddingBottom: 20}} 
                data={flattenData}
                keyExtractor={
                    (_, index)=> index.toString()
                }
                renderItem={renderItem}
                ListEmptyComponent={
                    <View style={[layout.row, layout.flex_1, gutters.marginHorizontal_0, layout.itemsCenter, layout.justifyCenter]}>
                        <Text>{t('home:NoResult')}</Text>
                    </View>
                }
                refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                onEndReached={loadNext}
                removeClippedSubviews={true}
                ListFooterComponent={
                    <View style={[layout.row,layout.justifyCenter, layout.itemsCenter ,{height: 100}]}>
                      {isFetchingNextPage && <ActivityIndicator />}
                    </View>
                }            
            />
        </View>
    )
}
export default NewsListing