import newsSources from "@/services/home/<USER>";
import { useTheme } from "@/theme";
import { ApplicationStackParamList } from "@/types/navigation";
import { newsSourceSchema } from "@/types/schemas/newsSources";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useQuery } from "@tanstack/react-query";
import { FlatList, ScrollView, SectionList, TouchableOpacity, View } from "react-native"
import FastImage from "react-native-fast-image";
import { TypeOf, z } from "zod";

export type NewsSourceListingNavigationProp = NativeStackNavigationProp<
    ApplicationStackParamList,
    'NewsListing'
>;
function NewsSourceListing() {
    const route = useRoute<RouteProp<ApplicationStackParamList, 'NewsSourceListing'>>();
    const navigation = useNavigation<NewsSourceListingNavigationProp>()
    const {
		layout,
		gutters,
		backgrounds,
        fonts,
        borders,
        colors
	} = useTheme();
    const {data, isLoading } = useQuery({
        queryKey: [`newsSources${route.params.item.id}`],
        queryFn: () =>  newsSources(route.params.item.id)
    });
    function itemPressed(item: z.infer<typeof newsSourceSchema>) {
        navigation.navigate('NewsListing', {item: item})
    }
    function renderItem({item}) {

        return (
            <TouchableOpacity style={[{flex: 1/2}, gutters.marginVertical_12, gutters.marginHorizontal_16]} onPress={() => itemPressed(item)}>
                                {/* <View style={[borders.rounded_16,{aspectRatio: 1, overflow: 'hidden'} ,backgrounds.green50, {marginVertical: 19}, gutters.marginHorizontal_0]}> */}

                <View style={[,borders.rounded_16,{aspectRatio: 1, overflow: 'hidden', borderBottomLeftRadius: 60} ,backgrounds.green50 ]}>
                    <FastImage style={[layout.absolute,layout.flex_1, borders.rounded_4, backgrounds.green50, {width: '100%', height: '100%'} ]} source={{uri: item?.thumbnail}}/>
                    
                </View>
                
                
            </TouchableOpacity>
        )

    }
    
    return (
        <View>
            <FlatList
                data={data?.news_sources}
                renderItem={renderItem}
                numColumns={2}
                showsVerticalScrollIndicator={false}
            />
        </View>
    )
}
export default NewsSourceListing