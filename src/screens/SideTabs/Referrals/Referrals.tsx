import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView"
import { Text, TouchableOpacity, View, Clipboard, ActivityIndicator } from "react-native"
import { useNavBar } from "@/hooks/useNavBar";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTheme } from "@/theme";
import { colorTokens } from "@/theme/colorTokens";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import ShareIcon from "@/theme/assets/images/ShareIcon.png";
import ChevronRightIcon from "@/theme/assets/images/ChevronRightIcon.png";
import { useCustomToast } from "@/hooks/useCustomToast";
import { ReferralsListingBottomSheet } from "@/components/molecules";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { useReferralLink } from "@/hooks/useReferralLink";
import getReferredUsers from "@/services/users/getReferredUsers";
import basicProfile from "@/services/users/basicProfile";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import ChainOverHand from "@/theme/assets/images/ChainOverHand.png";

function Referrals() {

    const { setupNavBar } = useNavBar('Referrals', false, true);

    const showToast = useCustomToast();
    const { shareReferralLink, getReferralLink, isLoading } = useReferralLink();

    const { layout, gutters, fonts, borders } = useTheme();
    const c = colorTokens();

    const [isCopied, setIsCopied] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);

    const profileResponse = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile,
    });

    const referredUsersResponse = useInfiniteQuery({
        queryKey: ['referred-users'],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            return getReferredUsers(profileResponse?.data?.user?.id, pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,
    });

    const flattenData = useMemo(() => {
        return referredUsersResponse.data?.pages.flatMap(page => page.data) || [];
    }, [referredUsersResponse.data, referredUsersResponse.isRefetching]);

    const loadNext = useCallback(() => {
        referredUsersResponse.hasNextPage && referredUsersResponse.fetchNextPage();
    }, [referredUsersResponse.fetchNextPage, referredUsersResponse.hasNextPage]);

    useEffect(() => {
        setupNavBar();
    }, [])

    function onPressShareLink() {
        shareReferralLink();
    }

    function onPressCopy() {
        Clipboard.setString(getReferralLink());
        setIsCopied(true);
        showToast('Copied to clipboard!');
    }

    function getGenerateLinkButton(): JSX.Element {
        return (
            <TouchableOpacity onPress={onPressShareLink} disabled={isLoading} style={[layout.row, layout.itemsCenter, layout.justifyCenter, gutters.paddingVertical_8, gutters.marginTop_4, borders.rounded_8, { gap: 8, backgroundColor: c.fill.bold.neutrals.rest, width: 148 }]}>
                {
                    isLoading && (
                        <ActivityIndicator color={c.custom.white} /> 
                    )
                }
                {
                    !isLoading && (
                        <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, { color: c.content.onBold.default.default }]}>Generate Link</Text>
                    )
                }
            </TouchableOpacity>
        )
    }

    return (
        <AKSafeAreaView>
            {
                profileResponse?.data?.share_able_referral_code == null ? (
                    <EmptyDataView
                        heading="Generate Referral Link"
                        desc="Create your customized link to spread the word and grow your network."
                        image={ChainOverHand}
                        actionButton={getGenerateLinkButton}
                    />
                ) : (
                    <View style={[gutters.marginHorizontal_16, gutters.marginTop_20]}>
                        <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: c.content.default.default }]}>Your custom referral link</Text>
                        <View style={[layout.row, layout.itemsCenter, gutters.paddingHorizontal_16, gutters.paddingVertical_12, gutters.marginTop_12, gutters.marginBottom_16, borders.rounded_8, { backgroundColor: c.background.default.neutrals.secondary }]}>
                            <View style={[layout.flex_1]}>
                                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: c.content.default.subdued }]}>{getReferralLink()}</Text>
                            </View>
                            <TouchableOpacity onPress={onPressCopy} disabled={isCopied} style={[gutters.marginLeft_16]}>
                                <Text style={[fonts.fontSizes.utility.sm, fonts.lineHeight.utility.sm, fonts.SemiBold, { color: isCopied ? c.content.default.subdued : c.content.default.default }]}>{isCopied ? 'Copied!' : 'Copy'}</Text>
                            </TouchableOpacity>
                        </View>
                        <TouchableOpacity disabled={isLoading} onPress={onPressShareLink} style={[layout.row, layout.justifyCenter, layout.itemsCenter, gutters.paddingVertical_12, borders.rounded_8, {backgroundColor: c.fill.bold.neutrals.rest}]}>
                            {
                                isLoading && (
                                    <ActivityIndicator color={c.custom.white} /> 
                                )
                            }
                            {
                                !isLoading && (
                                    <AKIcon source={ShareIcon} size={20} styles={[gutters.marginRight_12]}/>
                                )
                            }
                            {
                                !isLoading && (
                                    <Text style={[fonts.fontSizes.utility.lg, fonts.lineHeight.utility.lg, fonts.SemiBold, {color: c.content.onBold.default.default}]}>Share Referral Link</Text>
                                )
                            }
                        </TouchableOpacity>
                        <View style={[gutters.marginVertical_24, { height: 1, backgroundColor: c.stoke.default.default }]} />
                        <TouchableOpacity disabled={!flattenData.length} onPress={() => setIsModalVisible(true)} style={[layout.row, layout.itemsCenter, gutters.padding_16, borders.rounded_8, {
                                backgroundColor: 'white',
                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 1,
                                    height: 1,
                                },
                                shadowOpacity: 0.2,
                                shadowRadius: 3 }]}>
                            <View style={[{ gap: 8, flex: 1 }]}>
                                <Text style={[fonts.fontSizes.headings.H4, fonts.lineHeight.headings.H4, fonts.Bold, { color: c.content.default.emphasis }]}>Your Referrals</Text>
                                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, { color: c.content.default.subdued }]}>{referredUsersResponse.data?.pages[0]?.total} members joined</Text>
                            </View>
                            <AKIcon source={ChevronRightIcon} size={20} styles={[gutters.marginLeft_12]}/>
                        </TouchableOpacity>
                    </View>
                )
            }
            <ReferralsListingBottomSheet
                isVisible={isModalVisible}
                setIsVisible={setIsModalVisible}
                loadNext={loadNext}
                flattenData={flattenData}
                isFetchingNextPage={referredUsersResponse.isFetchingNextPage}
            />
        </AKSafeAreaView>
    )
}
export default Referrals