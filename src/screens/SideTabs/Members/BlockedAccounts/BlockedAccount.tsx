import { useTheme } from "@/theme";
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react"
import { ActivityIndicator, Alert, FlatList, RefreshControl, Text, TouchableOpacity, View } from "react-native"
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
import _Icon from 'react-native-vector-icons/FontAwesome';

import { useTranslation } from "react-i18next";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import ChevronLeftIcon from "@/theme/assets/images/SocialConnect/ChevronLeftIcon.png";
import AddIcon from "@/theme/assets/images/AddIcon.png";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import { colorTokens } from "@/theme/colorTokens";
import SearchBar from "@/components/atoms/SeacrhBar/SeacrhBar";
import MemberItem from "@/components/atoms/MemberItem/MemberItem";
import { useNavigation } from "@react-navigation/native";
import InviteMemberBottomSheet from "@/components/molecules/InviteMemberBottomSheet/InviteMemberBottomSheet";
import { getProfile } from "@/services/users";
import MembersActionBottomSheet from "@/components/molecules/MembersActionBottomSheet/MembersActionBottomSheet";
import getUserFollowings from "@/services/socialConnect/users/getUserFollowings";
import BlockBottomSheet from "@/components/molecules/BlockBottomSheet/BlockBottomSheet";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import BlockedUser from "@/theme/assets/images/BlockedUser.png";
import MemberTypeTab from "@/components/atoms/MemberTypeTab/MemberTypeTab";
import basicProfile from "@/services/users/basicProfile";
import { ImageVariant } from "@/components/atoms";
import CrossIconNav from "@/theme/assets/images/CrossIconNav.png"
import getBlockUsers from "@/services/socialConnect/users/getBlockUsers";
import { basicUserSchema, blockedSchema } from "@/types/schemas/user";
import unBlockUser from "@/services/socialConnect/users/unBlockUser";
import { useCustomToast } from "@/hooks/useCustomToast";

function BlockedAccounts() {
    const queryClient = useQueryClient();
    const { t } = useTranslation(['sideTabs']);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [selectedBlockedAccount, setSelectedBlockedAccount] = useState<z.infer<typeof basicUserSchema> | null>(null)
    // const blockedUsersResponse = useInfiniteQuery({
    //     queryKey: [`blocked-users`],
    //     initialPageParam: 1,
    //     queryFn:  ({pageParam}) =>  {
    //         console.log("Page Param: ;", pageParam)
    //         return getBlo
    //     },
    //     getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,

    // });
    const blockedUsersResponse = useQuery({
        queryKey: ['blocked-users'],
        queryFn: getBlockUsers
    })
    // const flattenData = useMemo(() => {
    //     return userListingResponse.data?.pages.flatMap(page => page.data) || [];
    // }, [userListingResponse.data, userListingResponse.isRefetching]);
    
    const {
        layout,
        gutters,
        borders,
        fonts,
    } = useTheme();

    const c = colorTokens();
    const navigation = useNavigation();
    const showToast = useCustomToast();
    const [isLoading, setIsLoading] = useState(false)
    useEffect(() => {
        setupNavBar()
    },[])
    const unblockUserMutation = useMutation(
        {
            mutationFn: (id: number) => unBlockUser(id),
            onSuccess: (response) => {
                setIsLoading(false)
                showToast('Member Unblocked Successfully');  
                removeCachedDataOnSuccess()
                // blockedUsersResponse.refetch()
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
            }
        }
    );

    function removeCachedDataOnSuccess() {
        queryClient.setQueryData([`blocked-users`],
            (cacheData: any) => {
            if (cacheData != null) {
                let blockedAccounts = cacheData?.blocked_users.filter(((blocked: z.infer<typeof blockedSchema>) => blocked.user.id !== selectedBlockedAccount?.id))
                return {
                    ...cacheData,
                    blocked_users: blockedAccounts
                }
            }
            setSelectedBlockedAccount(null)
        });
        
    }
    
    function setupNavBar() {
        navigation.setOptions({
            title: 'Blocked Accounts',
            headerStyle: {
               backgroundColor: c.background.default.neutrals.default,
            } ,
            headerTitleStyle: [
               fonts.SemiBold,
               fonts.fontSizes.body.sm, 
               {color: c.content.default.emphasis}
            ],
            headerShown: true, 
            headerLeft: headerCrossButton, 
       })
    }
    function headerCrossButton() {
        return (
            <TouchableOpacity 
                onPress={() => navigation.goBack()}
                style={{ marginLeft: 10 }}
            >
                <ImageVariant  source={CrossIconNav} style={{height: 40, width: 40, resizeMode: 'contain'}}/>
            </TouchableOpacity>
        );
    }
    
    function renderItem({item}: { item: z.infer<typeof blockedSchema> }): JSX.Element {

        const UnblockButton = (
            <TouchableOpacity onPress={() => onPressUnblock(item.user)} style={[gutters.paddingHorizontal_12, borders.rounded_8, { paddingVertical: 6, backgroundColor: c.fill.bold.neutrals.rest }]}>
                <Text style={[ fonts.fontSizes.utility.xs, fonts.SemiBold, fonts.lineHeight.utility.xs, {color: c.content.onBold.default.default}]}>Unblock</Text>
            </TouchableOpacity>
        );
        return (
            <MemberItem item={item.user} rightView={UnblockButton} isFromBlocked={true}/>
        )
    }

    function onPressUnblock(item: z.infer<typeof basicUserSchema>) {
        const fullName = `${item?.first_name} ${item?.last_name}`
        let options = [
            {
                text: 'Cancel',
            },
            {
                text: 'Unblock',
                style: 'destructive',
                onPress: () => {
                    setSelectedBlockedAccount(item)
                    unblockUserMutation.mutate(item.id)
                }
            },
        ]
        Alert.alert(`Unblock ${fullName}?`, 'They’ll be able to send you request and see your content again. They won’t be notified that you unblocked them.', options)
    }

    function ListEmptyComponent(): JSX.Element | null {
        return !blockedUsersResponse?.isLoading ? (
            <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter, gutters.marginBottom_32]}>
                <EmptyDataView
                    heading="No blocked accounts"
                    desc="You haven’t blocked anyone yet. When you do, they’ll appear here."
                    image={BlockedUser}
                />
            </View>
        ) : null;
    }

    // const loadNext = useCallback(() => {
    //     userListingResponse.hasNextPage && userListingResponse.fetchNextPage();
    // }, [userListingResponse.fetchNextPage, userListingResponse.hasNextPage]);
    
    const onRefresh = useCallback(() => {
        if (!isRefreshing) {
            setIsRefreshing(true);
            blockedUsersResponse.refetch()
            .then(() => setIsRefreshing(false))
            .catch(() => setIsRefreshing(false));
        }
    }, [isRefreshing, blockedUsersResponse.refetch]);

    return (
        <AKSafeAreaView>
            <Spinner
                visible={blockedUsersResponse?.isLoading || isLoading}
            />
            <View style={[ layout.flex_1,gutters.marginHorizontal_16]}>
                <FlatList 
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={[gutters.marginTop_12, {gap: 8, flexGrow: 1}]}
                    data={blockedUsersResponse.data?.blocked_users}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                    // onEndReached={loadNext}
                    removeClippedSubviews={true}
                    ListEmptyComponent={ListEmptyComponent}
                    // ListFooterComponent={
                    //     <View style={[layout.row,layout.justifyCenter, layout.itemsCenter]}>
                    //         {userListingResponse.isFetchingNextPage && <ActivityIndicator />}
                    //     </View>
                    // }
                />
            </View>
        </AKSafeAreaView>
    )
}

export default BlockedAccounts;
