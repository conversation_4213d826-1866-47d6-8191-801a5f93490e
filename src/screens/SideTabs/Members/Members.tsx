import { useTheme } from "@/theme";
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react"
import { ActivityIndicator, FlatList, RefreshControl, Text, TouchableOpacity, View } from "react-native"
import Spinner from "react-native-loading-spinner-overlay";
import { z } from "zod";
import _Icon from 'react-native-vector-icons/FontAwesome';

import { useTranslation } from "react-i18next";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import ChevronLeftIcon from "@/theme/assets/images/SocialConnect/ChevronLeftIcon.png";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import { colorTokens } from "@/theme/colorTokens";
import SearchBar from "@/components/atoms/SeacrhBar/SeacrhBar";
import MemberItem from "@/components/atoms/MemberItem/MemberItem";
import { useNavigation, useRoute } from "@react-navigation/native";
import InviteMemberBottomSheet from "@/components/molecules/InviteMemberBottomSheet/InviteMemberBottomSheet";
import { getProfile } from "@/services/users";
import MembersActionBottomSheet from "@/components/molecules/MembersActionBottomSheet/MembersActionBottomSheet";
import getUserFollowings from "@/services/socialConnect/users/getUserFollowings";
import BlockBottomSheet from "@/components/molecules/BlockBottomSheet/BlockBottomSheet";
import EmptyDataView from "@/components/molecules/EmptyDataView/EmptyDataView";
import FlowerOverHand from "@/theme/assets/images/FlowerOverHand.png";
import MemberTypeTab from "@/components/atoms/MemberTypeTab/MemberTypeTab";
import getUserFollowers from "@/services/socialConnect/users/getUserFollowers";

function Members() {

    const { t } = useTranslation(['sideTabs']);

    const route = useRoute();
    const { userId, isOwnProfile } = route.params;

    const [selectedTab, setSelectedTab] = useState<string>("Followers");
    const [selectedMember, setSelectedMember] = useState<z.infer<typeof socialConnectUserSchema> | null>(null);
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [isActionModalVisible, setIsActionModalVisible] = useState<boolean>(false);
    const [isBlockModalVisible, setIsBlockModalVisible] = useState<boolean>(false);
    const [isRefreshing, setIsRefreshing] = useState(false);

    const followingListingResponse = useInfiniteQuery({
        queryKey: [`${userId}-followings`],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            console.log("Page Param: ;", pageParam)
            return getUserFollowings(userId, pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,

    });
    const followersListingResponse = useInfiniteQuery({
        queryKey: [`${userId}-followers`],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            console.log("Page Param: ;", pageParam)
            return getUserFollowers(userId, pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,

    });
    const flattenData = useMemo(() => {
        if (selectedTab == 'Followers') {
            return followersListingResponse.data?.pages.flatMap(page => page.data) || [];
        } else {
            return followingListingResponse.data?.pages.flatMap(page => page.data) || [];
        }
    }, [followingListingResponse.data, followingListingResponse.isRefetching, followersListingResponse.data, followersListingResponse.isRefetching, selectedTab]);

    console.log(JSON.stringify(flattenData[0], null, 4));
    
    const {
        layout,
        gutters,
        borders,
        fonts,
    } = useTheme();

    const c = colorTokens();
    const navigation = useNavigation();
    useEffect(() => {
        setupNavBar()
    }, [])
    function setupNavBar() {
        navigation.setOptions({
            title: 'Members',
            headerTitleAlign: 'center',
            headerStyle: {
               backgroundColor: c.background.default.neutrals.default,
            } ,
            headerTitleStyle: [
               fonts.SemiBold,
               fonts.fontSizes.body.sm, 
               {color: c.content.default.emphasis}
            ],
            headerTintColor: c.content.default.default,
            headerShown: true, 
            headerShadowVisible: false,
       })
    }
    function renderItem({item}: { item: z.infer<typeof socialConnectUserSchema> }): JSX.Element {
        return (
            <MemberItem item={item} {...(isOwnProfile && { onPressMore: onPressMore })} />
        )
    }

    function ListEmptyComponent(): JSX.Element | null {
        return !followingListingResponse?.isLoading ? (
            <View style={[layout.flex_1]}>
                <EmptyDataView
                    heading="No Members Added"
                    desc="Reach out to people you’d like to connect with."
                    image={FlowerOverHand}
                />
            </View>
        ) : null;
    }

    const loadNext = useCallback(() => {
        if (selectedTab == 'Followers') {
            followersListingResponse.hasNextPage && followersListingResponse.fetchNextPage();
        } else {
            followingListingResponse.hasNextPage && followingListingResponse.fetchNextPage();
        }
    }, [followingListingResponse.fetchNextPage, followingListingResponse.hasNextPage, followersListingResponse.fetchNextPage, followersListingResponse.hasNextPage, selectedTab]);
    
    const onRefresh = useCallback(() => {
        if (!isRefreshing) {
            setIsRefreshing(true);
            if (selectedTab == 'Followers') {
                followersListingResponse.refetch()
                .then(() => setIsRefreshing(false))
                .catch(() => setIsRefreshing(false));
            } else {
                followingListingResponse.refetch()
                .then(() => setIsRefreshing(false))
                .catch(() => setIsRefreshing(false));
            }
        }
    }, [isRefreshing, followingListingResponse.refetch]);

    function onPressMore(item: z.infer<typeof socialConnectUserSchema>): void {
        setSelectedMember(item);
        setIsActionModalVisible(true);
    }

    function onPressBar(): void {
        navigation.navigate('MembersSearch');
    }

    function onPressBlock(item: z.infer<typeof socialConnectUserSchema>): void {
        setSelectedMember(item);
        setTimeout(() => {
            setIsBlockModalVisible(true);
        }, 500)
    }

    const tabs = ['Followers', 'Following'];

    return (
        <AKSafeAreaView>
            {/* <Spinner
                visible={followingListingResponse?.isLoading || followersListingResponse?.isLoading}
            /> */}
            
            <View style={[layout.row, gutters.marginTop_12, { borderBottomWidth: 1, borderBottomColor: c.stoke.default.default }]}>
                {
                    tabs.map(tab => {
                        return (
                            <MemberTypeTab
                                key={tab}
                                text={tab}
                                isSelected={tab == selectedTab}
                                badgeCount={tab == 'Followers' ? (followersListingResponse.data?.pages[0]?.total ?? 0) : (followingListingResponse.data?.pages[0]?.total ?? 0)}
                                onPress={(text) => setSelectedTab(text)}
                            />
                        )
                    })
                }
            </View>
            <View style={[layout.flex_1, gutters.marginHorizontal_16]}>
                {/* <View style={[gutters.marginVertical_16]}> // we can add it while implementing members flow
                    <SearchBar onPressBar={onPressBar} placeholderText="Search here..." />
                </View> */}
                <FlatList 
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={[gutters.marginTop_16, {gap: 8, flexGrow: 1,}]}
                    data={flattenData}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />}
                    onEndReached={loadNext}
                    removeClippedSubviews={true}
                    ListEmptyComponent={ListEmptyComponent}
                    ListFooterComponent={
                        <View style={[layout.row,layout.justifyCenter, layout.itemsCenter]}>
                            {(followingListingResponse.isFetchingNextPage || followersListingResponse.isFetchingNextPage) && <ActivityIndicator />}
                        </View>
                    }
                />
            </View>
            <InviteMemberBottomSheet isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} />
            <MembersActionBottomSheet
                isAddedMember={selectedTab=='Followers' ? false : true}
                item={selectedMember}
                userId={userId}
                isModalVisible={isActionModalVisible}
                setIsModalVisible={setIsActionModalVisible}
                onPressBlock={onPressBlock}
            />
            <BlockBottomSheet currentUserId={userId} selectedTab={selectedTab} isModalVisible={isBlockModalVisible} setIsModalVisible={setIsBlockModalVisible} item={selectedMember} />
        </AKSafeAreaView>
    )

}

export default Members;
