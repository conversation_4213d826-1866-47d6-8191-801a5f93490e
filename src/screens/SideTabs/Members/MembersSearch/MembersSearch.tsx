import socialConnectUsers from "@/services/socialConnect/users/socialConnectUsers"
import { useTheme } from "@/theme";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useCallback, useMemo, useState } from "react"
import { ActivityIndicator, FlatList, View } from "react-native"
import _Icon from 'react-native-vector-icons/FontAwesome';
import { isValidEmail } from "@/utils";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import ChevronLeftIcon from "@/theme/assets/images/SocialConnect/ChevronLeftIcon.png";
import AKIcon from "@/components/atoms/AKIcon/AKIcon";
import { colorTokens } from "@/theme/colorTokens";
import SearchBar from "@/components/atoms/SeacrhBar/SeacrhBar";
import MemberItem from "@/components/atoms/MemberItem/MemberItem";
import { useNavigation } from "@react-navigation/native";
import { socialConnectUserSchema } from "@/types/schemas/socialConnectUsers";

function MembersSearch() {

    const { t } = useTranslation(['sideTabs']);
    const [searchText, setSearchText] = useState('')

    const userListingResponse = useInfiniteQuery({
        queryKey: [`social-connect-users`, searchText],
        initialPageParam: 1,
        queryFn:  ({pageParam}) =>  {
            return socialConnectUsers(searchText == '' ? null : {
                search_key: (isValidEmail(searchText) == true) ? 'email' : 'name',
                search_value: searchText
            }, pageParam)
        },
        getNextPageParam: (lastPage, pages) => lastPage.current_page != lastPage.last_page ?  lastPage.current_page + 1 : undefined,
    });

    const {
        layout,
        gutters,
    } = useTheme();

    const c = colorTokens();
    const navigation = useNavigation();

    function renderItem({ item }: { item: z.infer<typeof socialConnectUserSchema> }): JSX.Element {
        const extraInfoText = item?.is_followed ? 'Member' : 'Not a Member';
        return (
            <MemberItem item={item} extraInfoText={extraInfoText} />
        )
    }

    const flattenData = useMemo(() => {
        return userListingResponse.data?.pages.flatMap(page => page.data) || [];
    }, [userListingResponse.data, userListingResponse.isRefetching]);

    const loadNext = useCallback(() => {
        userListingResponse.hasNextPage && userListingResponse.fetchNextPage();
    }, [userListingResponse.fetchNextPage, userListingResponse.hasNextPage]);
    
    return (
        <AKSafeAreaView edges={['top', 'bottom']}>
            <View style={[layout.row, gutters.paddingHorizontal_16, gutters.marginTop_12, layout.itemsCenter, { marginBottom: 28 }]}>
                <AKIcon source={ChevronLeftIcon} tintColor="black" onPress={navigation.goBack} />
                <View style={[layout.flex_1, gutters.marginLeft_12]}>
                    <SearchBar placeholderText="Search here..." value={searchText} setValue={setSearchText} />
                </View>
            </View>
            <View style={[layout.flex_1, gutters.marginHorizontal_16]}>
                <FlatList 
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{gap: 8}}
                    data={flattenData}
                    keyExtractor={
                        (_, index)=> index.toString()
                    }
                    renderItem={renderItem}
                    onEndReached={loadNext}
                    removeClippedSubviews={true}
                    ListFooterComponent={
                        <View style={[layout.row,layout.justifyCenter, layout.itemsCenter]}>
                            {userListingResponse.isFetchingNextPage && <ActivityIndicator />}
                        </View>
                    }
                />
            </View>
        </AKSafeAreaView>
    )
}

export default MembersSearch;
