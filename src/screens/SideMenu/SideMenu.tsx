import { AKFastImage, ImageVariant, SideMenuItem } from "@/components/atoms";
import { useTheme } from "@/theme";
import { DrawerContentScrollView, DrawerItem } from "@react-navigation/drawer";
import { CommonActions, DrawerActions, useNavigation } from "@react-navigation/native";
import { Text, View, FlatList, TouchableOpacity, Alert } from "react-native";
import FastImage from "react-native-fast-image";
import _Icon from 'react-native-vector-icons/FontAwesome';
import LogoDark from '@/theme/assets/images/LogoDark.png'
import { useMutation, useQuery } from "@tanstack/react-query";
import { getProfile } from "@/services/users";
import { storage } from "@/App";
import { useQueryClient } from "@tanstack/react-query";
import { AUTH_STORRAGE_KEYS, FCM_TOKEN_STORAGE_KEY } from "@/utils/constants";
import { useTranslation } from "react-i18next";
import { revokeFcmToken } from "@/integrations/fireabase/messaging";
import { useEffect, useState } from "react";
import logout from "@/services/auth/logout";
import Spinner from "react-native-loading-spinner-overlay";
import { useDrawerStatus } from '@react-navigation/drawer';

const Icon = _Icon as React.ElementType
interface MenuItemType {
    label: string;
    icon: string;
    route: string
}
function SideMenu(props: any) {
    const { t } = useTranslation(['sideMenu']);
    const queryClient = useQueryClient()
    const navigation = useNavigation()

    const drawerStatus = useDrawerStatus();

    const [isLoading, setIsLoading] = useState(false);
    const {
		layout,
		gutters,
		backgrounds,
        colors
	} = useTheme();
    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
        refetchOnWindowFocus: true,
    });
    const menuItems: MenuItemType[] = [ {label: t('sideMenu:Home'), icon: require('@/theme/assets/images/ColorLogo.png'), route: 'SocialHome'},
    {label: t('sideMenu:Favorites'), icon: require('@/theme/assets/images/Star.png'), route: 'Favorites'},
    {label: t('sideMenu:Search'), icon: require('@/theme/assets/images/Search.png'), route: 'Search'},
    {label: 'Members', icon: require('@/theme/assets/images/Search.png'), route: 'Members'},
    {label: 'Blocked Accounts', icon: require('@/theme/assets/images/Search.png'), route: 'BlockedAccounts'},
    // {label: t('sideMenu:Notifications'), icon: require('@/theme/assets/images/BellIcon.png'), route: 'Notifications'},
    {label: 'Ask Akina', icon: require('@/theme/assets/images/Message.png'), route: 'ChatListing'},
    {label: t('sideMenu:Saved'), icon: require('@/theme/assets/images/Home/SaveIcon.png'), route: 'Saved'},
    {label: t('sideMenu:Account'), icon: require('@/theme/assets/images/Settings.png'), route: 'Account'},
    {label: t('sideMenu:Logout'), icon: require('@/theme/assets/images/Account/PrivacyIcon.png'), route: 'Auth'}

    ]

    useEffect(() => {
        if (drawerStatus === 'open') {
            console.log('Drawer is opened');
            queryClient.invalidateQueries(['get_profile']);
        }
    }, [drawerStatus]);

    const logoutMutation = useMutation(
        {
            mutationFn: (token?: string) => logout(token),
            onSuccess: () => {
                let keys = storage.getAllKeys()
                keys.map(key => {
                    if (key != AUTH_STORRAGE_KEYS.USERID) {
                        storage.delete(key)
                    }
                } )
                // storage.clearAll()
                queryClient.removeQueries()
                revokeFcmToken()
                navigation.reset({
                    index: 0,
                    routes: [{name: 'Auth'}]
                })
                setIsLoading(false)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t('home:Error'), error.message)
            }
        },
    );

    function renderItem(item: MenuItemType) {
        async function menuItemTapped(route: string) {
            navigation.dispatch(DrawerActions.closeDrawer());

            if (route == 'Auth') {
                setIsLoading(true);
                const storedToken = await storage.getString(FCM_TOKEN_STORAGE_KEY);
                logoutMutation.mutate(storedToken);
            } else {
                
                navigation.navigate(route, route == 'ChatListing' ? {moduleId: 11} : null)
            }
        }
        return (

            <TouchableOpacity onPress={() => menuItemTapped(item.route)}>
                <SideMenuItem
                    label={item.label}
                    icon={item.icon} 
                    height={80}
                    {...(item.route == 'Notifications' ? { notifCount: data?.user_profile?.notifications_count } : {})} // data?.user_profile?.notifications_count
                />
            </TouchableOpacity>
        )
    }
    return (
        <View style={layout.flex_1}>
            <Spinner
                visible={isLoading}
            />
            <View style={{height: 193}}>
                <AKFastImage
                    uri={data?.user_profile?.profile_photo}
                    style={{height: 193}}
                />
            </View>
            <View style={layout.flex_1}>
                <FlatList
                    data={menuItems}
                    renderItem={({item}) => renderItem(item)}
                    ItemSeparatorComponent={() => (<View style={[layout.flex_1, backgrounds.gray100, gutters.marginHorizontal_16 ,{height: 1} ]}/>)}

                />
               

            </View>
            <View style={[ gutters.marginBottom_32, gutters.marginLeft_32]}>
                <ImageVariant
                    source={LogoDark}
                    style={ [layout.z10, layout.itemsCenter, gutters.marginTop_24]}
                />

            </View>
            
            
        </View>
    )
}

export default SideMenu