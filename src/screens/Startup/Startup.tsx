import { useEffect } from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { useTheme } from '@/theme';
import { SafeScreen } from '@/components/template';

import type { ApplicationScreenProps } from '@/types/navigation';
import { storage } from '@/App';
import { AUTH_STORRAGE_KEYS } from '@/utils/constants';
import FastImage from 'react-native-fast-image';
import { isOnboardingShown, isUserLoggedIn } from '@/utils/utility';
import { getFcmToken, registerNotificationForegroundHandler } from '@/integrations/fireabase/messaging';
import { FCM_TOKEN_STORAGE_KEY } from '@/utils/constants';
import storeFcmToken from '@/services/users/storeFcmToken';
import { useHandleTokenUpdate } from '@/hooks/useHandleTokenUpdate';
import { colorTokens } from '@/theme/colorTokens';

function Startup({ navigation }: ApplicationScreenProps) {
	const { layout, gutters, fonts, colors, backgrounds } = useTheme();
	const c = colorTokens()
	const { t } = useTranslation(['startup']);
	const handleTokenUpdate = useHandleTokenUpdate()
	const { isSuccess, isFetching, isError } = useQuery({
		queryKey: ['startup'],
		queryFn: () => {
			return Promise.resolve(false);
		},
	});
	

	useEffect(() => {
		if (!isSuccess) {
			
			return
		}
		if (isUserLoggedIn()) {
			handleTokenUpdate();
			registerNotificationForegroundHandler();
			navigation.reset({
				index: 0,
				routes: [{name: 'Main'}]
			})
			return
		}

		navigation.reset({
			index: 0,
			routes: [{name: 'NewLogin'}]
		})
		if (isOnboardingShown() == false) {
			setTimeout(() => {
				storage.set(AUTH_STORRAGE_KEYS.ONBOARDINGSHOWN, true)
				navigation.navigate('OnBoardingSignUp')
			}, 1000);
		}
		

	}, [isSuccess]);

	return (
			<View
				style={[
					layout.flex_1,
					layout.col,
					layout.itemsCenter,
					layout.justifyCenter,
					{backgroundColor: c.fill.bold.primary.rest}
					
				]}
			>
				<FastImage
                    defaultSource={require('@/theme/assets/images/SplashLogo.png')}
					source={require("@/theme/assets/images/SplashLogo.png")}
                    resizeMode={FastImage.resizeMode.cover}
                    style={[ layout.flex_1, {width: 179, height: 52}, layout.absolute]}
                />
				{isFetching && (
					<ActivityIndicator size="large" style={[gutters.marginVertical_24]} />
				)}
				{isError && (
					<Text style={[fonts.size_16, fonts.red500]}>
						{t('startup:error')}
					</Text>
				)}
			</View>
	);
}

export default Startup;
