import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>tField, ImageVariant } from "@/components/atoms";
import { getProfile, profileOptions } from "@/services/users";
import updateProfile from "@/services/users/updateProfile";
import { useTheme } from "@/theme";
import { User } from "@/types/models";
import { OnBoardingStackParamList } from "@/types/navigation";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { useMutation, useQuery } from "@tanstack/react-query";
import _ from "lodash";
import moment from "moment";
import { useEffect, useState } from "react";
import { getI18n, useTranslation } from "react-i18next";
import { Alert, KeyboardAvoidingView, Text, TouchableOpacity, View } from "react-native";
import DatePicker from "react-native-date-picker"
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Spinner from "react-native-loading-spinner-overlay";
import { colorTokens } from "@/theme/colorTokens";

export type PersonalInfoScreenNavigationProp = NativeStackNavigationProp<
    OnBoardingStackParamList,
    'BasicInfo'
>;
function PersonalInfo() {
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();

    const c = colorTokens();

    const { t } = useTranslation(['profile']);
    const navigation = useNavigation<PersonalInfoScreenNavigationProp>();
    const [openDatePicker, setOpenDatePicker] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const updateMutation = useMutation(
        {
            mutationFn: (newUser: any) => updateProfile(newUser),
            onSuccess: (response) => {
                setIsLoading(false)
                navigation.navigate('BasicInfo')
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );
    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile,
    });
    const [form, setForm] = useState({
        birthdate: '',
        nickname: '',
        address: '',
        phoneNumber: '',
        bio: ''
    })
    const [errors, setErrors] = useState({
        birthdate: null,
        nickname: null,
        address: null,
        phoneNumber: null,
        bio: null
    })
    useEffect(() => {
        setForm({..._.set(form, 'birthdate', data?.user_profile?.date_of_birth)});
        setForm({..._.set(form, 'nickname', data?.user_profile?.nick_name)});
        setForm({..._.set(form, 'address', data?.user_profile?.address)});
        setForm({..._.set(form, 'phoneNumber', data?.user_profile?.phone_number)});
        setForm({..._.set(form, 'bio', data?.user_profile?.bio)});

    }, [data])
    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});
    }
    function nextButtonPressed() {
        let data: any = {}
        if (form.address != '' && form.address != null) {
            data.address = form.address
        }
        if (form.birthdate != '' && form.birthdate != null) {
            data.date_of_birth = form.birthdate
        }
        if (form.phoneNumber != '' && form.phoneNumber != null) {
            data.phone_number = form.phoneNumber
        }
        if (form.nickname != '' && form.nickname != null) {
            data.nick_name = form.nickname
        }
        if (form.bio != '' && form.bio != null) {
            data.bio = form.bio
        }
        if (data == null) {
            navigation.navigate('BasicInfo')
        } else {
            console.log("DATA:::::::", data)
            data['_method'] = 'PUT'
            setIsLoading(true)
            const formData = new FormData();
            Object.keys(data).forEach((key) => {
                formData.append(key, data[key]);
            });
            updateMutation.mutate(formData)
        }

    }
    function skipButtonTapped() {
        navigation.goBack()
    }
    
    return (
        <KeyboardAwareScrollView style={[layout.flex_1, backgrounds.white]}>
            <Spinner
                visible={isLoading}
            />
            <View style={[gutters.marginHorizontal_16]}>
                <Text style={[fonts.black, fonts.bold ,fonts.size_24, layout.justifyCenter, fonts.alignCenter, gutters.marginTop_24]}>{t("profile:OnBoardingTitle")}</Text>
                <Text style={[fonts.black, fonts.size_16, layout.justifyCenter, fonts.alignCenter, gutters.marginTop_24]}>{t("profile:OnBoardingSubtitle")}</Text>
                <Text style={[fonts.black, fonts.size_16, layout.justifyCenter, fonts.alignCenter]}>{t("profile:Steps", {currentStep: 1, totalSteps: 3})}</Text>
            </View>
            
            <View style={[gutters.marginHorizontal_16, gutters.marginTop_16]}>
                <TouchableOpacity onPress={() => {
                        setOpenDatePicker(true)
                        }}>
                    <View style={[gutters.marginTop_16, {pointerEvents: 'none'}]}>
                        <AKTextField value={form.birthdate} error={errors.birthdate} name='birthdate' label={t("profile:Birthdate")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                    </View>

                </TouchableOpacity>
                <View style={[gutters.marginTop_16]}>
                    <AKTextField value={form.nickname} error={errors.nickname} name='nickname' label={t("profile:NickName")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                </View>
                <View style={[gutters.marginTop_16]}>
                    <AKTextField value={form.bio} error={errors.bio} name='bio' label={t("profile:Bio")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                </View>
                <View style={[gutters.marginTop_16]}>
                    <AKTextField value={form.address} error={errors.address} name='address' label={t("profile:Address")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                </View>
                <View style={[gutters.marginTop_16]}>
                    <AKTextField keyboardType="numeric"  value={form.phoneNumber} error={errors.phoneNumber} name='phoneNumber' label={t("profile:PhoneNumber")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                </View>
            </View>
            <View style={[gutters.marginHorizontal_16, {height: 100}]}>
                <AKButton 
                    height={48}
                    title={t("profile:Next")}
                    onPress={nextButtonPressed}
                    borderRadius={10}
                    viewStyle={[gutters.marginTop_16]}
                />
                <AKButton 
                    height={48}
                    title={t("profile:Skip")}
                    backgroundColor={c.custom.white}
                    onPress={skipButtonTapped}
                    textStyle={[fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: c.content.default.default}]}
                    borderRadius={10}
                    viewStyle={[gutters.marginTop_16]}
                />
            </View>
            <DatePicker
                modal
                mode="date"
                open={openDatePicker}
                date={new Date()}
                onConfirm={(date) => {
                    setOpenDatePicker(false)
                    setForm({..._.set(form,  'birthdate', moment(date).format('YYYY-MM-DD').toString())});
                }}
                onCancel={() => {
                    setOpenDatePicker(false)
                }}
            />
        </KeyboardAwareScrollView>
    )
}
export default PersonalInfo