import { AKButton, AKTermsControl, AKTextField, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import _ from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Text, TouchableOpacity, View } from "react-native";
import DropDownIcon from "@/theme/assets/images/DropDownIcon.png"
import { useMutation, useQuery } from "@tanstack/react-query";
import { getProfile, profileOptions } from "@/services/users";
import Modal from "react-native-modal";
import { SingleSelectionPopup } from "@/components/popups";
import { findSeletedOption } from "@/utils/utility";
import updateProfile from "@/services/users/updateProfile";
import Spinner from "react-native-loading-spinner-overlay";
import BackIcon from "@/theme/assets/images/BackIcon.png"
import { colorTokens } from "@/theme/colorTokens";

function AkinaInfo({navigation}) {
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();

    const c = colorTokens();

    const { t } = useTranslation(['profile']);
    const [form, setForm] = useState({
        motivationToJoin: {
            name: '',
            value: ''
        },
        insterestedIn: {
            name: '',
            value: ''
        },
    })
    const [acceptedTerms, setAcceptedTerms] = useState(false)
    const [errors, setErrors] = useState({
        motivationToJoin: null,
        insterestedIn: null,
    })
    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });
    
    const [currentFieldName, setCurrentFieldName] = useState('')
    const [currentOptions, setCurrentOptions] = useState([])
    const [modalVisible, setModalVisible] = useState(false)
    const [popupHeight, setPopupHeight] = useState(400)
    const [isLoading, setIsLoading] = useState(false)
    const updateMutation = useMutation(
        {
            mutationFn: (newUser: any) => updateProfile(newUser),
            onSuccess: (response) => {
                setIsLoading(false)
                let options = [{text: t("profile:Ok"), onPress: async () => {
                    navigation.getParent()?.goBack()
                }}]
                Alert.alert(t("profile:Success"), response.message, options)

            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );
    useEffect(() => {
        setForm({..._.set(form, 'motivationToJoin', findSeletedOption(data?.user_profile.joining_motivation_options))});
        setForm({..._.set(form, 'insterestedIn', findSeletedOption(data?.user_profile.interests_option))});
        setAcceptedTerms(data?.user_profile?.is_terms_accepted ?? false)
    }, [data])
    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});
    }
    function nextButtonPressed() {
        let data: any = {}
        if (acceptedTerms == false) {
            Alert.alert(t("profile:Warning"), t("profile:TermsError"))
            return
        } else {
            data.is_terms_accepted = acceptedTerms ? 1 : 0
        }
        
        if (form.motivationToJoin.value != '') {
            data.joining_motivation_options = form.motivationToJoin.name
        }
        if (form.insterestedIn.value != '') {
            data.interests_option = form.insterestedIn.name
        }
        
        if (data == null) {
            navigation.getParent()?.goBack()
        } else {
            data['_method'] = 'PUT'
            setIsLoading(true)
            const formData = new FormData();
            Object.keys(data).forEach((key) => {
                formData.append(key, data[key]);
            });
            updateMutation.mutate(formData)
        }
        // navigation.navigate('AkinaInfo')
    }
    function skipButtonTapped() {
        navigation.getParent()?.goBack()
    }
    function renderRightIcon() {
        return (
            <ImageVariant
                source={DropDownIcon}
                style={[{tintColor: colors.gray200}]}
            />
        )
    }
    function onsingleSelectionOptionSelected(option: any, name: string) {
        setModalVisible(false)
        setForm({..._.set(form, name, option)});
    }
    function fieldTapped(name: string) {
        setCurrentFieldName(name)
        let options: any = (data as any).user_profile
        switch (name) {
            case 'motivationToJoin':
                setCurrentOptions(options.joining_motivation_options)
                setPopupHeight(100 + getHeightWithRespectToCount(options.joining_motivation_options.length))
                break
            case 'insterestedIn':
                setCurrentOptions(options.interests_option)
                setPopupHeight(100 + getHeightWithRespectToCount(options.interests_option.length))
                break
            
        }
        setModalVisible(true)
    }
    const getHeightWithRespectToCount = (count?: number) => {
        return ((count ?? 0) < 6) ? (count ?? 0)* 25 : 200
    }
    return (
        <View style={[layout.flex_1, backgrounds.white]}>
            <Spinner
                visible={isLoading}
            />
            <TouchableOpacity onPress={() => navigation.goBack()}>
                <View style={[gutters.margin_24 ,{height: 30, width: 30}]}>
                    <ImageVariant
                        source={BackIcon}
                        style={ [layout.z10, layout.itemsCenter, gutters.marginTop_24]}
                    />
                </View>
            </TouchableOpacity>
            <View style={[gutters.marginHorizontal_16]}>
                <Text style={[fonts.black, fonts.bold ,fonts.size_24, layout.justifyCenter, fonts.alignCenter, gutters.marginTop_24]}>{t("profile:OnBoardingTitle")}</Text>
                <Text style={[fonts.black, fonts.size_16, layout.justifyCenter, fonts.alignCenter, gutters.marginTop_24]}>{t("profile:OnBoardingSubtitle")}</Text>
                <Text style={[fonts.black, fonts.size_16, layout.justifyCenter, fonts.alignCenter]}>{t("profile:Steps", {currentStep: 3, totalSteps: 3})}</Text>
            </View>
            <View style={[gutters.marginHorizontal_16, gutters.marginTop_16]}>
                <TouchableOpacity onPress={() => fieldTapped('motivationToJoin')}>
                    <View style={{pointerEvents: 'none'}}>
                        <AKTextField value={form.motivationToJoin.value} rightAccessory={renderRightIcon} error={errors.motivationToJoin} name='motivationToJoin' label={t("profile:MotivationToJoin")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => fieldTapped('insterestedIn')}>
                    <View style={[gutters.marginTop_16, {pointerEvents: 'none'}]}>
                        <AKTextField value={form.insterestedIn.value} rightAccessory={renderRightIcon} error={errors.insterestedIn} name='insterestedIn' label={t("profile:InterestedIn")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                    </View>
                </TouchableOpacity>
            </View>
            <AKTermsControl 
                isSelected={acceptedTerms} 
                radioButtonPressed={() => setAcceptedTerms(!acceptedTerms)}
                termsButtonPressed={() => {navigation.navigate('TermsAndConditions')}}
            />
            <View style={[gutters.marginHorizontal_16, {height: 100}]}>
                <AKButton 
                    height={48}
                    title={t("profile:Finish")}
                    onPress={nextButtonPressed}
                    borderRadius={10}
                    viewStyle={[gutters.marginTop_16]}
                />
                <AKButton 
                    height={48}
                    backgroundColor={c.custom.white}
                    title={t("profile:Skip")}
                    textStyle={[fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: c.content.default.default}]}
                    onPress={skipButtonTapped}
                    borderRadius={10}
                    viewStyle={[gutters.marginTop_16]}
                />
            </View>
            <Modal
                isVisible={modalVisible}
                onBackdropPress={() => {
                    setModalVisible(false)
                }}
                style={[layout.justifyCenter, layout.itemsCenter]}
            >
                <View style={[borders.rounded_16, backgrounds.gray50, {width: 300}, {height: popupHeight}]}>
                    <SingleSelectionPopup options={currentOptions} name={currentFieldName} onOptionSelection={(item: any, name: string) => onsingleSelectionOptionSelected(item, name)}/>

                </View>


            </Modal>
        </View>
    )
}
export default AkinaInfo