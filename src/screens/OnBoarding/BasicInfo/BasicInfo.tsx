import { <PERSON><PERSON><PERSON><PERSON>, AKTextField, ImageVariant } from "@/components/atoms";
import { useTheme } from "@/theme";
import _ from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Text, TouchableOpacity, View } from "react-native";
import DropDownIcon from "@/theme/assets/images/DropDownIcon.png"
import { useMutation, useQuery } from "@tanstack/react-query";
import { getProfile, profileOptions } from "@/services/users";
import Modal from "react-native-modal";
import { SingleSelectionPopup } from "@/components/popups";
import { findSeletedOption } from "@/utils/utility";
import updateProfile from "@/services/users/updateProfile";
import Spinner from "react-native-loading-spinner-overlay";
import BackIcon from "@/theme/assets/images/BackIcon.png"
import { colorTokens } from "@/theme/colorTokens";

function BasicInfo({navigation}) {
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();

    const c = colorTokens();

    const { t } = useTranslation(['profile']);
    const [form, setForm] = useState({
        education: {
            name: '',
            value: '',
        },
        incomeLevel: {
            name: '',
            value: '',

        },
        maritalStatus: {
            name: '',
            value: '',

        },
        haveChildren: {
            name: '',
            value: '',

        }
    })
    const [isLoading, setIsLoading] = useState(false)
    const updateMutation = useMutation(
        {
            mutationFn: (newUser: any) => updateProfile(newUser),
            onSuccess: (response) => {
                setIsLoading(false)
                navigation.navigate('AkinaInfo')
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert("Error!", error.message)
                console.log("Error:::", error)
            }
        },
    );
    const [errors, setErrors] = useState({
        education: null,
        incomeLevel: null,
        maritalStatus: null,
        haveChildren: null
    })
    const [currentFieldName, setCurrentFieldName] = useState('')
    const [currentOptions, setCurrentOptions] = useState([])
    const [modalVisible, setModalVisible] = useState(false)
    const [popupHeight, setPopupHeight] = useState(400)
    const {data } = useQuery({
        queryKey: ['get_profile'],
        queryFn: getProfile
    });
    useEffect(() => {
        setForm({..._.set(form, 'education', findSeletedOption(data?.user_profile.education_level))});
        setForm({..._.set(form, 'incomeLevel', findSeletedOption(data?.user_profile.income_level))});
        setForm({..._.set(form, 'maritalStatus', findSeletedOption(data?.user_profile.marital_status))});
        setForm({..._.set(form, 'haveChildren', findSeletedOption(data?.user_profile.children_option))});
    }, [data])
    
    function onValueChange(name: string, text: string) {
        setForm({..._.set(form, name, text)});
    }
    function onsingleSelectionOptionSelected(option: any, name: string) {
        setModalVisible(false)
        setForm({..._.set(form, name, option)});
    }
    function nextButtonPressed() {
        let data: any = {}
        if (form.education.value != '') {
            data.education_level = form.education.name
        }
        if (form.incomeLevel.value != '') {
            data.income_level = form.incomeLevel.name
        }
        if (form.maritalStatus.value != '') {
            data.marital_status = form.maritalStatus.name
        }
        if (form.haveChildren.value != '') {
            data.children_option = form.haveChildren.name
        }
        if (data == null) {
            navigation.navigate('AkinaInfo')
        } else {
            data['_method'] = 'PUT'
            setIsLoading(true)
            const formData = new FormData();
            Object.keys(data).forEach((key) => {
                formData.append(key, data[key]);
            });
            updateMutation.mutate(formData)        }
    }
    function skipButtonTapped() {
        navigation.getParent()?.goBack()

    }
    function renderRightIcon() {
        return (
            <ImageVariant
                source={DropDownIcon}
                style={[{tintColor: colors.gray200}]}
            />
        )
    }
    function fieldTapped(name: string) {
        setCurrentFieldName(name)
        let options: any = (data as any).user_profile
        switch (name) {
            case 'education':
                setCurrentOptions(options.education_level)
                setPopupHeight(100 + getHeightWithRespectToCount(options.education_level.length))
                break
            case 'incomeLevel':
                setCurrentOptions(options.income_level)
                setPopupHeight(100 + getHeightWithRespectToCount(options.income_level.length))
                break
            case 'maritalStatus':
                setCurrentOptions(options.marital_status)
                setPopupHeight(100 + getHeightWithRespectToCount(options.marital_status.length))
                break
            case 'haveChildren':
                setCurrentOptions(options.children_option)
                setPopupHeight(100 + getHeightWithRespectToCount(options.children_option.length))
                break
        }
        setModalVisible(true)
    }
    const getHeightWithRespectToCount = (count?: number) => {
        return ((count ?? 0) < 6) ? (count ?? 0)* 25 : 200
    }
    return (
        <View style={[layout.flex_1, backgrounds.white]}>
            <Spinner
                visible={isLoading}
            />
            <TouchableOpacity onPress={() => navigation.goBack()}>
                <View style={[gutters.margin_24 ,{height: 30, width: 30}]}>
                    <ImageVariant
                        source={BackIcon}
                        style={ [layout.z10, layout.itemsCenter, gutters.marginTop_24]}
                    />
                </View>
            </TouchableOpacity>
            
            <View style={[gutters.marginHorizontal_16]}>
                <Text style={[fonts.black, fonts.bold ,fonts.size_24, layout.justifyCenter, fonts.alignCenter, gutters.marginTop_24]}>{t("profile:OnBoardingTitle")}</Text>
                <Text style={[fonts.black, fonts.size_16, layout.justifyCenter, fonts.alignCenter, gutters.marginTop_24]}>{t("profile:OnBoardingSubtitle")}</Text>
                <Text style={[fonts.black, fonts.size_16, layout.justifyCenter, fonts.alignCenter]}>{t("profile:Steps", {currentStep: 2, totalSteps: 3})}</Text>
            </View>
            <View style={[gutters.marginHorizontal_16, gutters.marginTop_16]}>
                <TouchableOpacity onPress={() => fieldTapped('education')}>
                    <View style={{pointerEvents: 'none'}}>
                        <AKTextField value={form.education.value} rightAccessory={renderRightIcon} error={errors.education} name='education' label={t("profile:Education")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => fieldTapped('incomeLevel')}>
                    <View style={[gutters.marginTop_16, {pointerEvents: 'none'}]}>
                        <AKTextField value={form.incomeLevel.value} rightAccessory={renderRightIcon} error={errors.incomeLevel} name='incomeLevel' label={t("profile:IncomeLevel")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => fieldTapped('maritalStatus')}>
                    <View style={[gutters.marginTop_16, {pointerEvents: 'none'}]}>
                        <AKTextField value={form.maritalStatus.value} rightAccessory={renderRightIcon} error={errors.maritalStatus} name='maritalStatus' label={t("profile:MaritalStatus")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => fieldTapped('haveChildren')}>
                    <View style={[gutters.marginTop_16, {pointerEvents: 'none'}]}>
                        <AKTextField value={form.haveChildren.value} rightAccessory={renderRightIcon} error={errors.haveChildren} name='haveChildren' label={t("profile:HaveChildren")} baseColor={colors.gray200} onChangeValue={onValueChange}/>
                    </View>
                </TouchableOpacity>
            </View>
            
            <View style={[gutters.marginHorizontal_16, {height: 100}]}>
                <AKButton 
                    height={48}
                    title={t("profile:Next")}
                    onPress={nextButtonPressed}
                    borderRadius={10}
                    viewStyle={[gutters.marginTop_16]}
                />
                <AKButton 
                    height={48}
                    backgroundColor={c.custom.white}
                    title={t("profile:Skip")}
                    textStyle={[fonts.fontSizes.utility.sm, fonts.SemiBold, fonts.lineHeight.utility.sm, {color: c.content.default.default}]}
                    onPress={skipButtonTapped}
                    borderRadius={10}
                    viewStyle={[gutters.marginTop_16]}
                />
            </View>
            <Modal
                isVisible={modalVisible}
                onBackdropPress={() => {
                    setModalVisible(false)
                }}
                style={[layout.justifyCenter, layout.itemsCenter]}
            >
                <View style={[borders.rounded_16, backgrounds.gray50, {width: 300}, {height: popupHeight}]}>
                    <SingleSelectionPopup options={currentOptions} name={currentFieldName} onOptionSelection={(item: any, name: string) => onsingleSelectionOptionSelected(item, name)}/>
                </View>
            </Modal>
        </View>
    )
}
export default BasicInfo