import { useTheme } from "@/theme";
import { Text, TouchableOpacity, View } from "react-native";
import WebView from "react-native-webview";
import CrossIcon from "@/theme/assets/images/CrossIcon.png"
import { ImageVariant } from "@/components/atoms";
import { useTranslation } from "react-i18next";
import { SourceHTML } from "../constants";

function TermsAndConditions({navigation}) {

    const { t } = useTranslation(['profile']);
    const {
		colors,
		layout,
		gutters,
		fonts,
		backgrounds,
        borders,
	} = useTheme();
    return (
        <View style={[layout.flex_1]}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
                <View style={[,layout.absolute ,{height: 30, width: 30, right: 20, top: 20}]}>
                    <ImageVariant
                        source={CrossIcon}
                        style={ [layout.z10, layout.itemsCenter, gutters.marginTop_24]}
                    />
                </View>
            </TouchableOpacity>
            <View style={[layout.itemsCenter, layout.justifyCenter, gutters.marginTop_80, gutters.marginBottom_16]}>
                <Text style={[fonts.size_32]}>{t('profile:TermsAndConditions')}</Text>
            </View>
            <View style={[gutters.marginHorizontal_24, layout.flex_1]
            }>
                <WebView style={{backgroundColor: 'transparent'}} source={{ html: SourceHTML }} 
                />

            </View>
            

            
        </View>
    )
}
export default TermsAndConditions