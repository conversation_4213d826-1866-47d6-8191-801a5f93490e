import { AKFastImage, ImageVariant } from "@/components/atoms";
import { Alert, Text, TouchableOpacity, View, ImageSourcePropType, Linking, SectionList, Platform } from "react-native";
import { useTheme } from "@/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { colorTokens } from "@/theme/colorTokens";
import { useTranslation } from "react-i18next";
import logout from "@/services/auth/logout";
import { storage } from "@/App";
import { AUTH_STORRAGE_KEYS, FCM_TOKEN_STORAGE_KEY } from "@/utils/constants";
import { useNavigation } from "@react-navigation/native";
import { useState } from "react";
import { revokeFcmToken } from "@/integrations/fireabase/messaging";
import HeaderLogo from "@/theme/assets/images/HeaderLogo.png"
import AKSafeAreaView from "@/components/atoms/AKSafeAreaView/AKSafeAreaView";
import HeartIcon from "@/theme/assets/images/HeartIcon.png"
import Spinner from "react-native-loading-spinner-overlay";
import basicProfile from "@/services/users/basicProfile";
import Share from 'react-native-share';
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useReferralLink } from "@/hooks/useReferralLink";

interface MenuItemType {
    label: string;
    icon: ImageSourcePropType;
    route: string
}
interface MenuSectionType {
    title?: string;
    data: MenuItemType[];
}
function AKSideMenu() {
    const {
		layout,
		gutters,
        fonts,
		backgrounds,
        colors
	} = useTheme();
    const { data: profileData, refetch } = useQuery({
        queryKey: ['get_basic_profile'],
        queryFn: basicProfile,
        refetchOnWindowFocus: true,
    });
    const queryClient = useQueryClient()
    const navigation = useNavigation()

    const { top } = useSafeAreaInsets();

    const { shareReferralLink, isLoading: isGeneratingLink } = useReferralLink();

    const [isLoading, setIsLoading] = useState(false);

    const { t } = useTranslation(['sideMenu', 'home']);
    
    const c = colorTokens();
    const menuItems: MenuSectionType[] = [
        {
            title: 'main',
            data: [
                {label: 'Ask Akina', icon: require('@/theme/assets/images/AIIcon.png'), route: 'ChatHome'},
                {label: t('sideMenu:Members'), icon: require('@/theme/assets/images/MembersIcon.png'), route: 'Search'},
                {label: 'Groups', icon: require('@/theme/assets/images/GroupsIcon.png'), route: 'Groups'},
                {label: t('sideMenu:Favorites'), icon: require('@/theme/assets/images/HeartIcon.png'), route: 'Favorites'},
                {label: t('sideMenu:Saved'), icon: require('@/theme/assets/images/SocialConnect/BookmarkIcon.png'), route: 'Saved'},
                {label: t('sideMenu:Settings'), icon: require('@/theme/assets/images/SettingsIcon.png'), route: 'Settings'},
            ]
        },
        {
            title: 'secondary',
            data: [
                {label: t('sideMenu:PrivacyPolicy'), icon: require('@/theme/assets/images/PrivacyIcon.png'), route: 'PrivacyPolicy'},
                {label: t('sideMenu:Help'), icon: require('@/theme/assets/images/HelpIcon.png'), route: 'Help'},
                {label: t('sideMenu:Logout'), icon: require('@/theme/assets/images/LogoutIcon.png'), route: 'NewLogin'}
            ]
        }
    ];
    const logoutMutation = useMutation(
        {
            mutationFn: (token?: string) => logout(token),
            onSuccess: async () => {
                let keys = storage.getAllKeys()
                keys.map(key => {
                    if (key != AUTH_STORRAGE_KEYS.USERID) {
                        storage.delete(key)
                    }
                } )
                // storage.clearAll()
                queryClient.setDefaultOptions({
                    queries: {
                        enabled: false, // Disable all queries
                        retry: false,
                        refetchOnMount: false,
                        refetchOnWindowFocus: false,
                        refetchOnReconnect: false,
                    },
                })
                await queryClient.cancelQueries()
                queryClient.clear()
                // queryClient.removeQueries()
                revokeFcmToken()
                navigation.reset({
                    index: 0,
                    routes: [{name: 'NewLogin'}]
                })
                setIsLoading(false)
            },
            onError: (error) => {
                setIsLoading(false)
                Alert.alert(t('home:Error'), error.message)
            }
        },
    );

    function onPressLogout() {
        let options = [
            {
                text: 'Cancel',
            },
            {
                text: 'Yes',
                onPress: async () => {
                    setIsLoading(true);
                    const storedToken = await storage.getString(FCM_TOKEN_STORAGE_KEY);
                    logoutMutation.mutate(storedToken);
                },
                style: 'destructive',
            },
        ]
        Alert.alert('Logging out!', 'Do you want to log out of your account?', options)
    }

    function renderItem({item, section}: {item: MenuItemType, section: MenuSectionType}) {
        async function menuItemTapped(route: string) {
            // navigation.dispatch(DrawerActions.closeDrawer());

            if (route == 'NewLogin') {
                onPressLogout();
            } else if (route == 'Help') {
                Linking.openURL('mailto:<EMAIL>')
            } else {
                navigation.navigate(route, route == 'ChatHome' ? {moduleId: 11} : null)
            }
        }

        const iconSize = section.title == 'main' ? 24 : 20;
        const fontStyle = section.title == 'main' ? [fonts.fontSizes.body.lg, fonts.lineHeight.body.lg] : [fonts.fontSizes.body.sm, fonts.lineHeight.body.sm];

        return (

            <TouchableOpacity onPress={() => menuItemTapped(item.route)}>
                <View style={[layout.row, layout.itemsCenter, gutters.marginLeft_24, gutters.marginTop_24]}>
                    <ImageVariant tintColor={c.content.default.default} source={item.icon} style={{height: iconSize, width: iconSize}}/>
                    <Text style={[gutters.marginLeft_16, ...fontStyle, fonts.Medium, {color: c.content.default.default}]}>{item.label}</Text>

                </View>
            </TouchableOpacity>
        )
    }

    function inviteButtonTapped() {
        shareReferralLink();
    }
    function renderSectionFooter({section}: {section: MenuSectionType}) {
        return section.title == 'main' ? <View style={[gutters.marginHorizontal_24, gutters.marginTop_24, {height: 1, backgroundColor: c.stoke.default.default}]}/>: null;
    }

    function onPressProfile() {
        navigation.navigate('UserProfile', { userId: profileData?.user.id })
    }
    
    return (
        <AKSafeAreaView style={[layout.flex_1]}>
            <Spinner
                visible={isLoading || isGeneratingLink}
            />
            <TouchableOpacity onPress={onPressProfile} style={[gutters.marginLeft_24, { marginTop: top || 12 }]}>
                <View style={[]}>
                    <AKFastImage
                        placeholder={require('@/theme/assets/images/SocialConnect/DefaultProfileIcon.png')}
                        uri={profileData?.user.profile_photo}
                        style={{width: 56, height: 56, borderRadius: 28}}
                    />
                </View>
                <Text 
                    style={[gutters.marginTop_4, fonts.fontSizes.headings.H5, fonts.lineHeight.headings.H5, fonts.Bold, {color: c.content.default.emphasis}]}>
                        {`${profileData?.user.first_name} ${profileData?.user.last_name}`}
                </Text>
                <Text
                    style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, {color: '$687684'}]}
                >@{profileData?.user.user_name}</Text>
            </TouchableOpacity>
            <View style={[layout.row, gutters.marginLeft_24, gutters.marginTop_16]}>
                <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, {color: c.content.default.emphasis}]} >{profileData?.followers_count}</Text>
                <Text style={[gutters.marginLeft_4,fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, {color: '$687684'}]}>Followers</Text>

                <Text style={[gutters.marginLeft_8, fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, {color: c.content.default.emphasis}]}>{profileData?.posts_count}</Text>
                <Text style={[gutters.marginLeft_4, fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.Medium, {color: '$687684'}]}>Post</Text>
            </View>
            <View style={[gutters.marginHorizontal_24, gutters.marginTop_24 ,{height: 1, backgroundColor: c.stoke.default.default}]}/>

            <View style={[layout.flex_1]}>
                <SectionList
                    sections={menuItems}
                    renderItem={renderItem}
                    renderSectionFooter={renderSectionFooter}
                    keyExtractor={(item, index) => `${item.route}-${index}`}
                    showsVerticalScrollIndicator={false}
                />
            </View>
            <View style={[ gutters.marginBottom_32, gutters.marginLeft_32]}>
                <View>
                    <ImageVariant tintColor={c.content.primary.default}  source={HeaderLogo} style={{height: 20, width: 70, resizeMode: 'contain'}}/>
                </View>
                <TouchableOpacity onPress={inviteButtonTapped}>
                    <View style={[layout.row, gutters.marginTop_12]}>
                        <ImageVariant tintColor={c.content.default.default} source={HeartIcon} style={{height: 15, width: 15}}/>
                        <View style={{flex: 1}}>
                            <Text style={[gutters.marginLeft_6, fonts.fontSizes.utility.xs, fonts.lineHeight.utility.xs, fonts.SemiBold, {color: c.content.default.default}]}>{t("sideMenu:LoveAppDes")}</Text>
                        </View>
                    </View>
                </TouchableOpacity>
            </View>

        </AKSafeAreaView>
    )
}
export default AKSideMenu