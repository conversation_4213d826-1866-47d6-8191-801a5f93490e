export type User = {
    first_name: string,
	last_name: string,
	email: string,
	id: number,
    bio?: string,
    address?: string,
    phone_number?: string
    profile_photo?: string,
    date_of_birth?: string,
    is_completed?: boolean,
    nick_name?: string,
    updated_at?: string,
    created_at?: string,
    user_id?: number,
    cover_photo?: string,
    notifications_count? : number,
    is_terms_accepted?: boolean,
    education_level?: Array<Option>
    income_level?: Array<Option>,
    marital_status?: Array<Option>,
    children_option?: Array<Option>,
    interests_option?: Array<Option>,
    joining_motivation_options?: Array<Option>,
    user_type?: string,
    user_name?: string,
    followers_count?: number,
    sc_posts_count?: number
}

export type Option = {
    name: string,
    value: string,
    selected: boolean
}
export type GetProfileResponse = {
    success: boolean,
    user_profile: User
}

export type GetReferralCodeResponse = {
    success: boolean,
    referral_code: string
}
