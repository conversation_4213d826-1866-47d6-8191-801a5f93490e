import type { StackScreenProps } from '@react-navigation/stack';
import { moduleSchema } from './schemas/module';
import { z } from 'zod';
import { musicSchema } from './schemas/musicListing';
import { empowerHerListingSchema } from './schemas/empowerHerListings';
import { empowerHerPostSchema } from './schemas/empowerHerPostsListing';
import { videoSchema } from './schemas/videoListing';
import { newsSourceSchema } from './schemas/newsSources';
import { newsSchema } from './schemas/newsListing';
import { eventLocationSchema } from './schemas/eventLocationListing';
import { eventSchema } from './schemas/eventListing';
import { podcastSchema } from './schemas/podcastListing';
import { MediaType } from 'react-native-image-picker';
import { socialConnectPostSchema } from './schemas/socialConnectPost';

export type ApplicationStackParamList = {
	Startup: undefined;
	Example: undefined;
	Auth: undefined;
	NewLogin: undefined;

	VerificationCode: {
		email: string;
	};
	Main: undefined;
	ResetPassword: {
		email: string
	};
	LoginSuccess: undefined
	OnBoarding: undefined
	OnBoardingSignUp: undefined
	ChangePassword: undefined
	BlogListing: {
		item: typeof moduleSchema['_output']
	}
	BlogDetail: {
		item : any,
		moduleTitle: string
	}
	TermsAndConditions: undefined
	PrivacyPolicy: undefined
	NotificationPreference: undefined
	EmpowerHerListing: {
		item : z.infer<typeof moduleSchema>
	}
	PlaceListing: {
		item: z.infer<typeof empowerHerListingSchema>
	}
	MusicListing: {
		item: z.infer<typeof moduleSchema>
	}
	MusicDetail: {
		item?: z.infer<typeof musicSchema>
		categoryName?: string
	}
	PodcastListing: {
		item: z.infer<typeof moduleSchema>
	}
	PlaceDetail: {
		item: z.infer<typeof empowerHerPostSchema>
	}
	VideoListing: {
		item: z.infer<typeof moduleSchema>
	}
	VideoDetail: {
		item: z.infer<typeof videoSchema>
		categoryName?: string
	}
	NewsSourceListing: {
		item: z.infer<typeof moduleSchema>
	}
	NewsListing: {
		item: z.infer<typeof newsSourceSchema>
	}
	NewsDetail: {
		item: z.infer<typeof newsSchema>
		sourceName: string
	}
	LocationListing: {
		item: z.infer<typeof moduleSchema>
	}
	LocationEvents: {
		item: z.infer<typeof eventLocationSchema>
		isAkinaEvents: boolean
	}
	EventDetail: {
		item: z.infer<typeof eventSchema>
	}
	PodcastDetail: {
		item: z.infer<typeof podcastSchema>
		category: string
	}
	Search: undefined
	SocialConnect: undefined,
	SocialConnectDetail: {
		id: number,
		showComments?: string
	},
	SocialConnectPostDetail: {
		id: number,
		showComments?: string
	},
	AddText: {
		postText?: string | null
		isUpdating?: boolean
		postId?: number
		onGoBack?: (shouldRefresh: boolean) => void
	}
	AddMultimedia: {
		postId?: number
		postText?: string | null
		isUpdating?: boolean | null
		postType?: string | null
		photoLink?: string | null
		videoLink?: string | null
		onGoBack?: (shouldRefresh: boolean) => void
	}
	AkinaAI: {
		moduleId: number
	}
	ChatListing: {
		moduleId: number
	}
	Chat: {
		item: {
			id?: number
			moduleId: number
			limit: number
		}
	}
	Profile: {
		userId: number
	}
	UserListing: {
		userId: number,
		isFollowing: boolean
	}
	Notifications: undefined
	AKWebView: {
		source: string,
		isFromSignup?: boolean
	}
	AddSocialPost: {
		postId?: number
		postText?: string | null
		isUpdating?: boolean | null
		postType?: string | null
		photoLink?: string | null
		videoLink?: string | null
		onGoBack?: (shouldRefresh: boolean) => void
	}
	CameraRoll: {
		type: MediaType,
		onMultimediaPicked: (path?: string) => void
	}
	Members: undefined
	MembersSearch: undefined
	UserProfile: {
		userId: number
	}
	VideoPreview: {
		item: z.infer<typeof socialConnectPostSchema>
	}
	NotificationsListing: undefined
	BlockedAccounts: undefined
	ChatHome: {
		moduleId: number
	}
	Referrals: undefined
};
export type OnBoardingStackParamList = {
	PersonalInfo: undefined
	BasicInfo: undefined
	AkinaInfo: undefined
}

export type ApplicationScreenProps =
	StackScreenProps<ApplicationStackParamList>;
