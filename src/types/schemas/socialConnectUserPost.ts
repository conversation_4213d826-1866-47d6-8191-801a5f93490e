import { z } from 'zod';
import { socialConnectUserSchema } from './socialConnectUsers';

export const socialConnectUserPost = z.object({
    success: z.boolean(),
    message: z.string(),
    post_data: z.object({
        created_at: z.string(),
        id: z.number(),
        module_id: z.number(),
        text: z.string().optional().nullable(),
        type: z.string(),
        updated_at: z.string(),
        user: socialConnectUserSchema.optional().nullable(),
        user_id: z.number().optional().nullable(),
        media_link: z.string().optional().nullable(),
        video_thumbnail: z.string().optional().nullable()
    })
});
