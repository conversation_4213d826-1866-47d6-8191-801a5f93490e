import { z } from "zod";

export const updateNotificationPrefernceSchema = z.object({
    id: z.number(),
    user_id: z.number(),
    general_information: z.boolean(),
    sound: z.boolean(),
    vibrate: z.boolean(),
    app_updates: z.boolean(),
    bill_reminder: z.boolean(),
    promotion: z.boolean(),
    discount_available: z.boolean(),
    payment_request: z.boolean(),
    new_service_available: z.boolean(),
    new_tips_available: z.boolean(),
    created_at: z.string(),
    updated_at: z.string()
})

export const updateNotificationResponseSchema = z.object({
	success: z.boolean(),
    message: z.string(),
	user_notification_preferences: updateNotificationPrefernceSchema,
});