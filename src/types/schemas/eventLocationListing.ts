import { isDate } from 'lodash';
import { Zod<PERSON><PERSON><PERSON>, z } from 'zod';
import { moduleSchema } from './module';

export const eventLocationSchema = z.object({
	id: z.number(),
    module_id: z.number(),
    name: z.string(),
	sub_title: z.string(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
});
export const eventLocationListingResponseSchema = z.object({
    success: z.boolean(),
    module: moduleSchema,
    locations: z.array(eventLocationSchema)
})