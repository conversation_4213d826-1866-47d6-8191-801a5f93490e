import { isDate } from 'lodash';
import { Zod<PERSON><PERSON><PERSON>, z } from 'zod';
import { moduleSchema } from './module';
import { eventLocationSchema } from './eventLocationListing';

export const eventSchema = z.object({
	id: z.number(),
    module_id: z.number(),
    location_id: z.number(),
    title: z.string(),
	sub_title: z.string(),
    content: z.string(),
    thumbnail: z.string(),
    button_link: z.string().nullable().optional(),
    button_text: z.string().nullable().optional(),
    event_date: z.string(),
    is_akina_event: z.number(),
    created_at: z.string(),
    updated_at: z.string(),
});
export const eventListingResponseSchema = z.object({
    success: z.boolean(),
    location: eventLocationSchema,
    location_events: z.array(eventSchema),
    akina_events: z.array(eventSchema)
})