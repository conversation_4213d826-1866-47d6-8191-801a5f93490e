import { isDate } from 'lodash';
import { Zod<PERSON>rray, z } from 'zod';
import { moduleSchema } from './module';
import { empowerHerListingSchema } from './empowerHerListings';
export const empowerHerPostImageSchema = z.object({
    id: z.number(),
    empower_her_post_id: z.number(),
    image_url: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
})
export const empowerHerPostSchema = z.object({
	id: z.number(),
    module_id: z.number(),
    empower_her_type_id: z.number(),
	title: z.string(),
    subtitle: z.string(),
    content: z.string(),
    thumbnail: z.string(),
    button_text: z.string().nullable().optional(),
    button_link: z.string().nullable().optional(),
    created_at: z.string(),
    updated_at: z.string(),
    images: z.array(empowerHerPostImageSchema).nullable().optional()
});

export const empowerHerPostsListingResponseSchema = z.object({
    success: z.boolean(),
    type: empowerHerListingSchema,
    posts: z.array(empowerHerPostSchema)
})