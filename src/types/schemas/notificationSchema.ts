import { z } from "zod";
import { linkSchema } from "./newsListing";

export const notificationSchema = z.object({
    id: z.number(),
    user_id: z.number().nullable(),
    title: z.string(),
    body: z.string(),
    data: z.array(z.object({
        url: z.string().optional().nullable(),
        comment_id: z.string().optional().nullable()
    })).or(z.object({
        url: z.string().optional().nullable(),
        comment_id: z.string().optional().nullable()
    })).or(z.string()),
    is_read: z.boolean(),
    is_sent: z.boolean(),
    created_at: z.string(),
    updated_at: z.string()
});

export const paginatedNotificationSchema = z.object({
    data: z.array(notificationSchema),
    current_page: z.number(),
    from: z.number(),
    last_page: z.number(),
    links: z.array(linkSchema),
    path: z.string(),
    per_page: z.number(),
    to: z.number(),
    total: z.number(),
});