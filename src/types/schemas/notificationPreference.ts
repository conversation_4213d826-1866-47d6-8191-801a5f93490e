import { z } from 'zod';

export const notificationPreferenceSchema = z.object({
    name: z.string(),
    value: z.string(),
    selected: z.boolean()
})
export const notificationTypesSchema = z.object({
    'common': z.array(notificationPreferenceSchema).optional().nullable(),
    'system_&_services_update': z.array(notificationPreferenceSchema).optional().nullable(),
    'others': z.array(notificationPreferenceSchema).optional().nullable()

})
export const notificationResponseSchema = z.object({
	success: z.boolean(),
	user_notification_preferences: notificationTypesSchema,
});
