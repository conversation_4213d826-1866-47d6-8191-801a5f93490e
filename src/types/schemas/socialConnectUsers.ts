import { z } from 'zod';
import { linkSchema } from './newsListing';

export const socialConnectUserSchema = z.object({
	first_name: z.string(),
	last_name: z.string(),
	email: z.string(),
	id: z.number(),
    user_name: z.string().optional().nullable(),
	is_followed: z.boolean().optional().nullable(),
    profile_photo: z.string().nullable().optional(),
});

export const socialConnectUsersResponseSchema = z.object({
	data: z.array(socialConnectUserSchema),
	current_page: z.number(),
    from: z.number().nullable(),
    last_page: z.number(),
    links: z.array(linkSchema),
    path: z.string(),
    per_page: z.number(),
    to: z.number().nullable(),
    total: z.number(),
});
