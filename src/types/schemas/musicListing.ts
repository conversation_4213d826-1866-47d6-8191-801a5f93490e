import { isDate } from 'lodash';
import { Zod<PERSON><PERSON><PERSON>, z } from 'zod';
import { moduleSchema } from './module';
import { commentSchema } from './postDetail';

export const musicSchema = z.object({
    id: z.number(),
    category_id: z.number(),
    tag_id: z.number().optional().nullable(),
	title: z.string(),
    module_id: z.number(),
    audio_link: z.string(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    previous: z.number().nullable().optional(),
    next: z.number().nullable().optional(),
    is_liked: z.boolean().nullable().optional(),
    is_bookmarked: z.boolean().nullable().optional(),
    comments: z.array(commentSchema).optional().nullable()
})
export const categoriesListingSchema = z.object({
	id: z.number(),
    module_id: z.number(),
	name: z.string(),
    layout: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    music: z.array(musicSchema)

});
export const musicListingResponseSchema = z.object({
    success: z.boolean(),
    module: moduleSchema,
    categories: z.array(categoriesListingSchema)
})