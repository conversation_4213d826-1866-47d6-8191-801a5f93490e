import { isDate } from 'lodash';
import { Zod<PERSON><PERSON>y, z } from 'zod';
import { moduleSchema } from './module';
import { linkSchema } from './newsListing';
import { commentSchema } from './postDetail';

export const podcastSchema = z.object({
    id: z.number(),
    category_id: z.number(),
    category: z.object({
        id: z.number(),
        name: z.string(),
    }).nullable().optional(),
    tag_id: z.number().optional().nullable(),
	title: z.string(),
    guid: z.string(),
    description: z.string().nullable().optional(),
    module_id: z.number().nullable().optional(),
    audio_link: z.string(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    previous: z.number().nullable().optional(),
    next: z.number().nullable().optional(),
    comments: z.array(commentSchema).optional().nullable(),

    is_liked: z.boolean().nullable().optional(),
    is_bookmarked: z.boolean().nullable().optional(),

})
export const podcastCategorySchema = z.object({
	id: z.number(),
    module_id: z.number(),
	name: z.string(),
    layout: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    podcasts: z.array(podcastSchema),
    podcasts_count: z.number().optional(),
    is_Loading: z.boolean().optional()

});
export const podcastListingResponseSchema = z.object({
    success: z.boolean(),
    module: moduleSchema,
    categories: z.array(podcastCategorySchema),
    // tags: z.array(z.string()).nullable().optional()
})

export const podcastByCategoryPaginationSchema = z.object({
    current_page: z.number(),
    data: z.array(podcastSchema),
    first_page_url: z.string(),
    from: z.number(),
    last_page: z.number(),
    last_page_url: z.string(),
    links: z.array(linkSchema),
    next_page_url: z.string().optional().nullable(),
    path: z.string(),
    per_page: z.number(),
    prev_page_url: z.string().optional().nullable(),
    to: z.number(),
    total: z.number(),

})
export const podcastByCategoryResponseSchema = z.object({
    success: z.boolean(),
    podcasts: podcastByCategoryPaginationSchema
})