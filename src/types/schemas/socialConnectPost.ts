import { z } from 'zod';
import { linkSchema } from './newsListing';
import { commentSchema } from './postDetail';
import { socialConnectUserSchema } from './socialConnectUsers';

export const socialConnectPostSchema = z.object({
	id: z.number(),
    module_id: z.number(),
    type: z.string(),
    text: z.string().nullable().optional(),
	media_link: z.string().nullable().optional(),
    video_thumbnail: z.string().nullable().optional(),
	created_at: z.string(),
	updated_at: z.string(),
    isPlaying: z.boolean().optional(),
	is_liked: z.boolean().optional().nullable(),
    is_bookmarked: z.boolean().optional().nullable(),
    user: socialConnectUserSchema.optional().nullable(),
    comments: z.array(commentSchema).optional().nullable(),
    comments_count: z.number().optional().nullable(),
    likes_count: z.number().optional().nullable(),
    bookmarks_count: z.number().optional().nullable(),
    reported_exists: z.boolean().optional(),
})

export const socialConnectPostsResponseSchema = z.object({
	data: z.array(socialConnectPostSchema).transform(val => val ?? []),
	current_page: z.number(),
    from: z.number().nullable(),
    last_page: z.number(),
    links: z.array(linkSchema),
    path: z.string(),
    per_page: z.number(),
    to: z.number().nullable(),
    total: z.number(),
});

