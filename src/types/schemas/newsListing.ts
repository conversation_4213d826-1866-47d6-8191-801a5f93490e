import { z } from "zod";
import { moduleSchema } from "./module";
import { newsSourceSchema } from "./newsSources";
import { commentSchema } from "./postDetail";

export const linkSchema = z.object({
    url: z.string().nullable().optional(),
    label: z.string(),
    active: z.boolean()
})

export const newsSchema = z.object({
    id: z.number(),
    external_id: z.number().optional().nullable(),
    news_source_id: z.number().optional().nullable(),
    module_id: z.number(),
    title: z.string(),
    description: z.string(),
    url: z.string(),
    author: z.string().optional().nullable(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    is_liked: z.boolean().optional().nullable(),
    is_bookmarked: z.boolean().optional().nullable(),
    comments: z.array(commentSchema).optional().nullable()
})
export const newsListingSchema = z.object({
	current_page: z.number(),
    data: z.array(newsSchema),
    first_page_url: z.string(),
    from: z.number(),
    last_page: z.number(),
    last_page_url: z.string(),
    links: z.array(linkSchema),
    next_page_url: z.string().optional().nullable(),
    path: z.string(),
    per_page: z.number(),
    prev_page_url: z.string().optional().nullable(),
    to: z.number(),
    total: z.number(),

});
export const newsistingResponseSchema = z.object({
    success: z.boolean(),
    module: moduleSchema,
    news_source: newsSourceSchema,
    news: newsListingSchema
})