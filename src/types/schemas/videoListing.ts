import { isDate } from 'lodash';
import { Zod<PERSON><PERSON>y, z } from 'zod';
import { moduleSchema } from './module';
import { commentSchema } from './postDetail';

export const videoSchema = z.object({
    id: z.number(),
    category_id: z.number(),
    tag_id: z.number().optional().nullable(),
	title: z.string(),
    module_id: z.number(),
    video_link: z.string(),
    video_type: z.string(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    previous: z.number().nullable().optional(),
    next: z.number().nullable().optional(),
    is_liked: z.boolean().nullable().optional(),
    is_bookmarked: z.boolean().nullable().optional(),
    comments: z.array(commentSchema).optional().nullable()
})
export const videoCategorySchema = z.object({
	id: z.number(),
    module_id: z.number(),
	name: z.string(),
    layout: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    videos: z.array(videoSchema)

});
export const videoListingResponseSchema = z.object({
    success: z.boolean(),
    module: moduleSchema,
    categories: z.array(videoCategorySchema)
})