import { z } from "zod";
export const messageSchema = z.object({
	id: z.number(),
    conversation_id: z.number(),
    external_message_id: z.string().optional().nullable(),
    user_id: z.number().nullable(),
    sender_type: z.string(),
    content: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    like_status: z.number().nullable().optional()
})

export const conversationSchema = z.object({

	id: z.number(),
    user_id: z.number(),
    title: z.string().nullable(),
    created_at: z.string(),
    updated_at: z.string(),
    external_conversation_id: z.string(),
    messages: z.array(messageSchema).optional().nullable(),
    allow_interaction: z.number().optional().nullable(),

})
export const conversationResponseSchema = z.object({
    status: z.boolean(),
    request_limit: z.number(),
    conversations: z.array(conversationSchema)
})
export const startConversationSchema =  z.object({
    conversation_id: z.number(),
    message: messageSchema,
    request_limit: z.number()
})
