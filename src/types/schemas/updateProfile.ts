import { z } from "zod";
export const userDataSchema = z.object({
    first_name: z.string(),
	last_name: z.string(),
	email: z.string(),
	id: z.number(),
    profile_photo: z.string().optional().nullable(),
    date_of_birth: z.string().optional().nullable(),
    updated_at: z.string(),
    created_at: z.string(),
    user_id: z.number(),
})
export const updateProfileSchema = z.object({
    success: z.boolean(),
	message: z.string(),
    profile_data: userDataSchema
});
