import { z } from 'zod';


export const likeSchema = z.object({
    id: z.number(),
    module_id: z.number(),
    module_title: z.string(),
    news_source_id: z.number().nullable().optional(),
    source_name: z.string().nullable().optional(),
	type: z.string(),
    thumbnail: z.string(),
    title: z.string(),
});
export const userLikesResponseSchema = z.object({
    success: z.boolean(),
    likes: z.array(likeSchema)
})