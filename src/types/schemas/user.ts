import { z } from 'zod';

function getSocialConnectUserSchema() {
    return require("./socialConnectUsers").socialConnectUserSchema;
}

export const userFollowingSchema = z.object({
	id: z.number(),
	user_id: z.number(),
	follower_id: z.number(),
	updated_at: z.string(),
    created_at: z.string(),
	user_follower: z.lazy(() => getSocialConnectUserSchema()),
})
export const userFollowerSchema = z.object({
	id: z.number(),
	user_id: z.number(),
	follower_id: z.number(),
	updated_at: z.string(),
    created_at: z.string(),
	user_following: z.lazy(() => getSocialConnectUserSchema()),
})
export const profileSchem = z.object({
	id: z.number(),
	bio: z.string().nullable().optional(),
	address: z.string().nullable().optional(),
	phone_number: z.string().nullable().optional(),
	profile_photo: z.string().nullable().optional(),
	date_of_birth: z.string().nullable().optional(),
	nick_name: z.string().nullable().optional(),
	updated_at: z.string().nullable().optional(),
	created_at: z.string().nullable().optional(),
	user_id: z.number().nullable().optional(),
	is_terms_accepted: z.boolean().nullable().optional(),
})
export const userSchema = z.object({
	first_name: z.string(),
	last_name: z.string(),
	email: z.string(),
	id: z.number(),
	type: z.string().optional().nullable(),
	affiliate_code: z.string().optional().nullable(),
	profile_photo: z.string().optional().nullable(),
	cover_photo: z.string().optional().nullable(),
	user_name: z.string().optional().nullable(),
	followings_count: z.number().optional().nullable(),
	followers_count: z.number().optional().nullable(),
	social_connect_likes_count: z.number().optional().nullable(),
	is_followed: z.boolean().optional().nullable(),
	profile: profileSchem.optional().nullable()
});

export const commentUserSchema = z.object({
	first_name: z.string(),
	last_name: z.string(),
	email: z.string(),
	id: z.number(),
	user_name: z.string().optional().nullable(),
	type: z.string().optional().nullable(),
	affiliate_code: z.string().optional().nullable(),
	profile_photo: z.string().optional().nullable(),
}) 
export const basicUserSchema = z.object({
	first_name: z.string(),
	last_name: z.string(),
	email: z.string(),
	id: z.number(),
	user_name: z.string().optional().nullable(),
	type: z.string().optional().nullable(),
	affiliate_code: z.string().optional().nullable(),
	profile_photo: z.string().optional().nullable(),
	cover_photo: z.string().optional().nullable(),
	stripe_id: z.string().optional().nullable(),
	user_type: z.string(),
	user_membership_type: z.string().optional().nullable(),
})
export const basicProfileResponse = z.object({
	success: z.boolean(),
	user: basicUserSchema,
	followers_count: z.number(),
	posts_count: z.number(),
	notifications_count: z.number(),
	share_able_referral_code: z.string().nullable()
})
export const blockedSchema = z.object({
	id: z.number(),
	user_id: z.number(),
	blocked_by: z.number(),
	updated_at: z.string(),
	created_at: z.string(),
	user: basicUserSchema,

})
export const blockUserResponseSchema = z.object({
	success: z.boolean(),
	blocked_users:  z.array(blockedSchema)
})
