import { z } from "zod";

function getUserSchema() {
    return require("./user").userSchema;
}

export const replySchema = z.object({
    id: z.number(),
    reply: z.string(),
    user_id: z.number(),
    created_at: z.string(),
    updated_at: z.string(),
    reported_reply_exists: z.boolean().optional(),
    user: z.lazy(() => getUserSchema()),
})

export const commentSchema = z.object({
    id: z.number(),
    comment: z.string(),
    user_id: z.number(),
    module_id: z.number(),
    commentable_type: z.string(),
    commentable_id: z.number(),
    created_at: z.string(),
    updated_at: z.string(),
    user: z.lazy(() => getUserSchema()),
    reported_comment_exists: z.boolean().optional(),
    replies:  z.array(replySchema).optional().nullable()
})
export const postDetailSchema = z.object({
	id: z.number(),
    module_id: z.number(),
	title: z.string(),
    content: z.string(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    is_liked: z.boolean(),
    is_bookmarked: z.boolean(),
    comments: z.array(commentSchema).optional().nullable()
});


