import { z } from 'zod';
import { socialConnectPostSchema } from './socialConnectPost';
import { commentSchema } from './postDetail';

export const socialConnectPostCommentSchema = z.object({
	success: z.boolean(),
	message: z.string(),
    post: socialConnectPostSchema,
});

export const scPostCommentListingResponseSchema = z.object({
	status: z.boolean(),
	comments: z.array(commentSchema).optional().nullable(),
});