import { z } from 'zod';
import { podcastSchema } from './podcastListing';
import { musicSchema } from './musicListing';
import { blogListingSchema } from './blogListing';
import { postDetailSchema } from './postDetail';
import { newsSchema } from './newsListing';
import { videoSchema } from './videoListing';

export const basicSchema = z.object({
	success: z.boolean(),
	message: z.string(),
});
export const basicStatusSchema = z.object({
	status: z.boolean(),
	message: z.string(),
});

export const basicPodcastSchema = basicSchema.extend({
	podcast: podcastSchema
})
export const basicMusicSchema = basicSchema.extend({
	music: musicSchema
})
export const basicPostSchema = basicSchema.extend({
	post: postDetailSchema
})
export const basicNewsSchema = basicSchema.extend({
	news: newsSchema
})
export const basicVideSchema = basicSchema.extend({
	video: videoSchema
})