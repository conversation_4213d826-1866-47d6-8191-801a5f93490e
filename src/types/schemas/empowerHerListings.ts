import { isDate } from 'lodash';
import { Zod<PERSON><PERSON><PERSON>, z } from 'zod';
import { moduleSchema } from './module';

export const empowerHerListingSchema = z.object({
	id: z.number(),
    module_id: z.number(),
	title: z.string(),
    description: z.string(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
});
export const empowerHerListingResponseSchema = z.object({
    success: z.boolean(),
    module: moduleSchema,
    types: z.array(empowerHerListingSchema)
})