import { isDate } from 'lodash';
import { Zod<PERSON><PERSON><PERSON>, z } from 'zod';

export const moduleSchema = z.object({
	id: z.number(),
	title: z.string(),
    thumbnail: z.string(),
    mob_image: z.string().optional().nullable(),
    order: z.number(),
    type: z.string(),
    bg_color: z.string(),
    logo_color: z.string(),
    header_content_value: z.string(),
    header_content_type: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    slug: z.string()

});
export const moduleResponseSchema = z.object({
    success: z.boolean(),
    modules: z.array(moduleSchema)
})