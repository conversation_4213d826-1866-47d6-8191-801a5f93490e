import { z } from "zod";
import { moduleSchema } from "./module";

export const newsSourceSchema = z.object({
	id: z.number(),
    external_id: z.number(),
    module_id: z.number(),
	name: z.string(),
    url: z.string(),
    base_href: z.string(),
    thumbnail: z.string(),
    created_at: z.string(),
    updated_at: z.string(),

});
export const newsSourceListingResponseSchema = z.object({
    success: z.boolean(),
    module: moduleSchema,
    news_sources: z.array(newsSourceSchema)
})