export enum AuthPopupTypes {
    LOGIN = "LOGIN",
    SIGNUP = "SIGNU<PERSON>",
    FORGOT_PASSWORD = "FORGOT_PASSWORD"
}

export const AUTH_STORRAGE_KEYS = {
    TOKEN: "TOKEN",
    USERID: "USER<PERSON>",
    ONBOARDINGSHOWN: "ONBOARDINGSHOW<PERSON>",
    AIONBOARDINGSHOWN: "<PERSON>ON<PERSON><PERSON>DINGSHOWN"
}
export const VIDEO_TYPES = {
    SELF_HOSTED: "self_hosted",
    YOUTUBE: "youtube"
}

export const VerificationCodeConstant = {
    CELL_COUNT : 4
}

export const UserTypes = {
    FREE: 'free',
    PREMIUM: 'premium'

}

export const MODULE_TYPES = {
    BLOG: 'blog',
    MUSIC: 'music',
    VIDEO: 'video',
    NEWS: 'news',
    EVENT: 'event',
    PODCAST: 'podcast',
    EVENT_NEW: 'event_new',
    SOCIAL_CONNECT: 'social-connect',
    AKINA_AI: 'ask_akina'
}
export const LAYOUT_TYPES = {
    CAROUSEL: 'carousel',
    SQUARE_SLIDER: 'sq-slider',
    RECT_SLIDER: 'rect-slider'
}
export const MULTIMEDIA_TYPE = {
    IMAGE: 'photo',
    VIDEO: 'video'
}
export const MAX_CACHE_SIZE = 200;

export const CACHED_VIDEO_PREFIX = 'akvideo';

export const FCM_TOKEN_STORAGE_KEY = 'fcmtoken';

export const DEEP_LINKING_PREFIXES = {
    production: 'akinaconnect://',
    development: 'akinaconnectdev://',
    staging: 'akinaconnectstaging://'
};

export const DEEPLINK_CACHE_KEY = 'PENDING_DEEPLINK_URL';

export const EMOJIS = ['❤️', '✨', '🔥', '😁', '🙂', '🥲', '😡', '😢'];

export const BLOCK_MEMBER_INSTRUCTIONS = [
    'They won’t be able to find your profile or content on Akina.',
    'They won’t be notified that you blocked them.',
    'You can unblock them anytime in Account.'
];

export const UserPictureType = {
    PROFILE: 'Profile',
    COVER: 'Cover'
}

export const REFERRAL_CODE_STORAGE_KEY = 'referralCode';
