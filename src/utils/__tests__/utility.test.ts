jest.unmock('@/utils/utility');

import { DEEP_LINKING_PREFIXES } from '../constants';
import {
  isValidEmail,
  getObjectNameFromModule,
  getTimeBasedGreeting,
  hexToRgbA,
  timeSince,
  secondToHHMMSS,
  getYoutubeID,
  parseServerError,
  findSeletedOption,
  configureTouchaleOpacityDefaultSettings,
  checkVideoInCache,
  isUserLoggedIn,
  updateUnreadCountInProfile,
  markNotificationAsRead,
  setBadgeCount,
  updateBadgeCount,
  getDeeplinkingScheme,
  isEmptyObjectOrArray,
  mapUniversalLinkToDeepLink,
  parseModules,
  formatVideoSliderTime,
  formatNotificationDate,
  showBlockedPermissionAlert,
  openCamera,
} from '../utility';

// Mock dependencies
jest.mock('react-native-fs', () => ({
  CachesDirectoryPath: '/cache',
  readDir: jest.fn(),
  exists: jest.fn(),
  downloadFile: jest.fn(),
  stat: jest.fn(),
  unlink: jest.fn(),
}));

jest.mock('@/App', () => ({
  queryClient: {
    setQueryData: jest.fn(),
  },
  storage: {
    getString: jest.fn(),
  },
}));

jest.mock('@/services/notifications/setNotificationRead', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('@notifee/react-native', () => ({
  setBadgeCount: jest.fn(),
  getBadgeCount: jest.fn(),
}));

jest.mock('react-native', () => ({
  TouchableOpacity: {
    defaultProps: {},
  },
  Alert: {
    alert: jest.fn(),
  },
  Linking: {
    openURL: jest.fn(),
  },
  Platform: {
    OS: 'ios',
  },
}));

jest.mock('react-native-gesture-handler', () => ({
  TouchableOpacity: {
    defaultProps: {},
  },
}));

jest.mock('react-native-image-picker', () => ({
  launchCamera: jest.fn(),
}));

jest.mock('react-native-permissions', () => ({
  check: jest.fn(),
  PERMISSIONS: {
    IOS: {
      CAMERA: 'ios.permission.CAMERA',
    },
  },
}));

jest.mock('../constants', () => ({
  AUTH_STORRAGE_KEYS: {
    TOKEN: 'auth_token',
  },
  CACHED_VIDEO_PREFIX: 'cached_video',
  DEEP_LINKING_PREFIXES: {
    production: 'akinaconnect://',
    development: 'akinaconnectdev://',
    staging: 'akinaconnectstaging://'
  },
  MAX_CACHE_SIZE: 100,
}));

const RNFS = require('react-native-fs');
const { queryClient, storage } = require('@/App');
const setNotificationRead = require('@/services/notifications/setNotificationRead').default;
const notifee = require('@notifee/react-native');
const { Alert, Linking } = require('react-native');
const { launchCamera } = require('react-native-image-picker');
const { check } = require('react-native-permissions');

describe('Utility Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.ENV = 'development';
  });

  describe('isValidEmail', () => {
    it('should return true for valid email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should return false for invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('')).toBe(false);
    });
  });

  describe('getObjectNameFromModule', () => {
    it('should return correct object names for different modules', () => {
      expect(getObjectNameFromModule('news')).toBe('news');
      expect(getObjectNameFromModule('podcasts')).toBe('podcast');
      expect(getObjectNameFromModule('posts')).toBe('post');
      expect(getObjectNameFromModule('videos')).toBe('video');
      expect(getObjectNameFromModule('anything-else')).toBe('music');
    });
  });

  describe('getTimeBasedGreeting', () => {
    beforeEach(() => {
      jest.spyOn(Date.prototype, 'getHours');
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should return "Good Morning" for morning hours', () => {
      Date.prototype.getHours = jest.fn().mockReturnValue(8);
      expect(getTimeBasedGreeting()).toBe('Good Morning');
    });

    it('should return "Good Afternoon" for afternoon hours', () => {
      Date.prototype.getHours = jest.fn().mockReturnValue(14);
      expect(getTimeBasedGreeting()).toBe('Good Afternoon');
    });

    it('should return "Good Evening" for evening hours', () => {
      Date.prototype.getHours = jest.fn().mockReturnValue(18);
      expect(getTimeBasedGreeting()).toBe('Good Evening');
    });

    it('should return "Good Night" for night hours', () => {
      Date.prototype.getHours = jest.fn().mockReturnValue(22);
      expect(getTimeBasedGreeting()).toBe('Good Night');
    });
  });

  describe('hexToRgbA', () => {
    it('should convert 6-digit hex to rgba', () => {
      expect(hexToRgbA('#FF0000', 0.5)).toBe('rgba(255,0,0,0.5)');
      expect(hexToRgbA('#00FF00', 1)).toBe('rgba(0,255,0,1)');
    });

    it('should convert 3-digit hex to rgba', () => {
      expect(hexToRgbA('#F00', 0.5)).toBe('rgba(255,0,0,0.5)');
      expect(hexToRgbA('#0F0', 0.8)).toBe('rgba(0,255,0,0.8)');
    });

    it('should throw error for invalid hex', () => {
      expect(() => hexToRgbA('invalid', 0.5)).toThrow('Bad Hex');
      expect(() => hexToRgbA('#GG0000', 0.5)).toThrow('Bad Hex');
    });
  });

  describe('timeSince', () => {
    beforeEach(() => {
      // Mock the Date constructor to return a fixed date
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2023-01-01T12:00:00Z'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should return "Just Now" for very recent dates', () => {
      const recentDate = new Date('2023-01-01T11:59:59Z');
      expect(timeSince(recentDate)).toBe('Just Now');
    });

    it('should return seconds ago', () => {
      const date = new Date('2023-01-01T11:59:30Z');
      expect(timeSince(date)).toBe('30 seconds ago');
    });

    it('should return minutes ago', () => {
      const date = new Date('2023-01-01T11:58:00Z');
      expect(timeSince(date)).toBe('2 minutes ago');
    });

    it('should return hours ago', () => {
      const date = new Date('2023-01-01T10:00:00Z');
      expect(timeSince(date)).toBe('2 hours ago');
    });

    it('should return days ago', () => {
      const date = new Date('2022-12-30T12:00:00Z');
      expect(timeSince(date)).toBe('2 days ago');
    });

    it('should return months ago', () => {
      const date = new Date('2022-11-01T12:00:00Z');
      expect(timeSince(date)).toBe('2 months ago');
    });

    it('should return years ago', () => {
      const date = new Date('2021-01-01T12:00:00Z');
      expect(timeSince(date)).toBe('2 years ago');
    });
  });

  describe('secondToHHMMSS', () => {
    it('should format seconds to HH:MM:SS', () => {
      expect(secondToHHMMSS(3661)).toBe('01:01:01');
      expect(secondToHHMMSS(3600)).toBe('01:00:00');
      expect(secondToHHMMSS(61)).toBe('01:01');
      expect(secondToHHMMSS(30)).toBe('00:30');
    });

    it('should handle edge cases', () => {
      expect(secondToHHMMSS(0)).toBe('00:00');
      expect(secondToHHMMSS(7200)).toBe('02:00:00');
    });
  });

  describe('getYoutubeID', () => {
    it('should extract YouTube ID from various URL formats', () => {
      expect(getYoutubeID('https://www.youtube.com/watch?v=dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ');
      expect(getYoutubeID('https://youtu.be/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ');
      expect(getYoutubeID('https://www.youtube.com/embed/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ');
    });

    it('should return original string if no valid ID found', () => {
      expect(getYoutubeID('invalid-url')).toBe('invalid-url');
    });
  });

  describe('parseServerError', () => {
    it('should handle 401/403 status codes', () => {
      expect(parseServerError({ status: 401 })).toBe('Un Authenticated!');
      expect(parseServerError({ status: 403 })).toBe('Un Authenticated!');
    });

    it('should handle string errors', () => {
      expect(parseServerError('Custom error message')).toBe('Custom error message');
    });

    it('should handle message property', () => {
      expect(parseServerError({ message: 'Server error' })).toBe('Server error');
    });

    it('should handle errors object', () => {
      const errorObj = {
        errors: {
          email: ['Email is required'],
          password: ['Password is too short']
        }
      };
      expect(parseServerError(errorObj)).toBe('Email is required');
    });

    it('should return default message for unknown errors', () => {
      expect(parseServerError({})).toBe('Something went wrong');
    });
  });

  describe('findSeletedOption', () => {
    it('should return selected option', () => {
      const options = [
        { name: 'Option 1', value: 'opt1', selected: false },
        { name: 'Option 2', value: 'opt2', selected: true },
      ];
      expect(findSeletedOption(options)).toEqual({ name: 'Option 2', value: 'opt2', selected: true });
    });

    it('should return empty object if no selection', () => {
      const options = [
        { name: 'Option 1', value: 'opt1', selected: false },
        { name: 'Option 2', value: 'opt2', selected: false },
      ];
      expect(findSeletedOption(options)).toEqual({ name: '', value: '' });
    });

    it('should handle undefined list', () => {
      expect(findSeletedOption(undefined)).toEqual({ name: '', value: '' });
    });
  });

  describe('configureTouchaleOpacityDefaultSettings', () => {
    it('should configure default props for TouchableOpacity components', () => {
      configureTouchaleOpacityDefaultSettings();
      // This function modifies default props, so we just ensure it doesn't throw
      expect(true).toBe(true);
    });
  });

  describe('checkVideoInCache', () => {
    it('should return local path if video exists in cache', async () => {
      RNFS.exists.mockResolvedValue(true);
      const result = await checkVideoInCache('https://example.com/video.mp4');
      expect(result).toBe('/cache/cached_video-example.com-video.mp4');
    });

    it('should return original URL and start download if not in cache', async () => {
      RNFS.exists.mockResolvedValue(false);
      RNFS.downloadFile.mockResolvedValue({});
      RNFS.readDir.mockResolvedValue([]);
      
      const result = await checkVideoInCache('https://example.com/video.mp4');
      expect(result).toBe('https://example.com/video.mp4');
      expect(RNFS.downloadFile).toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      RNFS.exists.mockRejectedValue(new Error('File system error'));
      const result = await checkVideoInCache('https://example.com/video.mp4');
      expect(result).toBe('https://example.com/video.mp4');
    });
  });

  describe('isUserLoggedIn', () => {
    it('should return true if token exists', () => {
      storage.getString.mockReturnValue('valid-token');
      expect(isUserLoggedIn()).toBe(true);
    });

    it('should return false if no token', () => {
      storage.getString.mockReturnValue(null);
      expect(isUserLoggedIn()).toBe(false);
    });
  });

  describe('updateUnreadCountInProfile', () => {
    it('should update query cache with new notification count', () => {
      const mockCacheData = {
        user_profile: {
          notifications_count: 10,
        },
      };
      queryClient.setQueryData.mockImplementation((key, updater) => {
        updater(mockCacheData);
      });

      updateUnreadCountInProfile(3);
      expect(queryClient.setQueryData).toHaveBeenCalledWith(['get_profile'], expect.any(Function));
    });

    it('should handle empty cache data', () => {
      queryClient.setQueryData.mockImplementation((key, updater) => {
        const result = updater(null);
        expect(result).toEqual({});
      });

      updateUnreadCountInProfile(3);
    });
  });

  describe('markNotificationAsRead', () => {
    it('should mark notification as read when user is logged in', async () => {
      storage.getString.mockReturnValue('valid-token');
      setNotificationRead.mockResolvedValue({ success: true });
      notifee.getBadgeCount.mockResolvedValue(5);

      await markNotificationAsRead('123');

      expect(setNotificationRead).toHaveBeenCalledWith('123');
      expect(queryClient.setQueryData).toHaveBeenCalled();
    });

    it('should not proceed if user is not logged in', async () => {
      storage.getString.mockReturnValue(null);

      await markNotificationAsRead('123');

      expect(setNotificationRead).not.toHaveBeenCalled();
    });

    it('should handle API failure', async () => {
      storage.getString.mockReturnValue('valid-token');
      setNotificationRead.mockResolvedValue({ success: false });

      await markNotificationAsRead('123');

      expect(queryClient.setQueryData).not.toHaveBeenCalled();
    });
  });

  describe('setBadgeCount', () => {
    it('should set badge count', async () => {
      await setBadgeCount(5);
      expect(notifee.setBadgeCount).toHaveBeenCalledWith(5);
    });
  });

  describe('updateBadgeCount', () => {
    it('should update badge count by subtracting the given count', async () => {
      notifee.getBadgeCount.mockResolvedValue(10);
      await updateBadgeCount(3);
      expect(notifee.setBadgeCount).toHaveBeenCalledWith(7);
    });

    it('should not set negative badge count', async () => {
      notifee.getBadgeCount.mockResolvedValue(2);
      await updateBadgeCount(5);
      expect(notifee.setBadgeCount).toHaveBeenCalledWith(0);
    });
  });

  describe('getDeeplinkingScheme', () => {
    it('should convert URL to deep linking scheme', () => {
      const result = getDeeplinkingScheme('akinaconnect://path/to/content');
      expect(result).toBe(`${DEEP_LINKING_PREFIXES[process.env.ENV]}path/to/content`);
    });
  });

  describe('isEmptyObjectOrArray', () => {
    it('should return true for empty arrays', () => {
      expect(isEmptyObjectOrArray([])).toBe(true);
    });

    it('should return false for non-empty arrays', () => {
      expect(isEmptyObjectOrArray([1, 2, 3])).toBe(false);
    });

    it('should return true for empty objects', () => {
      expect(isEmptyObjectOrArray({})).toBe(true);
    });

    it('should return false for non-empty objects', () => {
      expect(isEmptyObjectOrArray({ key: 'value' })).toBe(false);
    });

    it('should return false for non-objects', () => {
      expect(isEmptyObjectOrArray('string')).toBe(false);
      expect(isEmptyObjectOrArray(null)).toBe(false);
    });
  });

  describe('mapUniversalLinkToDeepLink', () => {
    const mockMapping = {
      'news': { id: 1, title: 'News' },
      'videos': { id: 2, title: 'Videos' },
      'podcast': { id: 3, title: 'Podcast' },
      'ask-akina': { id: 4, title: 'Ask Akina' },
    };

    it('should map social connect post URLs', () => {
      const url = 'https://example.com/social-connect/post/123';
      const result = mapUniversalLinkToDeepLink(url, mockMapping);
      expect(result).toBe(`${DEEP_LINKING_PREFIXES[process.env.ENV]}social-connect-detail/123`);
    });

    it('should map user profile URLs', () => {
      const url = 'https://example.com/social-connect/user/456';
      const result = mapUniversalLinkToDeepLink(url, mockMapping);
      expect(result).toBe(`${DEEP_LINKING_PREFIXES[process.env.ENV]}user-profile/456`);
    });

    it('should map blog detail URLs', () => {
      const url = 'https://web-dev.akinaconnect.com/news/post/789';
      const result = mapUniversalLinkToDeepLink(url, mockMapping);
      expect(result).toBe(`${DEEP_LINKING_PREFIXES[process.env.ENV]}blog-detail/789/1/News`);
    });

    it('should return empty string for unmatched URLs', () => {
      const url = 'https://example.com/unknown/path';
      const result = mapUniversalLinkToDeepLink(url, mockMapping);
      expect(result).toBe('');
    });
  });

  describe('parseModules', () => {
    it('should parse modules into mapping object', () => {
      const modules = [
        { id: 1, slug: 'news', title: 'News' },
        { id: 2, slug: 'videos', title: 'Videos' },
      ];
      const result = parseModules(modules);
      expect(result).toEqual({
        'news': { id: 1, title: 'News' },
        'videos': { id: 2, title: 'Videos' },
      });
    });

    it('should handle undefined modules', () => {
      const result = parseModules(undefined);
      expect(result).toEqual({});
    });
  });

  describe('formatVideoSliderTime', () => {
    it('should format time correctly', () => {
      expect(formatVideoSliderTime(65)).toBe('01:05');
      expect(formatVideoSliderTime(600)).toBe('10:00');
      expect(formatVideoSliderTime(30)).toBe('00:30');
    });
  });

  describe('formatNotificationDate', () => {
    beforeEach(() => {
      // Mock the Date constructor to return a fixed date
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2023-01-15T12:00:00Z'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should return time for today', () => {
      const result = formatNotificationDate('2023-01-15T10:30:00Z');
      expect(result).toMatch(/\d{1,2}:\d{2} (AM|PM)/);
    });

    it('should return days for recent dates', () => {
      const result = formatNotificationDate('2023-01-12T10:30:00Z');
      expect(result).toBe('3d');
    });

    it('should return weeks for older dates', () => {
      const result = formatNotificationDate('2023-01-08T10:30:00Z');
      expect(result).toBe('1w');
    });

    it('should return month and day for very old dates', () => {
      const result = formatNotificationDate('2022-12-01T10:30:00Z');
      expect(result).toMatch(/[A-Z][a-z]{2} \d{2}/);
    });
  });

  describe('showBlockedPermissionAlert', () => {
    it('should show alert with permission name', () => {
      showBlockedPermissionAlert('Camera');
      expect(Alert.alert).toHaveBeenCalledWith(
        'Camera Permission Blocked',
        'Please enable Camera access in settings',
        expect.any(Array)
      );
    });
  });

  describe('openCamera', () => {
    it('should open camera when permission is granted', async () => {
      check.mockResolvedValue('granted');
      launchCamera.mockResolvedValue({
        assets: [{ uri: 'file://path/to/image.jpg' }]
      });

      const result = await openCamera('photo');
      expect(result).toBe('path/to/image.jpg');
    });

    it('should show alert when permission is denied', async () => {
      check.mockResolvedValue('denied');
      await openCamera('photo');
      expect(Alert.alert).toHaveBeenCalled();
    });

    it('should handle no assets returned', async () => {
      check.mockResolvedValue('granted');
      launchCamera.mockResolvedValue({ assets: null });

      const result = await openCamera('photo');
      expect(result).toBeUndefined();
    });
  });
});