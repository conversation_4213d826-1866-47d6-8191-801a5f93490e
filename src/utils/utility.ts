import { Option } from "@/services/users/getProfile";
import { TouchableOpacity as TouchableOpacityGH } from "react-native-gesture-handler";
import { Alert, Linking, Platform, TouchableOpacity } from "react-native";
import RNFS, { ReadDirItem, StatResult } from 'react-native-fs';
import { AUTH_STORRAGE_KEYS, CACHED_VIDEO_PREFIX, DEEP_LINKING_PREFIXES, MAX_CACHE_SIZE } from "./constants";
import { queryClient, storage } from "@/App";
import setNotificationRead from "@/services/notifications/setNotificationRead";
import notifee from '@notifee/react-native';
import { z } from "zod";
import { moduleSchema } from "@/types/schemas/module";
import { launchCamera, MediaType } from "react-native-image-picker";
import { check, PERMISSIONS, openSettings } from "react-native-permissions";

type ModulesMapping = Record<string, { id: number; title: string }>;
type RegexMapping = {
    pattern: RegExp;
    map: (match: RegExpMatchArray) => string;
};

export const isValidEmail = (email: string) => {
    let regex = new RegExp("([!#-'*+/-9=?A-Z^-~-]+(\.[!#-'*+/-9=?A-Z^-~-]+)*|\"\(\[\]!#-[^-~ \t]|(\\[\t -~]))+\")@([!#-'*+/-9=?A-Z^-~-]+(\.[!#-'*+/-9=?A-Z^-~-]+)*|\[[\t -Z^-~]*])");
    return regex.test(email)
}
export function getObjectNameFromModule(moduleName: string) {
    if (moduleName == 'news') {
        return 'news'
    } else if (moduleName == 'podcasts') {
        return 'podcast'
    } else if (moduleName == 'posts') {
        return 'post'
    } else if (moduleName == 'videos') {
        return 'video'
    } else {
        return 'music'
    }
}
export function getAIChatHistorySectionData(chatListing) {
    const data = chatListing || [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const sections = {
        today: [],
        yesterday: [],
        lastWeek: [],
        thisMonth: [],
        previous: [],
    };

    data.forEach(item => {
        const itemDate = new Date(item.created_at);
        const itemDateStart = new Date(itemDate);
        itemDateStart.setHours(0, 0, 0, 0);

        const diffDays = Math.floor((today - itemDateStart) / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            sections.today.push(item);
        } else if (diffDays === 1) {
            sections.yesterday.push(item);
        } else if (diffDays <= 6) {
            sections.lastWeek.push(item);
        } else if (diffDays <= 29) {
            sections.thisMonth.push(item);
        } else {
            sections.previous.push(item);
        }
    });

    const result = [];

    if (sections.today.length)
        result.push({ title: 'Today', data: sections.today });
    if (sections.yesterday.length)
        result.push({ title: 'Yesterday', data: sections.yesterday }); // Fixed: was sections.today
    if (sections.lastWeek.length)
        result.push({ title: 'Last Week', data: sections.lastWeek });
    if (sections.thisMonth.length)
        result.push({ title: 'This Month', data: sections.thisMonth });
    if (sections.previous.length)
        result.push({ title: 'Previous', data: sections.previous }); // Fixed: was 'Previious'

    return result;
}

export function getTimeBasedGreeting(): string {
    // Get current hour (in 24-hour format)
    const currentHour: number = new Date().getHours();
    
    // Determine appropriate greeting based on time of day
    let greeting: string;
    
    if (currentHour >= 6 && currentHour < 12) {
      greeting = "Good Morning";
    } else if (currentHour >= 12 && currentHour < 16) {
      greeting = "Good Afternoon";
    } else if (currentHour >= 16 && currentHour < 21) {
      greeting = "Good Evening";
    } else {
      greeting = "Good Night";
    }
    
    // Return the full greeting message
    return greeting
}
  
export const hexToRgbA = (hex: string, opacity: number) => {
    let c;
    if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
      c = hex.substring(1).split('');
      if (c.length === 3) {
        c = [c[0], c[0], c[1], c[1], c[2], c[2]];
      }
      c = `0x${c.join('')}`;
      return `rgba(${[(c >> 16) & 255, (c >> 8) & 255, c & 255].join(',')},${opacity})`;
    }
    throw new Error('Bad Hex');
};

export const timeSince = (date: Date): string => {
    const seconds = Math.floor((new Date().valueOf() - date.valueOf()) / 1000);

    if (seconds < 10) return 'Just Now';
    if (seconds < 60) return `${seconds} seconds ago`;

    const intervals = [
        { seconds: 31536000, label: 'year' },
        { seconds: 2592000, label: 'month' },
        { seconds: 86400, label: 'day' },
        { seconds: 3600, label: 'hour' },
        { seconds: 60, label: 'minute' },
    ];

    for (const { seconds: s, label } of intervals) {
        const interval = Math.floor(seconds / s);
        if (interval >= 1) {
            return `${interval} ${label}${interval > 1 ? 's' : ''} ago`;
        }
    }
};

export function secondToHHMMSS(duration: number) {
    var sec_num = duration //parseInt(seconds, 10); // don't forget the second param
    var hours   = Math.floor(sec_num / 3600).toFixed(0);
    var minutes = Math.floor((sec_num - (Number(hours) * 3600)) / 60).toFixed(0);
    var seconds = (sec_num - (Number(hours) * 3600) - (Number(minutes) * 60)).toFixed(0);

    if (Number(hours)   < 10) {hours   = "0"+hours;}
    if (Number(minutes) < 10) {minutes = "0"+minutes;}
    if (Number(seconds) < 10) {seconds = "0"+seconds;}
    return (hours == "00" ? '' : (hours + ':'))+minutes+':'+seconds;
}
export function getYoutubeID(url: string){
    let splittedURL = url.split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/);
    return (splittedURL[2] !== undefined) ? splittedURL[2].split(/[^0-9a-z_\-]/i)[0] : splittedURL[0];
 }
export const parseServerError = (data: any) => {
    if (data.status === 401 || data.status == 403) {
        return 'Un Authenticated!'
    }
    let error = ''
    if (typeof data === 'string') {
        error = data
    }
    if (data.message != null) {
        if (typeof data.message === 'string') {
            error= data.message
        }
    }
    if (data.errors != null) {
        if (typeof data.errors == 'object') {
            let keys = Object.keys(data.errors)
            if (keys.length > 0) {
                let errors = data.errors[keys[0]]
                if (errors.length > 0) {
                    error = errors[0]
                }
            }
        }
    }
    if (error == '') {
        error = 'Something went wrong'
    }
    return error
}
export function findSeletedOption(list?: Array<Option>) {
    let objectsFound = list?.filter(item => item.selected == true)
    if (objectsFound != null) {
        if (objectsFound.length > 0) {
            return objectsFound[0]
        } else {
            return {
                name: '',
                value: ''
            }
        }
    } else {
        return {
            name: '',
            value: ''
        }
    }
}

export function configureTouchaleOpacityDefaultSettings(): void {
    TouchableOpacityGH.defaultProps = {
      ...TouchableOpacity.defaultProps,
      activeOpacity: 0.7,
    };
    TouchableOpacity.defaultProps = {
        ...TouchableOpacity.defaultProps,
        activeOpacity: 0.7,
    };
};

async function removeCachedFile(): Promise<void>  {
    try {
        const files: ReadDirItem[] = await RNFS.readDir(RNFS.CachesDirectoryPath);
        const sortedFiles = files.sort((a, b) => a?.mtime - b?.mtime);
        const firstFile = sortedFiles.find(file => file.name.startsWith(CACHED_VIDEO_PREFIX))
        try {
            await RNFS.unlink(firstFile?.path);
        } catch (error) {
            console.log(error);
        }
    } catch (error) {
        console.log(error);
    }
};

async function getCacheDirectorySize(): Promise<number> {
    try {
        const files: ReadDirItem[] = await RNFS.readDir(RNFS.CachesDirectoryPath);
        let totalSize = 0;
        const filteredFiles = files.filter(file => file.name.startsWith(CACHED_VIDEO_PREFIX));
        const sortedFiles = filteredFiles.sort((a, b) => a?.mtime - b?.mtime);

        for (const file of sortedFiles) {
            const stats: StatResult = await RNFS.stat(file.path);
            console.log(file.name)
            totalSize += stats.size;
        }
  
        const sizeInMB = totalSize / (1024 * 1024);
        return sizeInMB;
    } catch (error) {
        console.log(error);
        return 0;
    }
};

async function downloadVideo(localPath: string, videoLink: string): Promise<void> {
    try {
        await RNFS.downloadFile({
            fromUrl: videoLink,
            toFile: localPath,
        });
        const size = await getCacheDirectorySize();
        if (size > MAX_CACHE_SIZE) {
            removeCachedFile();
        }
    } catch(e) {
        console.log(e);
    }
};

export async function checkVideoInCache(videoLink: string): Promise<string> {
    const fileName = videoLink.slice(8).replace(/\//g, '-');
    const localPath = `${RNFS.CachesDirectoryPath}/${CACHED_VIDEO_PREFIX}-${fileName}`;

    try {
        const exists = await RNFS.exists(localPath);
        if (exists) {
            return localPath;
        } else {
            downloadVideo(localPath, videoLink);
            return videoLink;
        }
    } catch (error) {
        console.log(error);
        return videoLink;
    }
};

export function isUserLoggedIn(): boolean {
    return !!storage.getString(AUTH_STORRAGE_KEYS.TOKEN);
}
export function isOnboardingShown(): boolean {
    return !!storage.getBoolean(AUTH_STORRAGE_KEYS.ONBOARDINGSHOWN);
}
export function isAIOnboardingShown(): boolean {
    return !!storage.getBoolean(AUTH_STORRAGE_KEYS.AIONBOARDINGSHOWN);
}

export function updateUnreadCountInProfile(count) {
    queryClient.setQueryData(['get_profile'], (cacheData: any) => {
        if (!cacheData) {
            return {}
        }
        const countDifference = cacheData?.user_profile?.notifications_count - count;
        const notifCount = countDifference < 0 ? 0 : countDifference;
        return {
            ...cacheData,
            user_profile: {
                ...cacheData.user_profile,
                notifications_count: notifCount,
            }
        }
    });
}

export async function markNotificationAsRead(id: string): Promise<void> {
    if (isUserLoggedIn()) {
        const response = await setNotificationRead(id);
        if (!response.success) {
            return;
        }
        updateUnreadCountInProfile(1);
        updateBadgeCount(1);
    }
}

export async function setBadgeCount(count: number) {
    await notifee.setBadgeCount(count);
}

export async function updateBadgeCount(count: number) {
    const currentCount = await notifee.getBadgeCount();
    const diff = currentCount - count;
    await notifee.setBadgeCount(diff < 0 ? 0 : diff);
}

export function getDeeplinkingScheme(url: string) {
    const path = url.split('://')[1];
    const scheme = DEEP_LINKING_PREFIXES[process.env.ENV];
    const updatedUrl = `${scheme}${path}`;
    return updatedUrl;
}

export function isEmptyObjectOrArray(obj: any[] | Record<string, any>): boolean {
    if (Array.isArray(obj)) {
        return obj.length === 0;
    } else if (obj && typeof obj === 'object') {
        return Object.keys(obj).length === 0;
    }
    return false;
}

export function mapUniversalLinkToDeepLink(url: string, mapping: ModulesMapping): string {
    const regexMappings: RegexMapping[] = [
        {
            pattern: /^https?:\/\/[^/]+\/social-connect\/post\/(\d+)/,
            map: (match) => `social-connect-detail/${match[1]}`,
        },
        {
            pattern: /^https?:\/\/[^/]+\/social-connect\/user\/(\d+)/,
            map: (match) => `user-profile/${match[1]}`,
        },
        {
            pattern: /^https:\/\/web-dev\.akinaconnect\.com\/([^\/]+)\/post\/(\d+)$/,
            map: (match) => `blog-detail/${match[2]}/${mapping[match[1]].id}/${mapping[match[1]].title}`,
        },
        {
            pattern: /^https?:\/\/[^/]+\/videos\/(\d+)/,
            map: (match) => `video-detail/${match[1]}/${mapping['videos'].id}`,
        },
        {
            pattern: /^https?:\/\/[^/]+\/podcast\/(\d+)/,
            map: (match) => `podcast/${match[1]}/${mapping['podcast'].id}`,
        },
        {
            pattern: /^https?:\/\/[^/]+\/ask-akina\/(\d+)/,
            map: (match) => `ask-akina/${match[1]}/${mapping['ask-akina'].id}`,
        },
    ];

    for (const { pattern, map } of regexMappings) {
        const match = url.match(pattern);
        if (match) {
            const deepLinkPath = map(match);
            return `${DEEP_LINKING_PREFIXES[process.env.ENV]}${deepLinkPath}`
        }
    }

    return '';
}

export function parseModules(modules: z.infer<typeof moduleSchema>[] | undefined): ModulesMapping {
    const modulesMapping: ModulesMapping = {};
    modules?.forEach(mod => {
        modulesMapping[mod.slug] = {
            id: mod.id,
            title: mod.title,
        };
    });
    return modulesMapping;
}

export function formatVideoSliderTime(seconds: number) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins < 10 ? '0' : ''}${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

export function formatNotificationDate(dateString: string) {
    const inputDate = new Date(dateString);
    const now = new Date();
  
    const isToday =
      inputDate.toDateString() === now.toDateString();
  
    const diffInMs = now - inputDate;
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
    if (isToday) {
        return inputDate.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        });
    } else if (diffInDays <= 6) {
        return `${diffInDays}d`;
    } else if (diffInDays <= 14) {
        const weeks = Math.floor(diffInDays / 7);
        return `${weeks}w`;
    } else {
        return inputDate.toLocaleDateString('en-US', {
            month: 'short',
            day: '2-digit',
        });
    }
};

export function showBlockedPermissionAlert(permissionName: string) {
    let options = [
        {
            text: 'Cancel',
        },
        {
            text: 'Open Settings',
            onPress: () => {
                if (Platform.OS === 'ios') {
                    Linking.openURL('app-settings:');
                } else {
                        openSettings().catch(() => {
                            Alert.alert('Error', 'Unable to open settings');
                        }
                    );
                }
            },
        },
    ]
    Alert.alert(`${permissionName} Permission Blocked`, `Please enable ${permissionName} access in settings`, options);
}

export async function openCamera(type: MediaType) {
    const permission = PERMISSIONS.IOS.CAMERA;
    const status = await check(permission);
    if (status != 'granted') {
        showBlockedPermissionAlert('Camera');
        return;
    }
    const result = await launchCamera({mediaType: type, quality: 0.1});
    if (result.assets != null && result.assets.length > 0) {
        const multimedia = result.assets[0]
        const mediaLink = Platform.OS === 'ios' ? multimedia?.uri?.replace('file://', '') : multimedia.uri
        return mediaLink;
    } 
}
