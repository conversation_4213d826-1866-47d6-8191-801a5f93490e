import { TextStyle } from 'react-native';
import type { FontColors, FontSizes } from '@/types/theme/fonts';
import type { UnionConfiguration } from '@/types/theme/config';
import { config, fontsValues } from '@/theme/_config';

export const generateFontColors = (configuration: UnionConfiguration) => {
	return Object.entries(configuration.fonts.colors ?? {}).reduce(
		(acc, [key, value]) => {
			return Object.assign(acc, {
				[`${key}`]: {
					color: value,
				},
			});
		},
		{} as FontColors,
	);
};

export const generateFontSizes = () => {
	return config.fonts.sizes.reduce((acc, size) => {
		return Object.assign(acc, {
			[`size_${size}`]: {
				fontSize: size,
			},
		});
	}, {} as FontSizes);
};

export const fontSizes = {
	fontSizes : {
		headings: {
			H1: {
				fontSize: 44
			},
			H2: {
				fontSize: 32
			},
			H3: {
				fontSize: 24
			},
			H4: {
				fontSize: 20
			},
			H5: {
				fontSize: 16
			},
			H6: {
				fontSize: 14
			},
		},
		body: {
			lg: {
				fontSize: 16
			},
			sm: {
				fontSize: 14
			},
			xs: {
				fontSize: 12
			},
		},
		utility: {
			lg: {
				fontSize: 16
			},
			sm: {
				fontSize: 14
			},
			xs: {
				fontSize: 12
			},
		}
	}
	
}  as const satisfies Record<string, Record<string, Record<string, TextStyle>>>;

export const lineHeight = {
	lineHeight : {
		headings: {
			H1: {
				lineHeight: 48
			},
			H2: {
				lineHeight: 40
			},
			H3: {
				lineHeight: 32
			},
			H4: {
				lineHeight: 24
			},
			H5: {
				lineHeight: 24
			},
			H6: {
				lineHeight: 20
			},
		},
		body: {
			lg: {
				lineHeight: 24
			},
			sm: {
				lineHeight: 22
			},
			xs: {
				lineHeight: 16
			},
		},
		utility: {
			lg: {
				lineHeight: 24
			},
			sm: {
				lineHeight: 20
			},
			xs: {
				lineHeight: 16
			},
		}
	}
	
}  as const satisfies Record<string, Record<string, Record<string, TextStyle>>>;

export const fontFamily = {
	headings: {
		fontFamily: fontsValues.regular
	},
	body: {
		fontFamily: fontsValues.regular
	},
	utility: {
		fontFamily: fontsValues.regular
	}
} as const satisfies Record<string, TextStyle>;

export const fontsWeightFamily = {
	Bold: {
		fontFamily: fontsValues.bold
	},
	SemiBold: {
		fontFamily: fontsValues.semibold
	},
	Medium: {
		fontFamily: fontsValues.medium
	},
	Regular: {
		fontFamily: fontsValues.regular
	}
} as const satisfies Record<string, TextStyle>;

export const staticFontStyles = {
	bold: {
		fontWeight: 'bold',
	},
	uppercase: {
		textTransform: 'uppercase',
	},
	capitalize: {
		textTransform: 'capitalize',
	},
	alignCenter: {
		textAlign: 'center',
	},
} as const satisfies Record<string, TextStyle>;
