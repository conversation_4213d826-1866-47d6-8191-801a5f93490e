import { config } from "./_config"

export function colorTokens() {
    const colorPrimitives = config.colors
    return {
    
        background: {
            default: {
                neutrals: {
                    default: colorPrimitives.white,
                    secondary: colorPrimitives.neutrals50,
                    tertiary: colorPrimitives.neutrals100
                },
                primary: {
                    default: colorPrimitives.primary50
                }
    
            },
            medium: {
                neutral: {
                    default: colorPrimitives.neutrals600,
                },
                primary: {
                    default: colorPrimitives.primary400
                }
            },
            bold: {
                neutral: {
                    default: colorPrimitives.neutrals900
                }
            }
        },
        fill: {
            default: {
                white: {
                    rest: colorPrimitives.white,
                    hover: colorPrimitives.neutrals50,
                    pressed: colorPrimitives.neutrals100,
                    disabled: colorPrimitives.neutrals200
                },
                primary: {
                    rest: colorPrimitives.primary50
                }
    
            },
            medium: {
                bold: {
                    rest: colorPrimitives.neutrals500,
                }
            },
            onBold: {
                neutral: {
                    rest: colorPrimitives.white
                },
                primary: {
                    restDefault: colorPrimitives.onBold100,
                    restSecondary: colorPrimitives.onBold300
                }
            },
            bold: {
                neutrals: {
                    rest: colorPrimitives.neutrals900
                },
                primary: {
                    rest: colorPrimitives.primary500,
                }
            }
        },
        content: {
            default: {
                default: colorPrimitives.neutrals800,
                emphasis: colorPrimitives.neutrals900,
                subdued: colorPrimitives.neutrals500,
                disabled: colorPrimitives.neutrals300
            },
            primary: {
                default: colorPrimitives.primary500
            },
            info: {
                default: colorPrimitives.infoBlue500
            },
            error: {
                default: colorPrimitives.primary600
            },
            success: {
                default: colorPrimitives.kellyGreen600
            },
            onBold: {
                default: {
                    default: colorPrimitives.onBold900,
                    emphasis: colorPrimitives.white,
                    subdued: colorPrimitives.onBold600,
                },
                success: {
                    default: colorPrimitives.kellyGreen300
                },
                error: {
                    default: colorPrimitives.primary200
                },
                info: {
                    default: colorPrimitives.infoBlue300
                }
            }
        },
        stoke: {
            default: {
                default: colorPrimitives.neutrals200,
                emphasis: colorPrimitives.neutrals400,
                subdued: colorPrimitives.neutrals50,
                
            },
            onBold: {
                neutral: {
                    default: colorPrimitives.onBold200,
                    subdued: colorPrimitives.onBold100,
                }
            },
            primary: {
                default: colorPrimitives.primary300
            },
            bold: {
                info: {
                    focussed: colorPrimitives.infoBlue500
                },
                error: {
                    default: colorPrimitives.primary600
                }
            }
        },
        custom: {
            white: colorPrimitives.white,
            black: colorPrimitives.black
        }
    }
}
