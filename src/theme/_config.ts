import { DarkTheme } from '@react-navigation/native';
import { Platform } from 'react-native';

import type { ThemeConfiguration } from '@/types/theme/config';


const colorsLight = {
	red500: '#C13333',
	gray800: '#303030',
	gray400: '#4D4D4D',
	gray200: '#A1A1A1',
	gray100: '#DFDFDF',
	gray50: '#EFEFEF',
	purple500: '#44427D',
	purple100: '#E1E1EF',
	purple50: '#1B1A23',
	green50: '#67AAB3',
	green300: '#1B9C5C',
	black: '#000000',
	purple: '#5357B6',
	musicBG: '#4A4B4B',
	musicCategoryTitleColor: '#FB0238',
	yellow: '#FBF59D',
	lightBlue: '#ACD3E5',
	orange: '#FF6C52',
	white: '#FFFFFF',
	primary50: '#FDE6E9',
	primary100: '#FACCD3',
	primary200: '#F599A7',
	primary300: '#F0667B',
	primary400: '#EB334F',
	primary500: '#E60023',
	primary600: '#B8001C',
	primary700: '#8A0015',
	primary800: '#5C000E',
	primary900: '#2E0007',
	neutrals50: '#F2F2F2',
	neutrals100: '#E6E6E6',
	neutrals200: '#CCCCCC',
	neutrals300: '#B3B3B3',
	neutrals400: '#999999',
	neutrals500: '#808080',
	neutrals600: '#666666',
	neutrals700: '#4D4D4D',
	neutrals800: '#333333',
	neutrals900: '#1A1A1A',
	sunshineYellow50: '#FFFEF5',
	sunshineYellow100: '#FFFDEA',
	sunshineYellow200: '#FFFBD6',
	sunshineYellow300: '#FFF8C1',
	sunshineYellow400: '#FFF6AD',
	sunshineYellow500: '#FFF498',
	sunshineYellow600: '#CCC37A',
	sunshineYellow700: '#99925B',
	sunshineYellow800: '#66623C',
	sunshineYellow900: '#33311E',
	kellyGreen50: '#E8F5EF',
	kellyGreen100: '#D1EBDE',
	kellyGreen200: '#A4D7BE',
	kellyGreen300: '#76C49D',
	kellyGreen400: '#49B07D',
	kellyGreen500: '#1B9C5C',
	kellyGreen600: '#167D4A',
	kellyGreen700: '#105E37',
	kellyGreen800: '#0B3E25',
	kellyGreen900: '#051F12',
	poppyRed50: '#FFE6EB',
	poppyRed100: '#FECCD7',
	poppyRed200: '#FD9AAF',
	poppyRed300: '#FD6788',
	poppyRed400: '#FC3560',
	poppyRed500: '#FB0238',
	poppyRed600: '#C9022D',
	poppyRed700: '#970122',
	poppyRed800: '#640116',
	poppyRed900: '#32000B',
	coralOrange50: '#FFF0EE',
	coralOrange100: '#FFE2DC',
	coralOrange200: '#FFC4BA',
	coralOrange300: '#FFA797',
	coralOrange400: '#FF8975',
	coralOrange500: '#FF6C52',
	coralOrange600: '#CC5642',
	coralOrange700: '#994131',
	coralOrange800: '#662B21',
	coralOrange900: '#331610',
	royalPurple50: '#EBEBF1',
	royalPurple100: '#D7D7E2',
	royalPurple200: '#AFAFC5',
	royalPurple300: '#8888A9',
	royalPurple400: '#60608C',
	royalPurple500: '#38386F',
	royalPurple600: '#2D2D59',
	royalPurple700: '#222243',
	royalPurple800: '#16162C',
	royalPurple900: '#0B0B16',
	infoBlue50: '#E8EFFF',
	infoBlue100: '#D0DEFE',
	infoBlue200: '#A2BEFD',
	infoBlue300: '#739EFD',
	infoBlue400: '#457DFC',
	infoBlue500: '#165DFB',
	infoBlue600: '#124AC9',
	infoBlue700: '#0D3897',
	infoBlue800: '#092564',
	infoBlue900: '#041332',
	onBold50: 'rgba(255,255,255,0.05)',
	onBold100: 'rgba(255,255,255,0.1)',
	onBold200: 'rgba(255,255,255,0.2)',
	onBold300: 'rgba(255,255,255,0.3)',
	onBold400: 'rgba(255,255,255,0.4)',
	onBold500: 'rgba(255,255,255,0.5)',
	onBold600: 'rgba(255,255,255,0.6)',
	onBold700: 'rgba(255,255,255,0.7)',
	onBold800: 'rgba(255,255,255,0.8)',
	onBold900: 'rgba(255,255,255,0.9)',


} as const;

const colorsDark = {
	red500: '#C13333',
	gray800: '#E0E0E0',
	gray400: '#969696',
	gray200: '#BABABA',
	gray100: '#000000',
	gray50: '#EFEFEF',
	purple500: '#A6A4F0',
	purple100: '#252732',
	purple50: '#1B1A23',
	green50: '#67AAB3',
	black: '#000000',
	purple: '#5357B6',
	musicBG: '#4A4B4B',
	musicCategoryTitleColor: '#FB0238',
	yellow: '#FBF59D',
	lightBlue: '#ACD3E5',
	orange: '#FF6C52',
	white: '#FFFFFF',
	white: '#FFFFFF',
	primary50: '#E8F2F2',
	primary100: '#D1E4E5',
	primary200: '#A3CACB',
	primary300: '#75AFB0',
	primary400: '#479596',
	primary500: '#197A7C',
	primary600: '#146263',
	primary700: '#0F494A',
	primary800: '#0A3132',
	primary900: '#051819',
	neutrals50: 'rgba(0,0,0,0.05)',
	neutrals100: 'rgba(0,0,0,0.1)',
	neutrals200: 'rgba(0,0,0,0.2)',
	neutrals300: 'rgba(0,0,0,0.3)',
	neutrals400: 'rgba(0,0,0,0.4)',
	neutrals500: 'rgba(0,0,0,0.5)',
	neutrals600: 'rgba(0,0,0,0.6)',
	neutrals700: 'rgba(0,0,0,0.7)',
	neutrals800: 'rgba(0,0,0,0.8)',
	neutrals900: 'rgba(0,0,0,0.9)',
	sunshineYellow50: '#FFFEF5',
	sunshineYellow100: '#FFFDEA',
	sunshineYellow200: '#FFFBD6',
	sunshineYellow300: '#FFF8C1',
	sunshineYellow400: '#FFF6AD',
	sunshineYellow500: '#FFF498',
	sunshineYellow600: '#CCC37A',
	sunshineYellow700: '#99925B',
	sunshineYellow800: '#66623C',
	sunshineYellow900: '#33311E',
	kellyGreen50: '#E8F5EF',
	kellyGreen100: '#D1EBDE',
	kellyGreen200: '#A4D7BE',
	kellyGreen300: '#76C49D',
	kellyGreen400: '#49B07D',
	kellyGreen500: '#1B9C5C',
	kellyGreen600: '#167D4A',
	kellyGreen700: '#105E37',
	kellyGreen800: '#0B3E25',
	kellyGreen900: '#051F12',
	poppyRed50: '#FFE6EB',
	poppyRed100: '#FECCD7',
	poppyRed200: '#FD9AAF',
	poppyRed300: '#FD6788',
	poppyRed400: '#FC3560',
	poppyRed500: '#FB0238',
	poppyRed600: '#C9022D',
	poppyRed700: '#970122',
	poppyRed800: '#640116',
	poppyRed900: '#32000B',
	coralOrange50: '#FFF0EE',
	coralOrange100: '#FFE2DC',
	coralOrange200: '#FFC4BA',
	coralOrange300: '#FFA797',
	coralOrange400: '#FF8975',
	coralOrange500: '#FF6C52',
	coralOrange600: '#CC5642',
	coralOrange700: '#994131',
	coralOrange800: '#662B21',
	coralOrange900: '#331610',
	royalPurple50: '#EBEBF1',
	royalPurple100: '#D7D7E2',
	royalPurple200: '#AFAFC5',
	royalPurple300: '#8888A9',
	royalPurple400: '#60608C',
	royalPurple500: '#38386F',
	royalPurple600: '#2D2D59',
	royalPurple700: '#222243',
	royalPurple800: '#16162C',
	royalPurple900: '#0B0B16',
	infoBlue50: '#E8EFFF',
	infoBlue100: '#D0DEFE',
	infoBlue200: '#A2BEFD',
	infoBlue300: '#739EFD',
	infoBlue400: '#457DFC',
	infoBlue500: '#165DFB',
	infoBlue600: '#124AC9',
	infoBlue700: '#0D3897',
	infoBlue800: '#092564',
	infoBlue900: '#041332',
	onBold50: 'rgba(255,255,255,0.05)',
	onBold100: 'rgba(255,255,255,0.1)',
	onBold200: 'rgba(255,255,255,0.2)',
	onBold300: 'rgba(255,255,255,0.3)',
	onBold400: 'rgba(255,255,255,0.4)',
	onBold500: 'rgba(255,255,255,0.5)',
	onBold600: 'rgba(255,255,255,0.6)',
	onBold700: 'rgba(255,255,255,0.7)',
	onBold800: 'rgba(255,255,255,0.8)',
	onBold900: 'rgba(255,255,255,1)',

} as const;


const sizes = [0, 4, 6, 8, 12, 16, 20, 24, 32, 40, 80] as const;

export const config = {
	colors: colorsLight,
	fonts: {
		sizes,
		colors: colorsLight,
	},
	gutters: sizes,
	backgrounds: colorsLight,
	borders: {
		widths: [1, 2],
		radius: [4, 8, 12, 16, 24, 32, 40, 50],
		colors: colorsLight,
	},
	navigationColors: {
		...DarkTheme.colors,
		background: colorsLight.gray50,
		card: colorsLight.gray50,
	},
	variants: {
		dark: {
			colors: colorsDark,
			fonts: {
				colors: colorsDark,
			},
			backgrounds: colorsDark,
			navigationColors: {
				...DarkTheme.colors,
				background: colorsDark.purple50,
				card: colorsDark.purple50,
			},
		},
	},
} as const satisfies ThemeConfiguration;

export const fontsValues = {
	regular: Platform.OS === 'ios' ? 'SF Pro Text Regular' : 'SFProTextRegular',
	medium: Platform.OS === 'ios' ? 'SF Pro Text Medium' : 'SFProTextMedium',
	semibold: Platform.OS === 'ios' ? 'SF Pro Text Semibold' : 'SFProTextSemibold',
	bold: Platform.OS === 'ios' ? 'SF Pro Text Bold' : 'SFProTextBold',
};
