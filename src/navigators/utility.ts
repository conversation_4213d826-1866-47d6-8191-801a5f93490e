import { storage } from "@/App";
import { DEEPLINK_CACHE_KEY } from "@/utils/constants";

export function cachePendingDeeplink(url: string) {
    storage.set(DEEPLINK_CACHE_KEY, url);
}

export function getAndClearPendingDeeplink(): string | undefined {
    const url = storage.getString(DEEPLINK_CACHE_KEY);
    if (url) {
        storage.delete(DEEPLINK_CACHE_KEY);
    }
    return url;
}

export function clearPendingDeeplink() {
    storage.delete(DEEPLINK_CACHE_KEY);
}
