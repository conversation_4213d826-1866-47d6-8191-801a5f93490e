import { CardStyleInterpolators, createStackNavigator } from '@react-navigation/stack';
import { NavigationContainer, useNavigation } from '@react-navigation/native';

import { Account, Auth, Billings, Favorites, Home, Startup, VerificationCode, SideMenu, AKSideMenu, 
	ResetPassword, LoginSuccess, PersonalInfo, BlogListing, BlogDetail, EmpowerHerListing, PlaceListing,
	Saved, MusicListing, MusicDetail, PodcastListing, PlaceDetail, OnBoardingSignUp } from '@/screens';
import { useTheme } from '@/theme';

import type { ApplicationStackParamList, OnBoardingStackParamList } from '@/types/navigation';
import { createDrawerNavigator, DrawerNavigationProp } from '@react-navigation/drawer';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import HeaderLogo from "@/theme/assets/images/HeaderLogo.png"
import MenuIcon from "@/theme/assets/images/MenuIcon.png"
import NotificationIcon from "@/theme/assets/images/NotificationIcon.png"
import NavBarLogo from "@/theme/assets/images/NavBarLogo.png"
import CrossIconNav from "@/theme/assets/images/CrossIconNav.png"

import { ImageVariant } from '@/components/atoms';
import BasicInfo from '@/screens/OnBoarding/BasicInfo/BasicInfo';
import AkinaInfo from '@/screens/OnBoarding/AkinaInfo/AkinaInfo';
import { TermsAndConditions } from '@/screens/OnBoarding';
import { AddMultimedia, AddText, ChangePassword, EventDetail, LocationEvents, LocationListing, 
	NewsDetail, NewsListing, NewsSourceListing, NotificationPreference, PodcastDetail, PrivacyPolicy, 
	Search, SocialConnect, VideoDetail, VideoListing, AkinaAI, ChatListing, Chat, Profile, UserListing, 
	Notifications,
	AKWebView,
	SocialHome,
	AddSocialPost,
	CameraRoll,
	UserProfile,
	Members,
	MembersSearch,
	BlockedAccounts,
	Settings,
	AccountSettings,
	ManageNotifications, ChatHome,
	Referrals,
	Groups,
	NewGroup} from '@/screens/SideTabs';
import { linking } from './linking';
import SocialConnectDetail from '@/screens/SideTabs/Home/SocialConnect/SocialConnectDetail/SocialConnectDetail';
import { NavigationRef } from "./NavigationRef";
import { Text, TouchableOpacity } from 'react-native';
import SocialConnectPostDetail from '@/screens/SideTabs/Home/SocialConnect/SocialConnectPostDetail/SocialConnectPostDetail';
import VideoPreview from '@/screens/SideTabs/Home/SocialConnect/VideoPreview/VideoPreview';
import NotificationsListing from '@/screens/SideTabs/Notifications/NotificationsListing';
import { colorTokens } from '@/theme/colorTokens';
import { fontsWeightFamily } from '@/theme/fonts';
import { useEffect } from 'react';
import { storage } from '@/App';
import { REFERRAL_CODE_STORAGE_KEY } from '@/utils/constants';
import { NewLogin } from '@/screens/Auth';

const Stack = createStackNavigator<ApplicationStackParamList>();
const Drawer = createDrawerNavigator();
const Tab = createBottomTabNavigator();
const OnBoardingStack = createStackNavigator<OnBoardingStackParamList>()
type DrawerParamList = {
	SocialHome: undefined;
	Favorites: undefined;
	AskAkina: undefined;
	Saved: undefined;
	Billings: undefined;
	Account: undefined;
  };
  
type DrawerNavigationProps = DrawerNavigationProp<DrawerParamList>;
  
function CustomDrawerButton() {
	const navigation = useNavigation<DrawerNavigationProps>();
	
	return (
		<TouchableOpacity 
			onPress={() => navigation.toggleDrawer()}
			style={{ marginLeft: 10 }}
		>
			<ImageVariant  source={MenuIcon} style={{height: 32, width: 32, resizeMode: 'contain'}}/>
		</TouchableOpacity>
	);
}

function HeaderRightButton() {
	const navigation = useNavigation<DrawerNavigationProps>();
	
	return (
		<TouchableOpacity 
			onPress={() => NavigationRef.navigate('NotificationsListing')}
			style={{ marginRight: 10 }}
		>
			<ImageVariant  source={NotificationIcon} style={{height: 32, width: 32, resizeMode: 'contain'}}/>
		</TouchableOpacity>
	);
}


function ApplicationNavigator() {
	const { variant, navigationTheme, colors, fonts } = useTheme();  
	const tokenColors = colorTokens()
	function DrawerComponent(){
		return(
			<Drawer.Navigator 
				screenOptions={{
					headerShown: false,
					drawerStyle: { width: 272 }
				}}
				initialRouteName='TabBar' 
				drawerContent={() => <AKSideMenu/>}>
				<Drawer.Screen name='SocialHome' component={SocialHome}/>
			</Drawer.Navigator>
		)
		
	}
	function OnBoardingNavigator() {
		return (
				<OnBoardingStack.Navigator key={variant} screenOptions={{ headerShown: false }}>
					<OnBoardingStack.Screen name="PersonalInfo" component={PersonalInfo}/>
					<OnBoardingStack.Screen name="BasicInfo" component={BasicInfo}/>
					<OnBoardingStack.Screen name="AkinaInfo" component={AkinaInfo}/>
				</OnBoardingStack.Navigator>
		)
	}
	
	
	return (
		<NavigationContainer ref={NavigationRef} theme={navigationTheme} linking={linking}>
			<Stack.Navigator key={variant} screenOptions={{headerShown: false, headerBackTitleVisible: false, headerStyle: {backgroundColor: colors.white}}}>
				<Stack.Screen name="Startup" component={Startup} />
				<Stack.Screen name="Auth" component={Auth} />
				<Stack.Screen name='VerificationCode' component={VerificationCode}/>
				<Stack.Screen name="Main" component={DrawerComponent}/>
				<Stack.Screen name='ResetPassword' component={ResetPassword}/>
				<Stack.Screen name='LoginSuccess' component={LoginSuccess}/>
				<Stack.Screen name='OnBoardingSignUp' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true, presentation: 'modal' }} component={OnBoardingSignUp}/>
				<Stack.Screen name='OnBoarding' options={{presentation: 'modal'}} component={OnBoardingNavigator}/>
				<Stack.Screen name="TermsAndConditions" options={{presentation: 'modal'}} component={TermsAndConditions}/>
				<Stack.Screen name='PrivacyPolicy' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true, presentation: 'modal' }} component={PrivacyPolicy} />
				<Stack.Screen name='NotificationPreference' component={NotificationPreference} options={{headerShown: true, headerTintColor: colors.gray800, headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='BlogListing' component={BlogListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='BlogDetail' component={BlogDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='EmpowerHerListing' component={EmpowerHerListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='PlaceListing' component={PlaceListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='MusicListing' component={MusicListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='MusicDetail' component={MusicDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='PodcastListing' component={PodcastListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='PlaceDetail' component={PlaceDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='VideoListing' component={VideoListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='NewsSourceListing' component={NewsSourceListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='VideoDetail' component={VideoDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='NewsListing' component={NewsListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='NewsDetail' component={NewsDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='LocationListing' component={LocationListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='LocationEvents' component={LocationEvents} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='EventDetail' component={EventDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='PodcastDetail' component={PodcastDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='ChangePassword' component={ChangePassword} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='Search' component={Search} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='Saved' component={Saved} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='Favorites' component={Favorites} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='Account' component={Account} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='SocialConnect' component={SocialConnect} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='SocialConnectDetail' component={SocialConnectDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='SocialConnectPostDetail' component={SocialConnectPostDetail} options={{headerShown: true, headerTintColor: colors.gray800, headerStyle: { backgroundColor: 'white' }, headerTitleAlign: 'center', headerTitle: () => <Text style={[fonts.fontSizes.body.sm, fonts.lineHeight.body.sm, fonts.bold]}>Post</Text>}}/>
				<Stack.Screen name='AddText' component={AddText} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='AddMultimedia' component={AddMultimedia} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='AkinaAI' component={AkinaAI} options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, headerShown: false }}/>
				<Stack.Screen name='ChatListing' component={ChatListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='Chat' component={Chat} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='Profile' component={Profile} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='UserListing' component={UserListing} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default} source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='Notifications' component={Notifications} options={{headerShown: true, headerTintColor: colors.gray800, headerTitleAlign: 'center', headerTitle: () => <ImageVariant tintColor={tokenColors.content.primary.default}  source={NavBarLogo} style={{height: 25, resizeMode: 'contain'}}/>}}/>
				<Stack.Screen name='NotificationsListing' component={NotificationsListing} />
				<Stack.Screen name='AKWebView' options={{presentation: 'modal'}} component={AKWebView} />
				<Stack.Screen name='AddSocialPost' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true, presentation: 'modal' }} component={AddSocialPost}/>
				<Stack.Screen name="VideoPreview" component={VideoPreview} />
				<Stack.Screen name='CameraRoll' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true }} component={CameraRoll}/>
				<Stack.Screen name='UserProfile' component={UserProfile} />
				<Stack.Screen name='Members' component={Members} />
				<Stack.Screen name='ChatHome' component={ChatHome} />
				<Stack.Screen name='MembersSearch' component={MembersSearch} />
				<Stack.Screen name='BlockedAccounts' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true, presentation: 'modal' }} component={BlockedAccounts} />
				<Stack.Screen name='Settings' component={Settings} />
				<Stack.Screen name='AccountSettings' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true, presentation: 'modal' }} component={AccountSettings} />
				<Stack.Screen name='ManageNotifications' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true, presentation: 'modal' }} component={ManageNotifications} />
				<Stack.Screen name='NewLogin' component={NewLogin} />
				<Stack.Screen name='Referrals' component={Referrals} />
				<Stack.Screen name='Groups' component={Groups} />
				<Stack.Screen name='NewGroup' options={{cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS, gestureDirection: 'vertical', gestureEnabled: true, presentation: 'modal' }} component={NewGroup} />
			</Stack.Navigator>
		</NavigationContainer>
	);
}

export default ApplicationNavigator;
