import { DEEP_LINKING_PREFIXES } from "@/utils/constants";
import { getDeeplinkingScheme, isUserLoggedIn, mapUniversalLinkToDeepLink, 
    markNotificationAsRead, parseModules } from "@/utils/utility";
import messaging from '@react-native-firebase/messaging';
import { Linking } from "react-native";
import modules from '@/services/home/<USER>';
import { queryClient } from "@/App";
import { cachePendingDeeplink, clearPendingDeeplink } from "./utility";

type LinkingConfig = {
    prefixes: string[];
    config: {
        initialRouteName: string;
        screens: Record<string, string>;
    };
    getInitialURL: () => Promise<string | null | undefined>;
    subscribe: (listener: (url: string) => void) => () => void;
};

type EnvType = 'development' | 'staging' | 'production';
const env = process.env.ENV as EnvType

export const linking: LinkingConfig = {
    prefixes: [DEEP_LINKING_PREFIXES[env]],
    config: {
        initialRouteName: 'Main',
        screens: {
            BlogDetail: 'blog-detail/:id/:moduleId/:name', // https://web-dev.akinaconnect.com/mental-health-wellness/post/63
            VideoDetail: 'video-detail/:id/:moduleId', // https://web-dev.akinaconnect.com/videos/23
            NewsDetail: 'news-detail/:id/:moduleId/:name/:news_source_id?',
            SocialConnectPostDetail: 'social-connect-detail/:id/:showComments?', // https://web-dev.akinaconnect.com/social-connect/post/108
            UserProfile: 'user-profile/:userId', // https://web-dev.akinaconnect.com/social-connect/user/133
            PodcastDetail: 'podcast/:id/:moduleId', // https://web-dev.akinaconnect.com/podcast/1113103
            Chat: 'ask-akina/:id/:moduleId', // https://web-dev.akinaconnect.com/ask-akina/272
        },
    },
    async getInitialURL(): Promise<string | null | undefined> {
        const message = await messaging().getInitialNotification();
        let { url, notification_id: notificationId, comment_id: commentId } = message?.data || {};
        if (message) {
            markNotificationAsRead(notificationId as string);
        }
        if (isUserLoggedIn() && url) {
            if (commentId) {
                url = url + '/true'
            }
            return(getDeeplinkingScheme(url as string));
        }
    
        const universalUrl = await Linking.getInitialURL();

        if (universalUrl) {
            if (isUserLoggedIn()) {
                const response = await modules();
                const modulesMapping = parseModules(response?.modules);
                const processedUrl = mapUniversalLinkToDeepLink(universalUrl, modulesMapping);
                return processedUrl;
            } else {
                cachePendingDeeplink(universalUrl);
                return null;
            }
        }
        clearPendingDeeplink();
        return null;
    },
    subscribe(listener: (url: string) => void): () => void {	  
        const unsubscribeNotification = messaging().onNotificationOpenedApp(
            message => {
                let { url, notification_id: notificationId, comment_id: commentId } = message?.data || {};
                markNotificationAsRead(notificationId as string);
                if (isUserLoggedIn() && url) {
                    if (commentId) {
                        url = url + '/true'
                    }
                    listener(getDeeplinkingScheme(url as string));
                }
            },
        );

        const linkingSubscription = Linking.addEventListener('url', ({ url }) => {
            if (isUserLoggedIn() && url) {
                let deeplinkUrl;
                if (url.startsWith('https')) {
                    const cachedModules = queryClient.getQueryData(['modules']);
                    const modulesMapping = parseModules(cachedModules?.modules);
                    const processedUrl = mapUniversalLinkToDeepLink(url, modulesMapping);
                    deeplinkUrl = processedUrl;
                } else {
                    deeplinkUrl = url;
                }
                if (deeplinkUrl) {
                    listener(getDeeplinkingScheme(deeplinkUrl));
                }
            }
        });

        return () => {
            unsubscribeNotification();
            linkingSubscription.remove();
        };
    },
};
