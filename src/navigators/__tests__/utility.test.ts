import { storage } from "@/App";
import { DEEPLINK_CACHE_KEY } from "@/utils/constants";
import { 
  cachePendingDeeplink, 
  getAndClearPendingDeeplink, 
  clearPendingDeeplink 
} from "../utility";

// Mock the storage module
jest.mock("@/App", () => ({
  storage: {
    set: jest.fn(),
    getString: jest.fn(),
    delete: jest.fn()
  }
}));

describe("Navigator Utility Functions", () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe("cachePendingDeeplink", () => {
    it("should store the URL in storage with the correct key", () => {
      const testUrl = "akinaconnect://test/url";
      
      cachePendingDeeplink(testUrl);
      
      expect(storage.set).toHaveBeenCalledWith(DEEPLINK_CACHE_KEY, testUrl);
      expect(storage.set).toHaveBeenCalledTimes(1);
    });

    it("should handle empty URL", () => {
      cachePendingDeeplink("");
      
      expect(storage.set).toHaveBeenCalledWith(DEEPLINK_CACHE_KEY, "");
    });
  });

  describe("getAndClearPendingDeeplink", () => {
    it("should return the stored URL and clear it from storage", () => {
      const testUrl = "akinaconnect://test/url";
      (storage.getString as jest.Mock).mockReturnValue(testUrl);
      
      const result = getAndClearPendingDeeplink();
      
      expect(result).toBe(testUrl);
      expect(storage.getString).toHaveBeenCalledWith(DEEPLINK_CACHE_KEY);
      expect(storage.delete).toHaveBeenCalledWith(DEEPLINK_CACHE_KEY);
    });

    it("should not delete from storage if no URL is stored", () => {
      (storage.getString as jest.Mock).mockReturnValue(null);
      
      const result = getAndClearPendingDeeplink();
      
      expect(result).toBeNull();
      expect(storage.getString).toHaveBeenCalledWith(DEEPLINK_CACHE_KEY);
      expect(storage.delete).not.toHaveBeenCalled();
    });
  });

  describe("clearPendingDeeplink", () => {
    it("should delete the URL from storage", () => {
      clearPendingDeeplink();
      
      expect(storage.delete).toHaveBeenCalledWith(DEEPLINK_CACHE_KEY);
      expect(storage.delete).toHaveBeenCalledTimes(1);
    });
  });
});