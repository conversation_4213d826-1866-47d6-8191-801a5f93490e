import messaging from '@react-native-firebase/messaging';
import notifee, { EventType } from '@notifee/react-native';
import { Linking, Platform } from 'react-native';
import { getDeeplinkingScheme, markNotificationAsRead, setBadgeCount, updateUnreadCountInProfile } from '@/utils/utility';
import RNPermissions from 'react-native-permissions';

type NotificationObject = {
    title?: string;
    body?: string;
    data: {
        commentId?: string;
        url?: string;
        notificationId?: string;
    };
    android?: {
        channelId: string;
        smallIcon?: string;
        pressAction: {
            id: string;
        };
    };
};

export async function requestNotificationPermissions(): Promise<void> {
    let notificationPermissionStatus;
    if (Platform.OS === 'ios') {
        notificationPermissionStatus = await messaging().requestPermission();
    } else {
        try {
            notificationPermissionStatus = await RNPermissions.requestNotifications();
        } catch (error) {
            console.log('Error requesting notification permissions:', error);
        }
    }
    console.log('Authorization status:', notificationPermissionStatus);
}

export async function getFcmToken(): Promise<string> {
    const fcmToken = (await messaging()?.getToken()) || '';
    return fcmToken;
}

export async function revokeFcmToken(): Promise<void> {
    await messaging()?.deleteToken();
}

export const registerNotificationForegroundHandler = async () => {
    messaging()
        .onMessage(async remoteMessage => {
            const { body, title } = remoteMessage.notification || {};
            const { url, notification_id: notificationId, unread_count: unreadCount, comment_id: commentId } = remoteMessage?.data || {};
            const notificationObject: NotificationObject = {
                title,
                body,
                // data: {
                //     commentId: (commentId || '') as string | undefined
                // },
            };
            if (commentId) {
                notificationObject.data = {
                    ...notificationObject.data,
                    commentId: commentId as string,
                }
            }
            if (url) {
                notificationObject.data = {
                    ...notificationObject.data,
                    url: url as string,
                }
            }
            if (notificationId) {
                notificationObject.data = {
                    ...notificationObject.data,
                    notificationId: notificationId as string,
                }
            }

            // Create a channel (required for Android)
            const channelId = await notifee.createChannel({
                id: 'default',
                name: 'Default Channel',
            });
            notificationObject.android = {
                channelId,
                // smallIcon: 'name-of-a-small-icon', // optional, defaults to 'ic_launcher'.
                // pressAction is needed if you want the notification to open the app when pressed
                pressAction: {
                    id: 'default',
                },
            };
            // console.log('Notification Object::', notificationObject)
            const notifId = await notifee.displayNotification(notificationObject);
            // console.log('Notification ID::', notifId)
            updateUnreadCountInProfile(-1);
            setBadgeCount(+unreadCount);
            notifee.onForegroundEvent(({ type, detail }) => {
                if (type === EventType.PRESS && detail.notification?.id === notifId) {
                    const { notificationId } = detail.notification?.data || {};
                    if (notificationId) {
                        markNotificationAsRead(notificationId as string);
                    }
                    let notificationUrl = detail.notification?.data?.url as string;
                    if (notificationUrl) {
                        if (detail.notification?.data?.commentId) {
                            notificationUrl = notificationUrl + '/true';
                        }
                        Linking.openURL(getDeeplinkingScheme(notificationUrl));
                    }
                }
            });
        });
};
