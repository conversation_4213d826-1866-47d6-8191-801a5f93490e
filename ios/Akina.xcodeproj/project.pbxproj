// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* AkinaTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* AkinaTests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		5421B73A2D40E277000DF040 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 5421B7342D40E277000DF040 /* GoogleService-Info.plist */; };
		5421B73B2D40E277000DF040 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 5421B7322D40E277000DF040 /* GoogleService-Info.plist */; };
		5444FC972E24F65B001139AB /* SFProTextBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = D9E46BF545B24A5F90C0CB06 /* SFProTextBold.otf */; };
		5444FC982E2504A6001139AB /* SFProTextMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = E959607E159F43849C3214D0 /* SFProTextMedium.otf */; };
		5444FC992E2504A9001139AB /* SFProTextSemibold.otf in Resources */ = {isa = PBXBuildFile; fileRef = C0AECBD6B73A41129805060C /* SFProTextSemibold.otf */; };
		5444FC9A2E2504AF001139AB /* SFProTextRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = D1C34E5A7C024F57A62952B3 /* SFProTextRegular.otf */; };
		5444FC9B2E265DB7001139AB /* SFProTextRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = D1C34E5A7C024F57A62952B3 /* SFProTextRegular.otf */; };
		5444FC9C2E265DBB001139AB /* SFProTextBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = D9E46BF545B24A5F90C0CB06 /* SFProTextBold.otf */; };
		5444FC9D2E265DBE001139AB /* SFProTextMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = E959607E159F43849C3214D0 /* SFProTextMedium.otf */; };
		5444FC9E2E265DC1001139AB /* SFProTextSemibold.otf in Resources */ = {isa = PBXBuildFile; fileRef = C0AECBD6B73A41129805060C /* SFProTextSemibold.otf */; };
		59E0A76326CD42578CA33BB8 /* SFProTextMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = E959607E159F43849C3214D0 /* SFProTextMedium.otf */; };
		619A79C32BA311E600ACFEA9 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		619A79C42BA311E600ACFEA9 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		619A79C82BA311E600ACFEA9 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		619A79C92BA311E600ACFEA9 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		619A79D62BA3122600ACFEA9 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		619A79D72BA3122600ACFEA9 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		619A79DB2BA3122600ACFEA9 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		619A79DC2BA3122600ACFEA9 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		6BBFB9B8CD9E4E1391F0DC04 /* SFProTextBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = D9E46BF545B24A5F90C0CB06 /* SFProTextBold.otf */; };
		6FFA6F0FD39618E4342B9C53 /* Pods_AkinaCommonPods_Akina.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EDC5E732F51F141EB73D1A9C /* Pods_AkinaCommonPods_Akina.framework */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		88769A8DE88C09CF8ED19C74 /* Pods_AkinaCommonPods_AkinaStg.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 246B8F529376114BF1925E4B /* Pods_AkinaCommonPods_AkinaStg.framework */; };
		922070191E1F10C44FA11568 /* Pods_AkinaCommonPods_AkinaTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 02E55AF7F3C55615C97076E6 /* Pods_AkinaCommonPods_AkinaTests.framework */; };
		9BD1C699436E03CE268A095D /* Pods_AkinaCommonPods_AkinaDev.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CE97892908503B0A7BA65990 /* Pods_AkinaCommonPods_AkinaDev.framework */; };
		BEBCC238D2CA44709E6F425C /* SFProTextSemibold.otf in Resources */ = {isa = PBXBuildFile; fileRef = C0AECBD6B73A41129805060C /* SFProTextSemibold.otf */; };
		DBFD5EBCFCE649E5A562A5F5 /* SFProTextRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = D1C34E5A7C024F57A62952B3 /* SFProTextRegular.otf */; };
		F5DE22122D63819400245309 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = F5DE22102D63819400245309 /* GoogleService-Info.plist */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = Akina;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		54F3BA2C2D1030A2001F57E1 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* AkinaTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AkinaTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* AkinaTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AkinaTests.m; sourceTree = "<group>"; };
		02E55AF7F3C55615C97076E6 /* Pods_AkinaCommonPods_AkinaTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_AkinaCommonPods_AkinaTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		03E321CF3F9ECA4B8BB78E5A /* Pods-AkinaCommonPods-AkinaStg.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-AkinaStg.release.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg.release.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Akina.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Akina.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = Akina/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = Akina/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Akina/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Akina/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = Akina/main.m; sourceTree = "<group>"; };
		246B8F529376114BF1925E4B /* Pods_AkinaCommonPods_AkinaStg.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_AkinaCommonPods_AkinaStg.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		36484D12C2485D296320F789 /* Pods-OneSignalNotificationServiceExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtension.debug.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtension/Pods-OneSignalNotificationServiceExtension.debug.xcconfig"; sourceTree = "<group>"; };
		3B4392A12AC88292D35C810B /* Pods-Akina.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Akina.debug.xcconfig"; path = "Target Support Files/Pods-Akina/Pods-Akina.debug.xcconfig"; sourceTree = "<group>"; };
		459BC0B3E7474F6E948FB887 /* Pods-AkinaCommonPods-Akina.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-Akina.release.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina.release.xcconfig"; sourceTree = "<group>"; };
		5152601631842295A70C530B /* Pods_OneSignalNotificationServiceExtension.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_OneSignalNotificationServiceExtension.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5421B7322D40E277000DF040 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		5421B7342D40E277000DF040 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		542B6A822D2568C000CEDA90 /* AkinaDev.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = AkinaDev.entitlements; sourceTree = "<group>"; };
		542B6A832D2568E900CEDA90 /* AkinaStg.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = AkinaStg.entitlements; sourceTree = "<group>"; };
		54F3BA1F2D0FC20A001F57E1 /* Akina.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = Akina.entitlements; path = Akina/Akina.entitlements; sourceTree = "<group>"; };
		5709B34CF0A7D63546082F79 /* Pods-Akina.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Akina.release.xcconfig"; path = "Target Support Files/Pods-Akina/Pods-Akina.release.xcconfig"; sourceTree = "<group>"; };
		5B7EB9410499542E8C5724F5 /* Pods-Akina-AkinaTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Akina-AkinaTests.debug.xcconfig"; path = "Target Support Files/Pods-Akina-AkinaTests/Pods-Akina-AkinaTests.debug.xcconfig"; sourceTree = "<group>"; };
		619A79D02BA311E600ACFEA9 /* AkinaDev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AkinaDev.app; sourceTree = BUILT_PRODUCTS_DIR; };
		619A79E32BA3122600ACFEA9 /* AkinaStg.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AkinaStg.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6A60A24C54C80EF976890AE0 /* Pods-AkinaCommonPods-Akina.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-Akina.debug.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina.debug.xcconfig"; sourceTree = "<group>"; };
		6C9342EF441E611A20EF6858 /* Pods-OneSignalNotificationServiceExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtension.release.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtension/Pods-OneSignalNotificationServiceExtension.release.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = Akina/LaunchScreen.storyboard; sourceTree = "<group>"; };
		84A2AA05C79494E81430C247 /* Pods-AkinaCommonPods-AkinaDev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-AkinaDev.debug.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev.debug.xcconfig"; sourceTree = "<group>"; };
		8747C353E02907F43C1F1CD3 /* Pods-AkinaCommonPods-AkinaDev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-AkinaDev.release.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev.release.xcconfig"; sourceTree = "<group>"; };
		89C6BE57DB24E9ADA2F236DE /* Pods-Akina-AkinaTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Akina-AkinaTests.release.xcconfig"; path = "Target Support Files/Pods-Akina-AkinaTests/Pods-Akina-AkinaTests.release.xcconfig"; sourceTree = "<group>"; };
		9B1AE428E2D78D230C93248F /* Pods-AkinaCommonPods-AkinaTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-AkinaTests.release.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests.release.xcconfig"; sourceTree = "<group>"; };
		A6749A7A5DC98B8D528B27C9 /* Pods-AkinaCommonPods-AkinaTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-AkinaTests.debug.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests.debug.xcconfig"; sourceTree = "<group>"; };
		C0AECBD6B73A41129805060C /* SFProTextSemibold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SFProTextSemibold.otf; path = ../src/theme/assets/fonts/SFProTextSemibold.otf; sourceTree = "<group>"; };
		CE97892908503B0A7BA65990 /* Pods_AkinaCommonPods_AkinaDev.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_AkinaCommonPods_AkinaDev.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D1C34E5A7C024F57A62952B3 /* SFProTextRegular.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SFProTextRegular.otf; path = ../src/theme/assets/fonts/SFProTextRegular.otf; sourceTree = "<group>"; };
		D70621A927531F60F994F3C1 /* Pods-AkinaCommonPods-AkinaStg.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AkinaCommonPods-AkinaStg.debug.xcconfig"; path = "Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg.debug.xcconfig"; sourceTree = "<group>"; };
		D9E46BF545B24A5F90C0CB06 /* SFProTextBold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SFProTextBold.otf; path = ../src/theme/assets/fonts/SFProTextBold.otf; sourceTree = "<group>"; };
		E959607E159F43849C3214D0 /* SFProTextMedium.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SFProTextMedium.otf; path = ../src/theme/assets/fonts/SFProTextMedium.otf; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		EDC5E732F51F141EB73D1A9C /* Pods_AkinaCommonPods_Akina.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_AkinaCommonPods_Akina.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F509EBE22C8B123000647CD5 /* hermes.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = hermes.xcframework; path = "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework"; sourceTree = "<group>"; };
		F509EBE42C91DC8500647CD5 /* Akina dev-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Akina dev-Info.plist"; sourceTree = "<group>"; };
		F509EBE52C91DC8500647CD5 /* Akina stg-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Akina stg-Info.plist"; sourceTree = "<group>"; };
		F5DE22102D63819400245309 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				922070191E1F10C44FA11568 /* Pods_AkinaCommonPods_AkinaTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6FFA6F0FD39618E4342B9C53 /* Pods_AkinaCommonPods_Akina.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		619A79C52BA311E600ACFEA9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9BD1C699436E03CE268A095D /* Pods_AkinaCommonPods_AkinaDev.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		619A79D82BA3122600ACFEA9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				88769A8DE88C09CF8ED19C74 /* Pods_AkinaCommonPods_AkinaStg.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* AkinaTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* AkinaTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = AkinaTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* Akina */ = {
			isa = PBXGroup;
			children = (
				5421B7362D40E277000DF040 /* GoogleServices */,
				54F3BA1F2D0FC20A001F57E1 /* Akina.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = Akina;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F509EBE22C8B123000647CD5 /* hermes.xcframework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				EDC5E732F51F141EB73D1A9C /* Pods_AkinaCommonPods_Akina.framework */,
				CE97892908503B0A7BA65990 /* Pods_AkinaCommonPods_AkinaDev.framework */,
				246B8F529376114BF1925E4B /* Pods_AkinaCommonPods_AkinaStg.framework */,
				02E55AF7F3C55615C97076E6 /* Pods_AkinaCommonPods_AkinaTests.framework */,
				5152601631842295A70C530B /* Pods_OneSignalNotificationServiceExtension.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		5421B7332D40E277000DF040 /* Development */ = {
			isa = PBXGroup;
			children = (
				5421B7322D40E277000DF040 /* GoogleService-Info.plist */,
			);
			path = Development;
			sourceTree = "<group>";
		};
		5421B7352D40E277000DF040 /* Production */ = {
			isa = PBXGroup;
			children = (
				5421B7342D40E277000DF040 /* GoogleService-Info.plist */,
			);
			path = Production;
			sourceTree = "<group>";
		};
		5421B7362D40E277000DF040 /* GoogleServices */ = {
			isa = PBXGroup;
			children = (
				F5DE22112D63819400245309 /* Staging */,
				5421B7332D40E277000DF040 /* Development */,
				5421B7352D40E277000DF040 /* Production */,
			);
			path = GoogleServices;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				542B6A832D2568E900CEDA90 /* AkinaStg.entitlements */,
				542B6A822D2568C000CEDA90 /* AkinaDev.entitlements */,
				F509EBE42C91DC8500647CD5 /* Akina dev-Info.plist */,
				F509EBE52C91DC8500647CD5 /* Akina stg-Info.plist */,
				13B07FAE1A68108700A75B9A /* Akina */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* AkinaTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				9311885F20DA4798B8A2206B /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Akina.app */,
				00E356EE1AD99517003FC87E /* AkinaTests.xctest */,
				619A79D02BA311E600ACFEA9 /* AkinaDev.app */,
				619A79E32BA3122600ACFEA9 /* AkinaStg.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9311885F20DA4798B8A2206B /* Resources */ = {
			isa = PBXGroup;
			children = (
				D1C34E5A7C024F57A62952B3 /* SFProTextRegular.otf */,
				D9E46BF545B24A5F90C0CB06 /* SFProTextBold.otf */,
				E959607E159F43849C3214D0 /* SFProTextMedium.otf */,
				C0AECBD6B73A41129805060C /* SFProTextSemibold.otf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B4392A12AC88292D35C810B /* Pods-Akina.debug.xcconfig */,
				5709B34CF0A7D63546082F79 /* Pods-Akina.release.xcconfig */,
				5B7EB9410499542E8C5724F5 /* Pods-Akina-AkinaTests.debug.xcconfig */,
				89C6BE57DB24E9ADA2F236DE /* Pods-Akina-AkinaTests.release.xcconfig */,
				6A60A24C54C80EF976890AE0 /* Pods-AkinaCommonPods-Akina.debug.xcconfig */,
				459BC0B3E7474F6E948FB887 /* Pods-AkinaCommonPods-Akina.release.xcconfig */,
				84A2AA05C79494E81430C247 /* Pods-AkinaCommonPods-AkinaDev.debug.xcconfig */,
				8747C353E02907F43C1F1CD3 /* Pods-AkinaCommonPods-AkinaDev.release.xcconfig */,
				D70621A927531F60F994F3C1 /* Pods-AkinaCommonPods-AkinaStg.debug.xcconfig */,
				03E321CF3F9ECA4B8BB78E5A /* Pods-AkinaCommonPods-AkinaStg.release.xcconfig */,
				A6749A7A5DC98B8D528B27C9 /* Pods-AkinaCommonPods-AkinaTests.debug.xcconfig */,
				9B1AE428E2D78D230C93248F /* Pods-AkinaCommonPods-AkinaTests.release.xcconfig */,
				36484D12C2485D296320F789 /* Pods-OneSignalNotificationServiceExtension.debug.xcconfig */,
				6C9342EF441E611A20EF6858 /* Pods-OneSignalNotificationServiceExtension.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F5DE22112D63819400245309 /* Staging */ = {
			isa = PBXGroup;
			children = (
				F5DE22102D63819400245309 /* GoogleService-Info.plist */,
			);
			path = Staging;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* AkinaTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "AkinaTests" */;
			buildPhases = (
				A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				C59DA0FBD6956966B86A3779 /* [CP] Embed Pods Frameworks */,
				F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = AkinaTests;
			productName = AkinaTests;
			productReference = 00E356EE1AD99517003FC87E /* AkinaTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* Akina */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Akina" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
				54F3BA2C2D1030A2001F57E1 /* Embed Foundation Extensions */,
				2E86C44E427B4835B8E1A06B /* Upload Debug Symbols to Sentry */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Akina;
			productName = Akina;
			productReference = 13B07F961A680F5B00A75B9A /* Akina.app */;
			productType = "com.apple.product-type.application";
		};
		619A79BF2BA311E600ACFEA9 /* AkinaDev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 619A79CD2BA311E600ACFEA9 /* Build configuration list for PBXNativeTarget "AkinaDev" */;
			buildPhases = (
				619A79C02BA311E600ACFEA9 /* [CP] Check Pods Manifest.lock */,
				619A79C12BA311E600ACFEA9 /* Start Packager */,
				619A79C22BA311E600ACFEA9 /* Sources */,
				619A79C52BA311E600ACFEA9 /* Frameworks */,
				619A79C72BA311E600ACFEA9 /* Resources */,
				619A79CA2BA311E600ACFEA9 /* Bundle React Native code and images */,
				619A79CB2BA311E600ACFEA9 /* [CP] Embed Pods Frameworks */,
				619A79CC2BA311E600ACFEA9 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AkinaDev;
			productName = Akina;
			productReference = 619A79D02BA311E600ACFEA9 /* AkinaDev.app */;
			productType = "com.apple.product-type.application";
		};
		619A79D22BA3122600ACFEA9 /* AkinaStg */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 619A79E02BA3122600ACFEA9 /* Build configuration list for PBXNativeTarget "AkinaStg" */;
			buildPhases = (
				619A79D32BA3122600ACFEA9 /* [CP] Check Pods Manifest.lock */,
				619A79D42BA3122600ACFEA9 /* Start Packager */,
				619A79D52BA3122600ACFEA9 /* Sources */,
				619A79D82BA3122600ACFEA9 /* Frameworks */,
				619A79DA2BA3122600ACFEA9 /* Resources */,
				619A79DD2BA3122600ACFEA9 /* Bundle React Native code and images */,
				619A79DE2BA3122600ACFEA9 /* [CP] Embed Pods Frameworks */,
				619A79DF2BA3122600ACFEA9 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AkinaStg;
			productName = Akina;
			productReference = 619A79E32BA3122600ACFEA9 /* AkinaStg.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Akina" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			preferredProjectObjectVersion = 77;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Akina */,
				00E356ED1AD99517003FC87E /* AkinaTests */,
				619A79BF2BA311E600ACFEA9 /* AkinaDev */,
				619A79D22BA3122600ACFEA9 /* AkinaStg */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				5421B73A2D40E277000DF040 /* GoogleService-Info.plist in Resources */,
				DBFD5EBCFCE649E5A562A5F5 /* SFProTextRegular.otf in Resources */,
				6BBFB9B8CD9E4E1391F0DC04 /* SFProTextBold.otf in Resources */,
				59E0A76326CD42578CA33BB8 /* SFProTextMedium.otf in Resources */,
				BEBCC238D2CA44709E6F425C /* SFProTextSemibold.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		619A79C72BA311E600ACFEA9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				619A79C82BA311E600ACFEA9 /* LaunchScreen.storyboard in Resources */,
				619A79C92BA311E600ACFEA9 /* Images.xcassets in Resources */,
				5421B73B2D40E277000DF040 /* GoogleService-Info.plist in Resources */,
				5444FC9B2E265DB7001139AB /* SFProTextRegular.otf in Resources */,
				5444FC9C2E265DBB001139AB /* SFProTextBold.otf in Resources */,
				5444FC9D2E265DBE001139AB /* SFProTextMedium.otf in Resources */,
				5444FC9E2E265DC1001139AB /* SFProTextSemibold.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		619A79DA2BA3122600ACFEA9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5444FC972E24F65B001139AB /* SFProTextBold.otf in Resources */,
				5444FC982E2504A6001139AB /* SFProTextMedium.otf in Resources */,
				5444FC992E2504A9001139AB /* SFProTextSemibold.otf in Resources */,
				5444FC9A2E2504AF001139AB /* SFProTextRegular.otf in Resources */,
				619A79DB2BA3122600ACFEA9 /* LaunchScreen.storyboard in Resources */,
				619A79DC2BA3122600ACFEA9 /* Images.xcassets in Resources */,
				F5DE22122D63819400245309 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2E86C44E427B4835B8E1A06B /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh";
		};
		619A79C02BA311E600ACFEA9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AkinaCommonPods-AkinaDev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		619A79C12BA311E600ACFEA9 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		619A79CA2BA311E600ACFEA9 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		619A79CB2BA311E600ACFEA9 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		619A79CC2BA311E600ACFEA9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaDev/Pods-AkinaCommonPods-AkinaDev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		619A79D32BA3122600ACFEA9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AkinaCommonPods-AkinaStg-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		619A79D42BA3122600ACFEA9 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		619A79DD2BA3122600ACFEA9 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		619A79DE2BA3122600ACFEA9 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		619A79DF2BA3122600ACFEA9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaStg/Pods-AkinaCommonPods-AkinaStg-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AkinaCommonPods-AkinaTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AkinaCommonPods-Akina-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C59DA0FBD6956966B86A3779 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-Akina/Pods-AkinaCommonPods-Akina-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AkinaCommonPods-AkinaTests/Pods-AkinaCommonPods-AkinaTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E356F31AD99517003FC87E /* AkinaTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		619A79C22BA311E600ACFEA9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				619A79C32BA311E600ACFEA9 /* AppDelegate.mm in Sources */,
				619A79C42BA311E600ACFEA9 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		619A79D52BA3122600ACFEA9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				619A79D62BA3122600ACFEA9 /* AppDelegate.mm in Sources */,
				619A79D72BA3122600ACFEA9 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* Akina */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A6749A7A5DC98B8D528B27C9 /* Pods-AkinaCommonPods-AkinaTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = AkinaTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Akina.app/Akina";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9B1AE428E2D78D230C93248F /* Pods-AkinaCommonPods-AkinaTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = AkinaTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Akina.app/Akina";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6A60A24C54C80EF976890AE0 /* Pods-AkinaCommonPods-Akina.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Akina/Akina.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWG485KTZL;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Akina/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Akina;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.akinatechnologies.akinaapp;
				PRODUCT_NAME = Akina;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 459BC0B3E7474F6E948FB887 /* Pods-AkinaCommonPods-Akina.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Akina/Akina.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWG485KTZL;
				INFOPLIST_FILE = Akina/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Akina;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.akinatechnologies.akinaapp;
				PRODUCT_NAME = Akina;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		619A79CE2BA311E600ACFEA9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 84A2AA05C79494E81430C247 /* Pods-AkinaCommonPods-AkinaDev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AkinaDev.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWG485KTZL;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "Akina dev-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = AkinaDev;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.akina.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		619A79CF2BA311E600ACFEA9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8747C353E02907F43C1F1CD3 /* Pods-AkinaCommonPods-AkinaDev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AkinaDev.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWG485KTZL;
				INFOPLIST_FILE = "Akina dev-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = AkinaDev;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.akina.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		619A79E12BA3122600ACFEA9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D70621A927531F60F994F3C1 /* Pods-AkinaCommonPods-AkinaStg.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AkinaStg.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWG485KTZL;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "Akina stg-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = AkinaStg;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.akina.staging;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		619A79E22BA3122600ACFEA9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 03E321CF3F9ECA4B8BB78E5A /* Pods-AkinaCommonPods-AkinaStg.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AkinaStg.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWG485KTZL;
				INFOPLIST_FILE = "Akina stg-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = AkinaStg;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.akina.staging;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					" ",
				);
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					" ",
				);
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "AkinaTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Akina" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		619A79CD2BA311E600ACFEA9 /* Build configuration list for PBXNativeTarget "AkinaDev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				619A79CE2BA311E600ACFEA9 /* Debug */,
				619A79CF2BA311E600ACFEA9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		619A79E02BA3122600ACFEA9 /* Build configuration list for PBXNativeTarget "AkinaStg" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				619A79E12BA3122600ACFEA9 /* Debug */,
				619A79E22BA3122600ACFEA9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Akina" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
