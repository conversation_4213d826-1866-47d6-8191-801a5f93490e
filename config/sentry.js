import Config from "react-native-config";
import { SENTRY_CONFIG as DEV_CONFIG } from "./sentry.development";
import { SENTRY_CONFIG as STAGING_CONFIG } from "./sentry.staging";
import { SENTRY_CONFIG as PROD_CONFIG } from "./sentry.production";

const getEnvironment = () => {
  if (__DEV__) return "development";

  // Use react-native-config to get build configuration
  const buildType = Config.BUILD_TYPE || "production";
  return buildType.toLowerCase();
};

const getSentryConfig = () => {
  const environment = getEnvironment();

  switch (environment) {
    case "development":
      return DEV_CONFIG;
    case "staging":
      return STAGING_CONFIG;
    case "production":
    default:
      return PROD_CONFIG;
  }
};

export const SENTRY_CONFIG = getSentryConfig();
